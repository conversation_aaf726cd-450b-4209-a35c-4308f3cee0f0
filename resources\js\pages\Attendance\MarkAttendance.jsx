import React, { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
    Clock,
    User,
    Calendar,
    AlertTriangle,
    CheckCircle,
    XCircle
} from 'lucide-react';

export default function MarkAttendance({ auth, session }) {
    const { data, setData, post, processing, errors } = useForm({
        session_id: session.id,
        attendance_status: 'present',
        check_in_time: new Date().toISOString().slice(0, 16),
        late_reason: '',
        absence_reason: '',
        is_excused: false,
        excuse_reason: '',
        marking_method: 'detailed'
    });

    const [showLateReason, setShowLateReason] = useState(false);
    const [showAbsenceReason, setShowAbsenceReason] = useState(false);

    const handleStatusChange = (status) => {
        setData('attendance_status', status);
        setShowLateReason(status === 'late');
        setShowAbsenceReason(status === 'absent');
        
        if (status !== 'late') {
            setData('late_reason', '');
        }
        if (status !== 'absent') {
            setData('absence_reason', '');
            setData('is_excused', false);
            setData('excuse_reason', '');
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/attendance/mark', {
            onSuccess: () => {
                router.get('/attendance/dashboard');
            }
        });
    };

    const isLate = () => {
        const scheduledTime = new Date(`${session.scheduled_date} ${session.scheduled_time}`);
        const checkInTime = new Date(data.check_in_time);
        return checkInTime > scheduledTime;
    };

    const calculateLateMinutes = () => {
        if (!isLate()) return 0;
        const scheduledTime = new Date(`${session.scheduled_date} ${session.scheduled_time}`);
        const checkInTime = new Date(data.check_in_time);
        return Math.floor((checkInTime - scheduledTime) / (1000 * 60));
    };

    return (
        <AppLayout>
            <Head title="Mark Attendance" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8 flex justify-between items-center">
                        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                            Mark Attendance
                        </h2>
                        <button
                            onClick={() => router.get('/attendance/dashboard')}
                            className="text-gray-600 hover:text-gray-900"
                        >
                            ← Back to Dashboard
                        </button>
                    </div>
                </div>
            </div>

            <div className="py-6">
                <div className="max-w-3xl mx-auto sm:px-6 lg:px-8">
                    {/* Session Info Card */}
                    <div className="bg-white shadow rounded-lg mb-6">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Session Details</h3>
                        </div>
                        <div className="px-6 py-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex items-center space-x-3">
                                    <User className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Patient</p>
                                        <p className="text-sm text-gray-600">{session.patient.name}</p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <User className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Doctor</p>
                                        <p className="text-sm text-gray-600">Dr. {session.doctor.name}</p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Scheduled Time</p>
                                        <p className="text-sm text-gray-600">
                                            {new Date(session.scheduled_date).toLocaleDateString()} at {session.scheduled_time}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Clock className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Duration</p>
                                        <p className="text-sm text-gray-600">{session.duration_minutes} minutes</p>
                                    </div>
                                </div>
                                {session.location && (
                                    <div className="flex items-center space-x-3">
                                        <div className="h-5 w-5 text-gray-400">📍</div>
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Location</p>
                                            <p className="text-sm text-gray-600">{session.location}</p>
                                        </div>
                                    </div>
                                )}
                                <div className="flex items-center space-x-3">
                                    <div className="h-5 w-5 text-gray-400">📦</div>
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Package</p>
                                        <p className="text-sm text-gray-600">{session.patient_package.therapy_package.name}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Attendance Form */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Mark Attendance</h3>
                        </div>
                        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
                            {/* Check-in Time */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Check-in Time
                                </label>
                                <input
                                    type="datetime-local"
                                    value={data.check_in_time}
                                    onChange={(e) => setData('check_in_time', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                                {isLate() && (
                                    <p className="mt-1 text-sm text-yellow-600 flex items-center">
                                        <AlertTriangle className="h-4 w-4 mr-1" />
                                        Patient is {calculateLateMinutes()} minutes late
                                    </p>
                                )}
                                {errors.check_in_time && (
                                    <p className="mt-1 text-sm text-red-600">{errors.check_in_time}</p>
                                )}
                            </div>

                            {/* Attendance Status */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Attendance Status
                                </label>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                                    <button
                                        type="button"
                                        onClick={() => handleStatusChange('present')}
                                        className={`flex items-center justify-center px-4 py-3 border rounded-md text-sm font-medium ${
                                            data.attendance_status === 'present'
                                                ? 'border-green-500 bg-green-50 text-green-700'
                                                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                    >
                                        <CheckCircle className="h-5 w-5 mr-2" />
                                        Present
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => handleStatusChange('late')}
                                        className={`flex items-center justify-center px-4 py-3 border rounded-md text-sm font-medium ${
                                            data.attendance_status === 'late'
                                                ? 'border-yellow-500 bg-yellow-50 text-yellow-700'
                                                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                    >
                                        <Clock className="h-5 w-5 mr-2" />
                                        Late
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => handleStatusChange('absent')}
                                        className={`flex items-center justify-center px-4 py-3 border rounded-md text-sm font-medium ${
                                            data.attendance_status === 'absent'
                                                ? 'border-red-500 bg-red-50 text-red-700'
                                                : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                        }`}
                                    >
                                        <XCircle className="h-5 w-5 mr-2" />
                                        Absent
                                    </button>
                                </div>
                                {errors.attendance_status && (
                                    <p className="mt-1 text-sm text-red-600">{errors.attendance_status}</p>
                                )}
                            </div>

                            {/* Late Reason */}
                            {showLateReason && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Reason for Being Late
                                    </label>
                                    <textarea
                                        value={data.late_reason}
                                        onChange={(e) => setData('late_reason', e.target.value)}
                                        rows={3}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        placeholder="Optional: Explain why the patient was late..."
                                    />
                                    {errors.late_reason && (
                                        <p className="mt-1 text-sm text-red-600">{errors.late_reason}</p>
                                    )}
                                </div>
                            )}

                            {/* Absence Reason */}
                            {showAbsenceReason && (
                                <div className="space-y-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Reason for Absence
                                        </label>
                                        <textarea
                                            value={data.absence_reason}
                                            onChange={(e) => setData('absence_reason', e.target.value)}
                                            rows={3}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            placeholder="Explain why the patient was absent..."
                                        />
                                        {errors.absence_reason && (
                                            <p className="mt-1 text-sm text-red-600">{errors.absence_reason}</p>
                                        )}
                                    </div>

                                    <div className="flex items-start">
                                        <div className="flex items-center h-5">
                                            <input
                                                type="checkbox"
                                                checked={data.is_excused}
                                                onChange={(e) => setData('is_excused', e.target.checked)}
                                                className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                                            />
                                        </div>
                                        <div className="ml-3 text-sm">
                                            <label className="font-medium text-gray-700">
                                                Excused Absence
                                            </label>
                                            <p className="text-gray-500">
                                                Check if this absence is excused (medical emergency, prior approval, etc.)
                                            </p>
                                        </div>
                                    </div>

                                    {data.is_excused && (
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Excuse Reason
                                            </label>
                                            <textarea
                                                value={data.excuse_reason}
                                                onChange={(e) => setData('excuse_reason', e.target.value)}
                                                rows={2}
                                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                placeholder="Provide details about the excuse..."
                                            />
                                            {errors.excuse_reason && (
                                                <p className="mt-1 text-sm text-red-600">{errors.excuse_reason}</p>
                                            )}
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Submit Buttons */}
                            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                                <button
                                    type="button"
                                    onClick={() => router.get('/attendance/dashboard')}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {processing ? 'Saving...' : 'Mark Attendance'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
