<?php

namespace App\Repositories;

use App\Contracts\AppointmentRepositoryInterface;
use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class AppointmentRepository implements AppointmentRepositoryInterface
{
    public function all()
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])
            ->latest('appointment_date')
            ->paginate($perPage);
    }

    public function find(int $id): ?Appointment
    {
        return Appointment::with([
            'patient:id,name,email,phone',
            'doctor:id,name,email,phone',
            'doctor.doctorProfile:user_id,specialization,consultation_fee',
            'branch:id,name,address,phone',
            'medicalRecord'
        ])->find($id);
    }

    public function create(array $data): Appointment
    {
        return Appointment::create($data);
    }

    public function update(int $id, array $data): Appointment
    {
        $appointment = Appointment::findOrFail($id);
        $appointment->update($data);
        return $appointment->fresh();
    }

    public function delete(int $id): bool
    {
        return Appointment::destroy($id) > 0;
    }

    public function getByDoctor(int $doctorId)
    {
        return Appointment::where('doctor_id', $doctorId)
            ->with(['patient', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByPatient(int $patientId)
    {
        return Appointment::where('patient_id', $patientId)
            ->with(['doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByBranch(int $branchId)
    {
        return Appointment::where('branch_id', $branchId)
            ->with(['patient', 'doctor'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getTodaysAppointments()
    {
        return Appointment::whereDate('appointment_date', today())
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getUpcomingAppointments(int $limit = 10)
    {
        return Appointment::with([
            'patient:id,name,email,phone',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])
        ->where('appointment_date', '>', now())
        ->where('status', 'scheduled')
        ->orderBy('appointment_date')
        ->limit($limit)
        ->get();
    }

    public function getByStatus(string $status)
    {
        return Appointment::where('status', $status)
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByDateRange(Carbon $start, Carbon $end)
    {
        return Appointment::whereBetween('appointment_date', [$start, $end])
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function checkConflict(int $doctorId, Carbon $dateTime, int $duration)
    {
        $endTime = $dateTime->copy()->addMinutes($duration);

        return Appointment::where('doctor_id', $doctorId)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($dateTime, $endTime) {
                $query->whereBetween('appointment_date', [$dateTime, $endTime])
                    ->orWhere(function ($q) use ($dateTime, $endTime) {
                        $q->where('appointment_date', '<=', $dateTime)
                          ->whereRaw('DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) > ?', [$dateTime]);
                    });
            })
            ->exists();
    }

    public function checkConflictExcluding(int $doctorId, Carbon $dateTime, int $duration, int $excludeAppointmentId)
    {
        $endTime = $dateTime->copy()->addMinutes($duration);

        return Appointment::where('doctor_id', $doctorId)
            ->where('id', '!=', $excludeAppointmentId)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($dateTime, $endTime) {
                $query->whereBetween('appointment_date', [$dateTime, $endTime])
                    ->orWhere(function ($q) use ($dateTime, $endTime) {
                        $q->where('appointment_date', '<=', $dateTime)
                          ->whereRaw('DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) > ?', [$dateTime]);
                    });
            })
            ->exists();
    }

    public function getForUser($user = null)
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])
        ->forUser($user)
        ->latest('appointment_date')
        ->get();
    }

    public function getDoctorAppointments(int $doctorId, Carbon $startDate, Carbon $endDate)
    {
        return Appointment::with([
            'patient:id,name,email,phone',
            'branch:id,name'
        ])
        ->where('doctor_id', $doctorId)
        ->whereBetween('appointment_date', [$startDate, $endDate])
        ->orderBy('appointment_date')
        ->get();
    }

    public function getPatientAppointments(int $patientId, int $limit = 20)
    {
        return Appointment::with([
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization,consultation_fee',
            'branch:id,name,address',
            'medicalRecord:id,appointment_id,diagnosis,treatment_plan'
        ])
        ->where('patient_id', $patientId)
        ->orderBy('appointment_date', 'desc')
        ->limit($limit)
        ->get();
    }

    public function getBranchAppointments(int $branchId, Carbon $date)
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization'
        ])
        ->where('branch_id', $branchId)
        ->whereDate('appointment_date', $date)
        ->orderBy('appointment_date')
        ->get();
    }

    public function getAppointmentStats(int $branchId = null, Carbon $startDate = null, Carbon $endDate = null)
    {
        $query = Appointment::query();

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        if ($startDate && $endDate) {
            $query->whereBetween('appointment_date', [$startDate, $endDate]);
        }

        return $query->selectRaw('
            COUNT(*) as total_appointments,
            COUNT(CASE WHEN status = "completed" THEN 1 END) as completed_appointments,
            COUNT(CASE WHEN status = "cancelled" THEN 1 END) as cancelled_appointments,
            COUNT(CASE WHEN status = "no_show" THEN 1 END) as no_show_appointments,
            AVG(duration_minutes) as avg_duration
        ')->first();
    }

    public function getDoctorAppointmentsForDate(int $doctorId, string $date)
    {
        return Appointment::where('doctor_id', $doctorId)
            ->whereDate('appointment_date', $date)
            ->where('status', '!=', 'cancelled')
            ->orderBy('appointment_date')
            ->get();
    }
}