<?php

namespace App\Repositories;

use App\Contracts\AppointmentRepositoryInterface;
use App\Models\Appointment;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class AppointmentRepository implements AppointmentRepositoryInterface
{
    public function all()
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return Appointment::with([
            'patient:id,name,email',
            'doctor:id,name,email',
            'doctor.doctorProfile:user_id,specialization',
            'branch:id,name'
        ])
            ->latest('appointment_date')
            ->paginate($perPage);
    }

    public function find(int $id): ?Appointment
    {
        return Appointment::with(['patient', 'doctor', 'branch'])->find($id);
    }

    public function create(array $data): Appointment
    {
        return Appointment::create($data);
    }

    public function update(int $id, array $data): Appointment
    {
        $appointment = Appointment::findOrFail($id);
        $appointment->update($data);
        return $appointment->fresh();
    }

    public function delete(int $id): bool
    {
        return Appointment::destroy($id) > 0;
    }

    public function getByDoctor(int $doctorId)
    {
        return Appointment::where('doctor_id', $doctorId)
            ->with(['patient', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByPatient(int $patientId)
    {
        return Appointment::where('patient_id', $patientId)
            ->with(['doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByBranch(int $branchId)
    {
        return Appointment::where('branch_id', $branchId)
            ->with(['patient', 'doctor'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getTodaysAppointments()
    {
        return Appointment::whereDate('appointment_date', today())
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getUpcomingAppointments()
    {
        return Appointment::where('appointment_date', '>', now())
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByStatus(string $status)
    {
        return Appointment::where('status', $status)
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function getByDateRange(Carbon $start, Carbon $end)
    {
        return Appointment::whereBetween('appointment_date', [$start, $end])
            ->with(['patient', 'doctor', 'branch'])
            ->orderBy('appointment_date')
            ->get();
    }

    public function checkConflict(int $doctorId, Carbon $dateTime, int $duration)
    {
        $endTime = $dateTime->copy()->addMinutes($duration);

        return Appointment::where('doctor_id', $doctorId)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($dateTime, $endTime) {
                $query->whereBetween('appointment_date', [$dateTime, $endTime])
                    ->orWhere(function ($q) use ($dateTime, $endTime) {
                        $q->where('appointment_date', '<=', $dateTime)
                          ->whereRaw('DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) > ?', [$dateTime]);
                    });
            })
            ->exists();
    }

    public function checkConflictExcluding(int $doctorId, Carbon $dateTime, int $duration, int $excludeAppointmentId)
    {
        $endTime = $dateTime->copy()->addMinutes($duration);

        return Appointment::where('doctor_id', $doctorId)
            ->where('id', '!=', $excludeAppointmentId)
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($dateTime, $endTime) {
                $query->whereBetween('appointment_date', [$dateTime, $endTime])
                    ->orWhere(function ($q) use ($dateTime, $endTime) {
                        $q->where('appointment_date', '<=', $dateTime)
                          ->whereRaw('DATE_ADD(appointment_date, INTERVAL duration_minutes MINUTE) > ?', [$dateTime]);
                    });
            })
            ->exists();
    }
}