<?php

namespace App\Http\Controllers;

use App\Models\MedicalRecord;
use App\Services\UserService;
use App\Http\Requests\MedicalRecordRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MedicalRecordController extends Controller
{
    public function __construct(
        private UserService $userService
    ) {}

    public function index()
    {
        $user = auth()->user();
        
        // Get medical records based on user role
        $records = $this->getMedicalRecordsForRole($user);
        
        return Inertia::render('MedicalRecords/Index', [
            'records' => $records,
            'userRole' => $user->roles->first()->name,
            'canCreate' => $user->hasAnyRole(['doctor', 'admin']),
            'stats' => [
                'total' => $records->count(),
                'recent' => $records->where('created_at', '>=', now()->subDays(30))->count(),
                'by_type' => $records->groupBy('record_type')->map->count(),
            ]
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        
        if (!$user->hasAnyRole(['doctor', 'admin'])) {
            abort(403, 'Unauthorized');
        }

        $data = [];
        
        if ($user->hasRole('doctor')) {
            // Doctor can create records for their patients
            $branchId = $user->doctorProfile->branch_id;
            $data['patients'] = $this->userService->getPatientsByBranch($branchId);
        } elseif ($user->hasRole('admin')) {
            // Admin can create records for any patient
            $data['patients'] = $this->userService->getUsersByRole('patient');
            $data['doctors'] = $this->userService->getUsersByRole('doctor');
        }

        $data['recordTypes'] = [
            'consultation' => 'Consultation',
            'test_result' => 'Test Result',
            'prescription' => 'Prescription',
            'diagnosis' => 'Diagnosis',
            'treatment_plan' => 'Treatment Plan',
            'follow_up' => 'Follow Up',
        ];

        return Inertia::render('MedicalRecords/Create', $data);
    }

    public function store(MedicalRecordRequest $request)
    {
        $validated = $request->validated();

        // Set doctor_id based on current user
        if ($user->hasRole('doctor')) {
            $validated['doctor_id'] = $user->id;
        } elseif (isset($validated['doctor_id'])) {
            // Admin can specify doctor
            $validated['doctor_id'] = $validated['doctor_id'];
        } else {
            $validated['doctor_id'] = $user->id;
        }

        try {
            MedicalRecord::create($validated);
            
            return redirect()->route('medical-records.index')
                ->with('success', 'Medical record created successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $user = auth()->user();
        $record = MedicalRecord::with(['patient.patientProfile', 'doctor.doctorProfile', 'appointment'])->find($id);
        
        if (!$record || !$this->canViewRecord($user, $record)) {
            abort(403, 'Unauthorized');
        }

        return Inertia::render('MedicalRecords/Show', [
            'record' => $record,
            'canEdit' => $this->canEditRecord($user, $record)
        ]);
    }

    public function edit($id)
    {
        $user = auth()->user();
        $record = MedicalRecord::with(['patient', 'doctor'])->find($id);
        
        if (!$record || !$this->canEditRecord($user, $record)) {
            abort(403, 'Unauthorized');
        }

        $recordTypes = [
            'consultation' => 'Consultation',
            'test_result' => 'Test Result',
            'prescription' => 'Prescription',
            'diagnosis' => 'Diagnosis',
            'treatment_plan' => 'Treatment Plan',
            'follow_up' => 'Follow Up',
        ];

        return Inertia::render('MedicalRecords/Edit', [
            'record' => $record,
            'recordTypes' => $recordTypes
        ]);
    }

    public function update(Request $request, $id)
    {
        $user = auth()->user();
        $record = MedicalRecord::findOrFail($id);

        if (!$this->canEditRecord($user, $record)) {
            abort(403, 'Unauthorized');
        }

        $validated = $request->validate([
            'record_type' => 'required|string|max:50',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'findings' => 'nullable|string',
            'recommendations' => 'nullable|string',
            'record_date' => 'required|date',
        ]);

        try {
            $record->update($validated);
            
            return redirect()->route('medical-records.index')
                ->with('success', 'Medical record updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $record = MedicalRecord::findOrFail($id);

        if (!$this->canEditRecord($user, $record)) {
            abort(403, 'Unauthorized');
        }

        try {
            $record->delete();
            
            return redirect()->route('medical-records.index')
                ->with('success', 'Medical record deleted successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    // Private helper methods
    private function getMedicalRecordsForRole($user)
    {
        $query = MedicalRecord::with(['patient.patientProfile', 'doctor.doctorProfile', 'appointment'])
            ->orderBy('record_date', 'desc');

        if ($user->hasRole('admin')) {
            return $query->get();
        } elseif ($user->hasRole('branch_head')) {
            // Branch head sees records for patients in their branch
            $branchId = $user->managedBranch->id;
            return $query->whereHas('patient.patientProfile', function($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            })->get();
        } elseif ($user->hasRole('doctor')) {
            // Doctor sees records they created
            return $query->where('doctor_id', $user->id)->get();
        } elseif ($user->hasRole('patient')) {
            // Patient sees only their own records
            return $query->where('patient_id', $user->id)->get();
        }
        
        return collect();
    }

    private function canViewRecord($user, $record)
    {
        if ($user->hasRole('admin')) {
            return true;
        } elseif ($user->hasRole('branch_head')) {
            // Branch head can view records for patients in their branch
            $branchId = $user->managedBranch->id;
            return $record->patient->patientProfile->branch_id === $branchId;
        } elseif ($user->hasRole('doctor')) {
            // Doctor can view records they created or for their patients
            return $record->doctor_id === $user->id || 
                   $record->patient->patientProfile->branch_id === $user->doctorProfile->branch_id;
        } elseif ($user->hasRole('patient')) {
            // Patient can only view their own records
            return $record->patient_id === $user->id;
        }
        
        return false;
    }

    private function canEditRecord($user, $record)
    {
        if ($user->hasRole('admin')) {
            return true;
        } elseif ($user->hasRole('doctor')) {
            // Doctor can edit records they created
            return $record->doctor_id === $user->id;
        }
        
        return false;
    }
}