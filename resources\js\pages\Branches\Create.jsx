import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { ArrowLeft, Building2, Save } from 'lucide-react';

export default function BranchCreate({ availableBranchHeads = [] }) {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        address: '',
        city: '',
        state: '',
        postal_code: '',
        phone: '',
        email: '',
        branch_head_id: 'none',
        is_active: true,
        description: '',
        create_branch_head: false,
        branch_head_data: {
            name: '',
            email: '',
            password: '',
            password_confirmation: '',
            phone: ''
        }
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        
        post(route('branches.store'), {
            transform: (data) => ({
                ...data,
                branch_head_id: data.branch_head_id === 'none' ? null : data.branch_head_id
            })
        });
    };

    const handleBranchHeadDataChange = (field, value) => {
        setData('branch_head_data', {
            ...data.branch_head_data,
            [field]: value
        });
    };

    return (
        <AppLayout>
            <Head title="Create Branch" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex items-center gap-4">
                        <Link href={route('branches.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Create New Branch</h1>
                            <p className="text-gray-600">Add a new therapy center branch</p>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Building2 className="h-5 w-5" />
                                        Branch Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="name">Branch Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Main Branch"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="address">Address *</Label>
                                        <Textarea
                                            id="address"
                                            value={data.address}
                                            onChange={(e) => setData('address', e.target.value)}
                                            placeholder="123 Main Street"
                                            className={errors.address ? 'border-red-500' : ''}
                                        />
                                        {errors.address && <p className="text-sm text-red-600 mt-1">{errors.address}</p>}
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="city">City *</Label>
                                            <Input
                                                id="city"
                                                value={data.city}
                                                onChange={(e) => setData('city', e.target.value)}
                                                placeholder="New York"
                                                className={errors.city ? 'border-red-500' : ''}
                                            />
                                            {errors.city && <p className="text-sm text-red-600 mt-1">{errors.city}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="state">State *</Label>
                                            <Input
                                                id="state"
                                                value={data.state}
                                                onChange={(e) => setData('state', e.target.value)}
                                                placeholder="NY"
                                                className={errors.state ? 'border-red-500' : ''}
                                            />
                                            {errors.state && <p className="text-sm text-red-600 mt-1">{errors.state}</p>}
                                        </div>
                                    </div>

                                    <div>
                                        <Label htmlFor="postal_code">Postal Code *</Label>
                                        <Input
                                            id="postal_code"
                                            value={data.postal_code}
                                            onChange={(e) => setData('postal_code', e.target.value)}
                                            placeholder="10001"
                                            className={errors.postal_code ? 'border-red-500' : ''}
                                        />
                                        {errors.postal_code && <p className="text-sm text-red-600 mt-1">{errors.postal_code}</p>}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Contact Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Contact Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="phone">Phone Number *</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+****************"
                                            className={errors.phone ? 'border-red-500' : ''}
                                        />
                                        {errors.phone && <p className="text-sm text-red-600 mt-1">{errors.phone}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors.email ? 'border-red-500' : ''}
                                        />
                                        {errors.email && <p className="text-sm text-red-600 mt-1">{errors.email}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="description">Description</Label>
                                        <Textarea
                                            id="description"
                                            value={data.description}
                                            onChange={(e) => setData('description', e.target.value)}
                                            placeholder="Optional description of the branch"
                                            className={errors.description ? 'border-red-500' : ''}
                                        />
                                        {errors.description && <p className="text-sm text-red-600 mt-1">{errors.description}</p>}
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="is_active"
                                            checked={data.is_active}
                                            onChange={(e) => setData('is_active', e.target.checked)}
                                        />
                                        <Label htmlFor="is_active">Active Branch</Label>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Branch Head Selection */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Branch Manager</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-center space-x-4">
                                        <input
                                            type="radio"
                                            id="existing_head"
                                            name="branch_head_option"
                                            checked={!data.create_branch_head}
                                            onChange={() => setData('create_branch_head', false)}
                                        />
                                        <Label htmlFor="existing_head">Select Existing User</Label>
                                        
                                        <input
                                            type="radio"
                                            id="new_head"
                                            name="branch_head_option"
                                            checked={data.create_branch_head}
                                            onChange={() => setData('create_branch_head', true)}
                                        />
                                        <Label htmlFor="new_head">Create New Branch Head</Label>
                                    </div>

                                    {!data.create_branch_head ? (
                                        <div>
                                            <Label>Select Branch Head</Label>
                                            <Select
                                                value={data.branch_head_id}
                                                onValueChange={(value) => setData('branch_head_id', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Choose a branch head..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="none">No branch head</SelectItem>
                                                    {availableBranchHeads.map((user) => (
                                                        <SelectItem key={user.id} value={user.id.toString()}>
                                                            {user.name} ({user.email})
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    ) : (
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label>Name *</Label>
                                                <Input
                                                    value={data.branch_head_data.name}
                                                    onChange={(e) => handleBranchHeadDataChange('name', e.target.value)}
                                                    placeholder="Branch head name"
                                                />
                                            </div>
                                            <div>
                                                <Label>Email *</Label>
                                                <Input
                                                    type="email"
                                                    value={data.branch_head_data.email}
                                                    onChange={(e) => handleBranchHeadDataChange('email', e.target.value)}
                                                    placeholder="<EMAIL>"
                                                />
                                            </div>
                                            <div>
                                                <Label>Password *</Label>
                                                <Input
                                                    type="password"
                                                    value={data.branch_head_data.password}
                                                    onChange={(e) => handleBranchHeadDataChange('password', e.target.value)}
                                                />
                                            </div>
                                            <div>
                                                <Label>Confirm Password *</Label>
                                                <Input
                                                    type="password"
                                                    value={data.branch_head_data.password_confirmation}
                                                    onChange={(e) => handleBranchHeadDataChange('password_confirmation', e.target.value)}
                                                />
                                            </div>
                                            <div>
                                                <Label>Phone</Label>
                                                <Input
                                                    value={data.branch_head_data.phone}
                                                    onChange={(e) => handleBranchHeadDataChange('phone', e.target.value)}
                                                    placeholder="+****************"
                                                />
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Actions */}
                        <div className="flex justify-end gap-4 mt-6">
                            <Link href={route('branches.index')}>
                                <Button variant="outline">Cancel</Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Creating...' : 'Create Branch'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}