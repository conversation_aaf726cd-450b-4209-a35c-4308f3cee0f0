import { createContext, useContext, useState, useCallback } from 'react';
import Modal from '@/Components/ui/Modal';

const ModalContext = createContext();

export const useModal = () => {
    const context = useContext(ModalContext);
    if (!context) {
        throw new Error('useModal must be used within a ModalProvider');
    }
    return context;
};

export const ModalProvider = ({ children }) => {
    const [modals, setModals] = useState([]);

    const openModal = useCallback((modalConfig) => {
        const id = Date.now() + Math.random();
        const modal = {
            id,
            isOpen: true,
            ...modalConfig,
        };

        setModals(prev => [...prev, modal]);
        return id;
    }, []);

    const closeModal = useCallback((id) => {
        setModals(prev => prev.filter(modal => modal.id !== id));
    }, []);

    const closeAllModals = useCallback(() => {
        setModals([]);
    }, []);

    const updateModal = useCallback((id, updates) => {
        setModals(prev => prev.map(modal => 
            modal.id === id ? { ...modal, ...updates } : modal
        ));
    }, []);

    // Convenience methods for common modal types
    const openFormModal = useCallback((config) => {
        return openModal({
            size: 'lg',
            showHeader: true,
            showFooter: true,
            closeOnOverlayClick: false,
            closeOnEscape: true,
            ...config,
        });
    }, [openModal]);

    const openConfirmModal = useCallback((config) => {
        return openModal({
            size: 'sm',
            showHeader: true,
            showFooter: true,
            closeOnOverlayClick: true,
            closeOnEscape: true,
            ...config,
        });
    }, [openModal]);

    const openViewModal = useCallback((config) => {
        return openModal({
            size: 'xl',
            showHeader: true,
            showFooter: false,
            closeOnOverlayClick: true,
            closeOnEscape: true,
            ...config,
        });
    }, [openModal]);

    const openFullScreenModal = useCallback((config) => {
        return openModal({
            size: 'full',
            showHeader: true,
            showFooter: true,
            closeOnOverlayClick: false,
            closeOnEscape: true,
            fullScreen: true,
            onFullScreenToggle: true,
            ...config,
        });
    }, [openModal]);

    const value = {
        modals,
        openModal,
        closeModal,
        closeAllModals,
        updateModal,
        openFormModal,
        openConfirmModal,
        openViewModal,
        openFullScreenModal,
    };

    return (
        <ModalContext.Provider value={value}>
            {children}
            {/* Render all active modals */}
            {modals.map((modal) => (
                <Modal
                    key={modal.id}
                    {...modal}
                    onClose={() => closeModal(modal.id)}
                />
            ))}
        </ModalContext.Provider>
    );
};

export default ModalContext;
