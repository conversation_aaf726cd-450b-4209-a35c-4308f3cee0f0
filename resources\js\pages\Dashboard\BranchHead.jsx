import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Users, Calendar, Building2, Clock, UserCheck } from 'lucide-react';

export default function BranchHeadDashboard({ 
    auth, 
    branch, 
    stats, 
    recentAppointments, 
    doctors, 
    patients 
}) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={<h2 className="font-semibold text-xl text-gray-800 leading-tight">Branch Dashboard</h2>}
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    {/* Branch Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Building2 className="mr-2 h-5 w-5" />
                                {branch?.name || 'Branch Management'}
                            </CardTitle>
                            <CardDescription>
                                {branch?.address || 'Managing branch operations and staff'}
                            </CardDescription>
                        </CardHeader>
                    </Card>

                    {/* Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                                <UserCheck className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.total_doctors || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Active in your branch
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.total_patients || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Registered patients
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.todays_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Scheduled for today
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending Appointments</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.pending_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Awaiting confirmation
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common branch management tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-4">
                                <Button variant="default" onClick={() => window.location.href = '/users/create'}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Add Doctor/Patient
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    View Appointments
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/doctors'}>
                                    <UserCheck className="mr-2 h-4 w-4" />
                                    Manage Doctors
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/patients'}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Manage Patients
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Appointments & Staff Overview */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Appointments</CardTitle>
                                <CardDescription>Latest appointments in your branch</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentAppointments?.slice(0, 5).map((appointment, index) => (
                                        <div key={index} className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">
                                                    {appointment.doctor?.name}
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                    Patient: {appointment.patient?.name}
                                                </p>
                                            </div>
                                            <div className="flex-shrink-0">
                                                <Badge variant={
                                                    appointment.status === 'completed' ? 'default' :
                                                    appointment.status === 'confirmed' ? 'secondary' : 'outline'
                                                }>
                                                    {appointment.status}
                                                </Badge>
                                            </div>
                                        </div>
                                    )) || (
                                        <p className="text-sm text-muted-foreground">No recent appointments</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Staff Overview</CardTitle>
                                <CardDescription>Doctors and patients in your branch</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div>
                                        <h4 className="text-sm font-medium mb-2">Doctors ({doctors?.length || 0})</h4>
                                        {doctors?.slice(0, 3).map((doctor, index) => (
                                            <div key={index} className="flex items-center justify-between py-1">
                                                <span className="text-sm">{doctor.name}</span>
                                                <Badge variant="outline" className="text-xs">
                                                    {doctor.doctorProfile?.specialization || 'Doctor'}
                                                </Badge>
                                            </div>
                                        )) || (
                                            <p className="text-sm text-muted-foreground">No doctors assigned</p>
                                        )}
                                    </div>
                                    
                                    <div>
                                        <h4 className="text-sm font-medium mb-2">Recent Patients ({patients?.length || 0})</h4>
                                        {patients?.slice(0, 3).map((patient, index) => (
                                            <div key={index} className="flex items-center justify-between py-1">
                                                <span className="text-sm">{patient.name}</span>
                                                <Badge variant="secondary" className="text-xs">
                                                    Patient
                                                </Badge>
                                            </div>
                                        )) || (
                                            <p className="text-sm text-muted-foreground">No patients registered</p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}