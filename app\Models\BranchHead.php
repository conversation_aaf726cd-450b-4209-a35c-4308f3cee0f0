<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class BranchHead extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'user_id',
        'assigned_date',
        'end_date',
        'is_active',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'assigned_date' => 'date',
            'end_date' => 'date',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCurrent($query)
    {
        return $query->where('is_active', true)
                    ->where('assigned_date', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    // Helper methods
    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        if ($this->assigned_date > $now) {
            return false;
        }

        if ($this->end_date && $this->end_date < $now) {
            return false;
        }

        return true;
    }

    public function deactivate(?string $reason = null): bool
    {
        $this->update([
            'is_active' => false,
            'end_date' => now(),
            'notes' => $this->notes . ($reason ? "\nDeactivated: {$reason}" : "\nDeactivated on " . now()->format('Y-m-d H:i:s'))
        ]);

        return true;
    }

    public function getDurationAttribute(): ?int
    {
        if (!$this->assigned_date) {
            return null;
        }

        $endDate = $this->end_date ?? now();
        return $this->assigned_date->diffInDays($endDate);
    }

    // Static methods
    public static function assignBranchHead(int $branchId, int $userId, ?string $notes = null): self
    {
        // Deactivate current branch head if exists
        static::where('branch_id', $branchId)
              ->where('is_active', true)
              ->update([
                  'is_active' => false,
                  'end_date' => now(),
                  'notes' => 'Replaced by new assignment'
              ]);

        // Create new assignment
        return static::create([
            'branch_id' => $branchId,
            'user_id' => $userId,
            'assigned_date' => now(),
            'is_active' => true,
            'notes' => $notes
        ]);
    }

    public static function getCurrentBranchHead(int $branchId): ?self
    {
        return static::where('branch_id', $branchId)
                    ->current()
                    ->first();
    }
}
