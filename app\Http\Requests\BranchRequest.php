<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BranchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasRole('admin');
    }

    public function rules(): array
    {
        $branchId = $this->route('branch') ?? $this->route('id');
        
        return [
            'name' => ['required', 'string', 'max:255'],
            'address' => ['required', 'string'],
            'phone' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'unique:branches,email,' . $branchId],
            'branch_head_id' => ['nullable', 'exists:users,id'],
            'is_active' => ['boolean'],
            
            // Branch head creation data (optional)
            'branch_head_data.name' => ['required_with:branch_head_data', 'string', 'max:255'],
            'branch_head_data.email' => ['required_with:branch_head_data', 'email', 'unique:users,email'],
            'branch_head_data.password' => ['required_with:branch_head_data', 'string', 'min:8'],
            'branch_head_data.phone' => ['nullable', 'string', 'max:20'],
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Branch name is required',
            'address.required' => 'Branch address is required',
            'phone.required' => 'Branch phone number is required',
            'email.required' => 'Branch email is required',
            'email.unique' => 'This email is already registered for another branch',
            'branch_head_data.email.unique' => 'This email is already registered',
        ];
    }
}