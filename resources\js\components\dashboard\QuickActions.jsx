import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { 
    Building2, 
    Users, 
    Calendar, 
    TrendingUp, 
    Clock, 
    CheckCircle, 
    Heart, 
    User, 
    UserCheck 
} from 'lucide-react';

export default function QuickActions({ userRole, isAdmin, isBranchHead, isDoctor, isPatient }) {
    const getDescription = () => {
        if (isAdmin()) return 'Common administrative tasks';
        if (isBranchHead()) return 'Common branch management tasks';
        if (isDoctor()) return 'Common doctor tasks';
        if (isPatient()) return 'Manage your appointments and health records';
        return 'Quick actions';
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>{getDescription()}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex flex-wrap gap-4">
                    {isAdmin() && (
                        <>
                            <Button variant="default" onClick={() => window.location.href = '/branches/create'}>
                                <Building2 className="mr-2 h-4 w-4" />Add New Branch
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/users/create'}>
                                <Users className="mr-2 h-4 w-4" />Add User
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/branches'}>
                                <TrendingUp className="mr-2 h-4 w-4" />Manage Branches
                            </Button>
                        </>
                    )}
                    
                    {isBranchHead() && (
                        <>
                            <Button variant="default" onClick={() => window.location.href = '/users/create'}>
                                <Users className="mr-2 h-4 w-4" />Add Doctor/Patient
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/doctors'}>
                                <UserCheck className="mr-2 h-4 w-4" />Manage Doctors
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/patients'}>
                                <Users className="mr-2 h-4 w-4" />Manage Patients
                            </Button>
                        </>
                    )}
                    
                    {isDoctor() && (
                        <>
                            <Button variant="default" onClick={() => window.location.href = '/appointments/today/list'}>
                                <Clock className="mr-2 h-4 w-4" />Today's Schedule
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/medical-records/create'}>
                                <CheckCircle className="mr-2 h-4 w-4" />New Record
                            </Button>
                        </>
                    )}
                    
                    {isPatient() && (
                        <>
                            <Button variant="default" onClick={() => window.location.href = '/appointments/create'}>
                                <Calendar className="mr-2 h-4 w-4" />Book Appointment
                            </Button>
                            <Button variant="outline" onClick={() => window.location.href = '/medical-records'}>
                                <Heart className="mr-2 h-4 w-4" />Medical Records
                            </Button>
                        </>
                    )}
                    
                    <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                        <Calendar className="mr-2 h-4 w-4" />
                        {isPatient() ? 'My Appointments' : 'View Appointments'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}