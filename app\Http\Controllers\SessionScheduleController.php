<?php

namespace App\Http\Controllers;

use App\Models\SessionSchedule;
use App\Models\PatientPackage;
use App\Models\User;
use App\Services\SessionSchedulingService;
use App\Http\Requests\SessionScheduleRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class SessionScheduleController extends Controller
{
    protected $schedulingService;

    public function __construct(SessionSchedulingService $schedulingService)
    {
        $this->schedulingService = $schedulingService;
    }

    /**
     * Display session scheduling interface
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $view = $request->get('view', 'calendar'); // calendar, list, timeline
        $date = $request->get('date', today()->toDateString());

        $sessionsQuery = SessionSchedule::with(['patient', 'doctor', 'patientPackage.therapyPackage']);

        if ($user->hasRole('doctor')) {
            $sessionsQuery->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            $sessionsQuery->where('patient_id', $user->id);
        }

        // Filter by date range based on view
        if ($view === 'calendar') {
            $startDate = Carbon::parse($date)->startOfMonth();
            $endDate = Carbon::parse($date)->endOfMonth();
        } else {
            $startDate = Carbon::parse($date)->startOfWeek();
            $endDate = Carbon::parse($date)->endOfWeek();
        }

        $sessions = $sessionsQuery->whereBetween('scheduled_date', [$startDate, $endDate])
                                 ->orderBy('scheduled_date')
                                 ->orderBy('scheduled_time')
                                 ->get();

        return Inertia::render('Sessions/Schedule', [
            'sessions' => $sessions,
            'view' => $view,
            'selectedDate' => $date,
            'canSchedule' => $user->hasAnyRole(['admin', 'branch_head', 'doctor']),
            'doctors' => User::role('doctor')->get(['id', 'name']),
            'patients' => User::role('patient')->with('patientPackages.therapyPackage')->get(),
        ]);
    }

    /**
     * Show form for creating a new session
     */
    public function create(Request $request)
    {
        $this->authorize('create', SessionSchedule::class);

        $patientPackage = null;
        if ($request->package_id) {
            $patientPackage = PatientPackage::with(['patient', 'therapyPackage'])
                ->findOrFail($request->package_id);
        }

        return Inertia::render('Sessions/Create', [
            'patientPackage' => $patientPackage,
            'doctors' => User::role('doctor')->get(['id', 'name']),
            'patients' => User::role('patient')->get(['id', 'name']),
            'packages' => PatientPackage::with(['patient', 'therapyPackage'])
                ->where('status', 'active')
                ->get(),
        ]);
    }

    /**
     * Store a new session
     */
    public function store(SessionScheduleRequest $request)
    {
        $this->authorize('create', SessionSchedule::class);

        try {
            $session = $this->schedulingService->scheduleSession($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Session scheduled successfully',
                'session' => $session->load(['patient', 'doctor', 'patientPackage.therapyPackage']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Display session details
     */
    public function show(SessionSchedule $session)
    {
        $this->authorize('view', $session);

        $session->load([
            'patient',
            'doctor',
            'patientPackage.therapyPackage',
            'attendance',
            'scheduledBy',
            'rescheduledBy',
            'cancelledBy'
        ]);

        return Inertia::render('Sessions/Show', [
            'session' => $session,
            'canEdit' => auth()->user()->can('update', $session),
            'canMarkAttendance' => auth()->user()->can('markAttendance', $session),
        ]);
    }

    /**
     * Show form for editing session
     */
    public function edit(SessionSchedule $session)
    {
        $this->authorize('update', $session);

        return Inertia::render('Sessions/Edit', [
            'session' => $session->load(['patient', 'doctor', 'patientPackage.therapyPackage']),
            'doctors' => User::role('doctor')->get(['id', 'name']),
        ]);
    }

    /**
     * Update session
     */
    public function update(SessionScheduleRequest $request, SessionSchedule $session)
    {
        $this->authorize('update', $session);

        try {
            $updatedSession = $this->schedulingService->updateSession($session, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Session updated successfully',
                'session' => $updatedSession->load(['patient', 'doctor', 'patientPackage.therapyPackage']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Reschedule session
     */
    public function reschedule(Request $request, SessionSchedule $session)
    {
        $this->authorize('reschedule', $session);

        $validated = $request->validate([
            'scheduled_date' => 'required|date|after_or_equal:today',
            'scheduled_time' => 'required|date_format:H:i',
            'reschedule_reason' => 'nullable|string|max:500',
        ]);

        try {
            $newSession = $this->schedulingService->rescheduleSession(
                $session,
                $validated['scheduled_date'],
                $validated['scheduled_time'],
                $validated['reschedule_reason']
            );

            return response()->json([
                'success' => true,
                'message' => 'Session rescheduled successfully',
                'session' => $newSession->load(['patient', 'doctor', 'patientPackage.therapyPackage']),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Cancel session
     */
    public function cancel(Request $request, SessionSchedule $session)
    {
        $this->authorize('cancel', $session);

        $validated = $request->validate([
            'cancellation_reason' => 'required|string|max:500',
        ]);

        try {
            $this->schedulingService->cancelSession($session, $validated['cancellation_reason']);

            return response()->json([
                'success' => true,
                'message' => 'Session cancelled successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get available time slots for scheduling
     */
    public function availableSlots(Request $request)
    {
        $validated = $request->validate([
            'doctor_id' => 'required|exists:users,id',
            'date' => 'required|date|after_or_equal:today',
            'duration' => 'nullable|integer|min:15|max:240',
        ]);

        $slots = $this->schedulingService->getAvailableTimeSlots(
            $validated['doctor_id'],
            $validated['date'],
            $validated['duration'] ?? 60
        );

        return response()->json([
            'slots' => $slots,
        ]);
    }

    /**
     * Bulk schedule sessions for a package
     */
    public function bulkSchedule(Request $request)
    {
        $this->authorize('bulkSchedule');

        $validated = $request->validate([
            'patient_package_id' => 'required|exists:patient_packages,id',
            'doctor_id' => 'required|exists:users,id',
            'start_date' => 'required|date|after_or_equal:today',
            'session_frequency' => 'required|in:daily,weekly,biweekly,monthly',
            'session_time' => 'required|date_format:H:i',
            'session_duration' => 'required|integer|min:15|max:240',
            'number_of_sessions' => 'required|integer|min:1|max:50',
            'skip_weekends' => 'boolean',
            'skip_holidays' => 'boolean',
        ]);

        try {
            $sessions = $this->schedulingService->bulkScheduleSessions($validated);

            return response()->json([
                'success' => true,
                'message' => 'Sessions scheduled successfully',
                'sessions' => $sessions,
                'count' => count($sessions),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get doctor's schedule for a specific date
     */
    public function doctorSchedule(User $doctor, Request $request)
    {
        $this->authorize('viewDoctorSchedule', $doctor);

        $date = $request->get('date', today()->toDateString());
        
        $sessions = SessionSchedule::with(['patient', 'patientPackage.therapyPackage', 'attendance'])
            ->where('doctor_id', $doctor->id)
            ->whereDate('scheduled_date', $date)
            ->orderBy('scheduled_time')
            ->get();

        return response()->json([
            'doctor' => $doctor,
            'date' => $date,
            'sessions' => $sessions,
        ]);
    }

    /**
     * Get patient's upcoming sessions
     */
    public function patientSessions(User $patient, Request $request)
    {
        $this->authorize('viewPatientSessions', $patient);

        $limit = $request->get('limit', 10);
        
        $sessions = SessionSchedule::with(['doctor', 'patientPackage.therapyPackage'])
            ->where('patient_id', $patient->id)
            ->where('status', 'scheduled')
            ->where('scheduled_date', '>=', today())
            ->orderBy('scheduled_date')
            ->orderBy('scheduled_time')
            ->limit($limit)
            ->get();

        return response()->json([
            'patient' => $patient,
            'sessions' => $sessions,
        ]);
    }
}
