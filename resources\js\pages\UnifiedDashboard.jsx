import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { 
    Users, 
    Building2, 
    Calendar, 
    TrendingUp, 
    Clock, 
    CheckCircle, 
    Heart, 
    User, 
    Stethoscope,
    UserCheck,
    AlertCircle
} from 'lucide-react';

export default function UnifiedDashboard({ 
    auth, 
    stats = {}, 
    recentActivity = {}, 
    upcomingAppointments = [], 
    branchStats = [],
    branch = null,
    doctorProfile = null,
    patientProfile = null,
    todaysAppointments = [],
    pastAppointments = [],
    recentPatients = [],
    recentDoctors = [],
    doctors = [],
    patients = []
}) {
    const user = auth.user;
    const userRole = user.roles?.[0]?.name;

    // Role check helpers
    const isAdmin = () => userRole === 'admin';
    const isBranchHead = () => userRole === 'branch_head';
    const isDoctor = () => userRole === 'doctor';
    const isPatient = () => userRole === 'patient';

    return (
        <AuthenticatedLayout
            user={user}
            header={
                <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                    {isAdmin() && 'Admin Dashboard'}
                    {isBranchHead() && 'Branch Dashboard'}
                    {isDoctor() && 'Doctor Dashboard'}
                    {isPatient() && 'Patient Dashboard'}
                    {!userRole && 'Dashboard'}
                </h2>
            }
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Profile Header */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                {isAdmin() && <><Building2 className="mr-2 h-5 w-5" />System Administrator</>}
                                {isBranchHead() && <><Building2 className="mr-2 h-5 w-5" />{branch?.name || 'Branch Management'}</>}
                                {isDoctor() && <><Stethoscope className="mr-2 h-5 w-5" />Dr. {user.name}</>}
                                {isPatient() && <><User className="mr-2 h-5 w-5" />{user.name}</>}
                                {!userRole && <><User className="mr-2 h-5 w-5" />Welcome, {user.name}</>}
                            </CardTitle>
                            <CardDescription>
                                {isAdmin() && 'Managing the entire therapy system'}
                                {isBranchHead() && (branch?.address || 'Managing branch operations and staff')}
                                {isDoctor() && `${doctorProfile?.specialization || 'Medical Professional'} • ${doctorProfile?.branch?.name || 'Therapy Center'}`}
                                {isPatient() && `Patient at ${patientProfile?.branch?.name || 'Therapy Center'}`}
                                {!userRole && 'Your account is set up, but no specific role has been assigned yet.'}
                            </CardDescription>
                        </CardHeader>
                        
                        {/* Additional profile info for specific roles */}
                        {isDoctor() && doctorProfile && (
                            <CardContent>
                                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <span>{doctorProfile.experience_years || 0} years experience</span>
                                    <span>•</span>
                                    <span>License: {doctorProfile.license_number || 'N/A'}</span>
                                </div>
                            </CardContent>
                        )}
                        
                        {isPatient() && patientProfile && (
                            <CardContent>
                                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                    <span>DOB: {patientProfile.date_of_birth ? new Date(patientProfile.date_of_birth).toLocaleDateString() : 'N/A'}</span>
                                    <span>•</span>
                                    <span>Gender: {patientProfile.gender || 'N/A'}</span>
                                    {patientProfile.insurance_provider && (
                                        <>
                                            <span>•</span>
                                            <span>Insurance: {patientProfile.insurance_provider}</span>
                                        </>
                                    )}
                                </div>
                            </CardContent>
                        )}
                    </Card>

                    {/* No Role Warning */}
                    {!userRole && (
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-start space-x-4 p-4 border border-orange-200 bg-orange-50 rounded-lg">
                                    <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                                    <div className="flex-1">
                                        <h3 className="text-sm font-medium text-orange-800">
                                            Account Setup Required
                                        </h3>
                                        <p className="mt-1 text-sm text-orange-700">
                                            Your account doesn't have a specific role assigned yet. Please contact your administrator.
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Stats Overview */}
                    {userRole && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {/* Admin Stats */}
                            {isAdmin() && (
                                <>
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
                                            <Building2 className="h-4 w-4 text-muted-foreground" />
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-2xl font-bold">{stats.total_branches || 0}</div>
                                            <p className="text-xs text-muted-foreground">{stats.active_branches || 0} active</p>
                                        </CardContent>
                                    </Card>
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                                            <Users className="h-4 w-4 text-muted-foreground" />
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-2xl font-bold">{stats.total_doctors || 0}</div>
                                            <p className="text-xs text-muted-foreground">Across all branches</p>
                                        </CardContent>
                                    </Card>
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                                            <Users className="h-4 w-4 text-muted-foreground" />
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-2xl font-bold">{stats.total_patients || 0}</div>
                                            <p className="text-xs text-muted-foreground">Registered patients</p>
                                        </CardContent>
                                    </Card>
                                </>
                            )}

                            {/* Branch Head Stats */}
                            {isBranchHead() && (
                                <>
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                                            <UserCheck className="h-4 w-4 text-muted-foreground" />
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-2xl font-bold">{stats.total_doctors || 0}</div>
                                            <p className="text-xs text-muted-foreground">Active in your branch</p>
                                        </CardContent>
                                    </Card>
                                    <Card>
                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                            <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                                            <Users className="h-4 w-4 text-muted-foreground" />
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-2xl font-bold">{stats.total_patients || 0}</div>
                                            <p className="text-xs text-muted-foreground">Registered patients</p>
                                        </CardContent>
                                    </Card>
                                </>
                            )}

                            {/* Common Stats for all roles */}
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        {isPatient() ? 'Total Appointments' : "Today's Appointments"}
                                    </CardTitle>
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">
                                        {isPatient() ? (stats.total_appointments || 0) : (stats.todays_appointments || 0)}
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        {isPatient() ? 'All time' : `${stats.completed_today || 0} completed`}
                                    </p>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        {isPatient() ? 'Upcoming' : isDoctor() ? 'Upcoming' : 'Pending'}
                                    </CardTitle>
                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">
                                        {stats.upcoming_appointments || stats.pending_appointments || 0}
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        {isPatient() ? 'Scheduled sessions' : 'Awaiting confirmation'}
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    )}

                    {/* Quick Actions */}
                    {userRole && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                                <CardDescription>
                                    {isAdmin() && 'Common administrative tasks'}
                                    {isBranchHead() && 'Common branch management tasks'}
                                    {isDoctor() && 'Common doctor tasks'}
                                    {isPatient() && 'Manage your appointments and health records'}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="flex flex-wrap gap-4">
                                    {isAdmin() && (
                                        <>
                                            <Button variant="default" onClick={() => window.location.href = '/branches/create'}>
                                                <Building2 className="mr-2 h-4 w-4" />Add New Branch
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/users/create'}>
                                                <Users className="mr-2 h-4 w-4" />Add User
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/branches'}>
                                                <TrendingUp className="mr-2 h-4 w-4" />Manage Branches
                                            </Button>
                                        </>
                                    )}
                                    
                                    {isBranchHead() && (
                                        <>
                                            <Button variant="default" onClick={() => window.location.href = '/users/create'}>
                                                <Users className="mr-2 h-4 w-4" />Add Doctor/Patient
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/doctors'}>
                                                <UserCheck className="mr-2 h-4 w-4" />Manage Doctors
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/patients'}>
                                                <Users className="mr-2 h-4 w-4" />Manage Patients
                                            </Button>
                                        </>
                                    )}
                                    
                                    {isDoctor() && (
                                        <>
                                            <Button variant="default" onClick={() => window.location.href = '/appointments/today/list'}>
                                                <Clock className="mr-2 h-4 w-4" />Today's Schedule
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/medical-records/create'}>
                                                <CheckCircle className="mr-2 h-4 w-4" />New Record
                                            </Button>
                                        </>
                                    )}
                                    
                                    {isPatient() && (
                                        <>
                                            <Button variant="default" onClick={() => window.location.href = '/appointments/create'}>
                                                <Calendar className="mr-2 h-4 w-4" />Book Appointment
                                            </Button>
                                            <Button variant="outline" onClick={() => window.location.href = '/medical-records'}>
                                                <Heart className="mr-2 h-4 w-4" />Medical Records
                                            </Button>
                                        </>
                                    )}
                                    
                                    <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                                        <Calendar className="mr-2 h-4 w-4" />
                                        {isPatient() ? 'My Appointments' : 'View Appointments'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Dynamic Content Cards */}
                    {(upcomingAppointments.length > 0 || todaysAppointments.length > 0 || pastAppointments.length > 0 || branchStats.length > 0) && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Appointments */}
                            {(upcomingAppointments.length > 0 || todaysAppointments.length > 0) && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>
                                            {isDoctor() ? "Today's Schedule" : 
                                             isPatient() ? "Upcoming Appointments" : 
                                             "Recent Appointments"}
                                        </CardTitle>
                                        <CardDescription>
                                            {isDoctor() ? "Your appointments for today" :
                                             isPatient() ? "Your scheduled therapy sessions" :
                                             "Latest appointments"}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            {(isDoctor() ? todaysAppointments : upcomingAppointments).slice(0, 5).map((appointment, index) => (
                                                <div key={index} className="flex items-center space-x-4">
                                                    <div className="flex-shrink-0">
                                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                                    </div>
                                                    <div className="min-w-0 flex-1">
                                                        <p className="text-sm font-medium truncate">
                                                            {isPatient() ? `Dr. ${appointment.doctor?.name}` : appointment.patient?.name}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {new Date(appointment.appointment_date).toLocaleDateString()} • {new Date(appointment.appointment_date).toLocaleTimeString([], {
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                            })}
                                                        </p>
                                                    </div>
                                                    <div className="flex-shrink-0">
                                                        <Badge variant={
                                                            appointment.status === 'completed' ? 'default' :
                                                            appointment.status === 'confirmed' ? 'secondary' : 'outline'
                                                        }>
                                                            {appointment.status}
                                                        </Badge>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Secondary Content */}
                            {isAdmin() && branchStats.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Branch Overview</CardTitle>
                                        <CardDescription>Performance metrics for all branches</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            {branchStats.slice(0, 5).map((branch, index) => (
                                                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                                                    <div>
                                                        <p className="text-sm font-medium">{branch.name}</p>
                                                        <p className="text-xs text-muted-foreground">
                                                            {branch.doctors} doctors • {branch.patients} patients
                                                        </p>
                                                    </div>
                                                    <Badge variant="secondary">{branch.appointments} appointments</Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {isPatient() && pastAppointments.length > 0 && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Recent Sessions</CardTitle>
                                        <CardDescription>Your past therapy appointments</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            {pastAppointments.slice(0, 5).map((appointment, index) => (
                                                <div key={index} className="flex items-center space-x-4">
                                                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                                                    <div className="min-w-0 flex-1">
                                                        <p className="text-sm font-medium">Dr. {appointment.doctor?.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {new Date(appointment.appointment_date).toLocaleDateString()}
                                                        </p>
                                                    </div>
                                                    <Badge variant={appointment.status === 'completed' ? 'default' : 'outline'}>
                                                        {appointment.status}
                                                    </Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </AuthenticatedLayout>
    );
}