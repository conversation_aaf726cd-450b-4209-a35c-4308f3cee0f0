<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('session_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('session_schedule_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('patient_package_id')->constrained()->onDelete('cascade');
            
            // Attendance details
            $table->enum('attendance_status', ['present', 'absent', 'late', 'partial', 'excused'])->default('present');
            $table->timestamp('check_in_time')->nullable();
            $table->timestamp('check_out_time')->nullable();
            $table->integer('actual_duration_minutes')->nullable();
            
            // Attendance recording
            $table->foreignId('marked_by')->constrained('users')->onDelete('cascade'); // Who marked attendance
            $table->timestamp('marked_at');
            $table->enum('marking_method', ['manual', 'qr_code', 'biometric', 'auto'])->default('manual');
            
            // Late arrival tracking
            $table->integer('late_minutes')->default(0);
            $table->text('late_reason')->nullable();
            
            // Absence tracking
            $table->text('absence_reason')->nullable();
            $table->boolean('is_excused')->default(false);
            $table->text('excuse_reason')->nullable();
            
            // Session quality and progress
            $table->enum('session_completion', ['completed', 'partial', 'interrupted', 'cancelled'])->default('completed');
            $table->integer('participation_score')->nullable(); // 1-10 scale
            $table->text('session_summary')->nullable();
            $table->text('homework_assigned')->nullable();
            $table->text('next_session_goals')->nullable();
            
            // Progress tracking
            $table->json('progress_metrics')->nullable(); // Custom progress indicators
            $table->enum('mood_before', ['excellent', 'good', 'neutral', 'poor', 'very_poor'])->nullable();
            $table->enum('mood_after', ['excellent', 'good', 'neutral', 'poor', 'very_poor'])->nullable();
            
            // Follow-up and recommendations
            $table->text('doctor_notes')->nullable();
            $table->text('follow_up_instructions')->nullable();
            $table->boolean('requires_follow_up')->default(false);
            $table->date('next_recommended_date')->nullable();
            
            // Administrative
            $table->boolean('billable')->default(true);
            $table->decimal('session_fee', 8, 2)->nullable();
            $table->boolean('insurance_covered')->default(false);
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['patient_id', 'created_at']);
            $table->index(['doctor_id', 'created_at']);
            $table->index(['attendance_status', 'created_at']);
            $table->index(['session_schedule_id']);
            $table->index(['patient_package_id', 'created_at']);
            $table->index(['marked_at', 'attendance_status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('session_attendances');
    }
};
