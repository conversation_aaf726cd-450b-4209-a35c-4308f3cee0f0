<?php

namespace App\Services;

use App\Contracts\AppointmentRepositoryInterface;
use App\Models\Appointment;
use App\Models\MedicalRecord;
use App\Models\User;
use App\Models\DoctorProfile;
use App\Exceptions\AppointmentConflictException;
use App\Exceptions\BranchAccessDeniedException;
use App\Exceptions\BusinessRuleViolationException;
use App\Exceptions\AppointmentStateException;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AppointmentService
{
    public function __construct(
        private AppointmentRepositoryInterface $appointmentRepository
    ) {}

    public function getAllAppointments()
    {
        return $this->appointmentRepository->all();
    }

    public function getAppointmentById(int $id)
    {
        return $this->appointmentRepository->find($id);
    }

    public function createAppointment(array $data)
    {
        $appointmentDateTime = Carbon::parse($data['appointment_date']);
        $duration = $data['duration_minutes'] ?? 60;
        $doctorId = $data['doctor_id'];
        $patientId = $data['patient_id'] ?? auth()->id();

        // Create a unique lock key for this doctor and time slot
        $lockKey = "appointment_lock_{$doctorId}_{$appointmentDateTime->format('Y-m-d_H-i')}";

        return Cache::lock($lockKey, 30)->block(5, function () use ($data, $appointmentDateTime, $duration, $doctorId, $patientId) {
            return DB::transaction(function () use ($data, $appointmentDateTime, $duration, $doctorId, $patientId) {
                // Validate business rules first
                $this->validateAppointmentBusinessRules($doctorId, $patientId, $appointmentDateTime, $duration);

                // Double-check for conflicts within the lock
                $hasConflict = $this->appointmentRepository->checkConflict($doctorId, $appointmentDateTime, $duration);

                if ($hasConflict) {
                    throw new AppointmentConflictException(
                        'Doctor is not available at the selected time',
                        0,
                        null,
                        [
                            'doctor_id' => $doctorId,
                            'appointment_date' => $appointmentDateTime->toISOString(),
                            'duration' => $duration
                        ]
                    );
                }

                // Validate appointment is in the future
                if ($appointmentDateTime->isPast()) {
                    throw new BusinessRuleViolationException('Cannot schedule appointments in the past');
                }

                $appointmentData = [
                    'patient_id' => $patientId,
                    'doctor_id' => $doctorId,
                    'branch_id' => $data['branch_id'],
                    'appointment_date' => $appointmentDateTime,
                    'duration_minutes' => $duration,
                    'status' => 'scheduled',
                    'notes' => $data['notes'] ?? null,
                    'symptoms' => $data['symptoms'] ?? null,
                    'fee' => $data['fee'],
                    'payment_status' => 'pending',
                ];

                return $this->appointmentRepository->create($appointmentData);
            });
        });
    }

    public function updateAppointment(int $id, array $data)
    {
        $appointment = $this->appointmentRepository->find($id);

        if (!$appointment) {
            throw new BusinessRuleViolationException('Appointment not found');
        }

        // Validate state transitions
        if (isset($data['status'])) {
            $this->validateStatusTransition($appointment->status, $data['status']);
        }

        // Check if appointment can be modified
        if (in_array($appointment->status, ['completed', 'cancelled'])) {
            throw new AppointmentStateException('Cannot modify completed or cancelled appointments');
        }

        // If rescheduling, use locking mechanism
        if (isset($data['appointment_date']) || isset($data['doctor_id'])) {
            $newDateTime = isset($data['appointment_date'])
                ? Carbon::parse($data['appointment_date'])
                : $appointment->appointment_date;

            $doctorId = $data['doctor_id'] ?? $appointment->doctor_id;
            $duration = $data['duration_minutes'] ?? $appointment->duration_minutes;

            $lockKey = "appointment_lock_{$doctorId}_{$newDateTime->format('Y-m-d_H-i')}";

            return Cache::lock($lockKey, 30)->block(5, function () use ($id, $data, $appointment, $newDateTime, $doctorId, $duration) {
                return DB::transaction(function () use ($id, $data, $appointment, $newDateTime, $doctorId, $duration) {
                    // Validate business rules for rescheduling
                    if ($newDateTime->toDateTimeString() !== $appointment->appointment_date->toDateTimeString() ||
                        $doctorId !== $appointment->doctor_id) {
                        $this->validateAppointmentBusinessRules($doctorId, $appointment->patient_id, $newDateTime, $duration);
                    }

                    // Check for conflicts (excluding current appointment)
                    $hasConflict = $this->appointmentRepository->checkConflictExcluding($doctorId, $newDateTime, $duration, $id);

                    if ($hasConflict) {
                        throw new AppointmentConflictException(
                            'Doctor is not available at the selected time',
                            0,
                            null,
                            [
                                'doctor_id' => $doctorId,
                                'appointment_date' => $newDateTime->toISOString(),
                                'duration' => $duration,
                                'appointment_id' => $id
                            ]
                        );
                    }

                    return $this->appointmentRepository->update($id, $data);
                });
            });
        }

        return DB::transaction(function () use ($id, $data) {
            return $this->appointmentRepository->update($id, $data);
        });
    }

    public function cancelAppointment(int $id, string $reason = null)
    {
        return DB::transaction(function () use ($id, $reason) {
            $appointment = $this->appointmentRepository->find($id);
            
            if (!$appointment) {
                throw new \Exception('Appointment not found');
            }

            if (!$appointment->canBeCancelled()) {
                throw new \Exception('This appointment cannot be cancelled');
            }

            $updateData = [
                'status' => 'cancelled',
                'notes' => $appointment->notes . ($reason ? "\nCancellation reason: $reason" : '')
            ];

            return $this->appointmentRepository->update($id, $updateData);
        });
    }

    public function completeAppointment(int $id, array $consultationData)
    {
        return DB::transaction(function () use ($id, $consultationData) {
            $appointment = $this->appointmentRepository->find($id);
            
            if (!$appointment) {
                throw new \Exception('Appointment not found');
            }

            if ($appointment->status !== 'in_progress') {
                throw new \Exception('Only in-progress appointments can be completed');
            }

            // Update appointment
            $updateData = [
                'status' => 'completed',
                'diagnosis' => $consultationData['diagnosis'] ?? null,
                'treatment_plan' => $consultationData['treatment_plan'] ?? null,
                'prescription' => $consultationData['prescription'] ?? null,
                'notes' => $consultationData['notes'] ?? $appointment->notes,
            ];

            $updatedAppointment = $this->appointmentRepository->update($id, $updateData);

            // Create medical record
            if (isset($consultationData['create_record']) && $consultationData['create_record']) {
                $this->createMedicalRecord($appointment, $consultationData);
            }

            return $updatedAppointment;
        });
    }

    public function getAppointmentsByDoctor(int $doctorId)
    {
        return $this->appointmentRepository->getByDoctor($doctorId);
    }

    public function getAppointmentsByPatient(int $patientId)
    {
        return $this->appointmentRepository->getByPatient($patientId);
    }

    public function getTodaysAppointments()
    {
        return $this->appointmentRepository->getTodaysAppointments();
    }

    public function getUpcomingAppointments()
    {
        return $this->appointmentRepository->getUpcomingAppointments();
    }

    public function getAppointmentsByDateRange(string $startDate, string $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        
        return $this->appointmentRepository->getByDateRange($start, $end);
    }

    public function checkDoctorAvailability(int $doctorId, string $date, int $duration = 60)
    {
        $dateTime = Carbon::parse($date);
        
        return !$this->appointmentRepository->checkConflict($doctorId, $dateTime, $duration);
    }

    public function getAppointmentsByBranch(int $branchId)
    {
        return $this->appointmentRepository->getByBranch($branchId);
    }

    /**
     * Validate business rules for appointment creation
     */
    private function validateAppointmentBusinessRules(int $doctorId, int $patientId, Carbon $appointmentDateTime, int $duration): void
    {
        // Prevent self-appointments
        if ($doctorId === $patientId) {
            throw new BusinessRuleViolationException('Doctor cannot book appointment with themselves');
        }

        // Validate doctor exists and is active
        $doctor = User::with('doctorProfile')->find($doctorId);
        if (!$doctor || !$doctor->doctorProfile || !$doctor->doctorProfile->is_active) {
            throw new BusinessRuleViolationException('Selected doctor is not available');
        }

        // Validate patient exists and is active
        $patient = User::with('patientProfile')->find($patientId);
        if (!$patient || !$patient->patientProfile || !$patient->patientProfile->is_active) {
            throw new BusinessRuleViolationException('Patient profile is not active');
        }

        // Validate branch compatibility
        if ($doctor->doctorProfile->branch_id !== $patient->patientProfile->branch_id) {
            throw new BranchAccessDeniedException('Doctor and patient must belong to the same branch');
        }

        // Validate doctor working hours
        $this->validateDoctorWorkingHours($doctor->doctorProfile, $appointmentDateTime, $duration);

        // Validate appointment timing (business hours)
        $this->validateBusinessHours($appointmentDateTime);
    }

    /**
     * Validate doctor working hours
     */
    private function validateDoctorWorkingHours(DoctorProfile $doctorProfile, Carbon $appointmentDateTime, int $duration): void
    {
        $dayOfWeek = strtolower($appointmentDateTime->format('l'));

        if (!in_array($dayOfWeek, $doctorProfile->available_days)) {
            throw new BusinessRuleViolationException("Doctor is not available on {$dayOfWeek}s");
        }

        $appointmentStart = $appointmentDateTime->format('H:i');
        $appointmentEnd = $appointmentDateTime->copy()->addMinutes($duration)->format('H:i');

        $doctorStart = $doctorProfile->start_time->format('H:i');
        $doctorEnd = $doctorProfile->end_time->format('H:i');

        if ($appointmentStart < $doctorStart || $appointmentEnd > $doctorEnd) {
            throw new BusinessRuleViolationException(
                "Appointment time ({$appointmentStart} - {$appointmentEnd}) is outside doctor's working hours ({$doctorStart} - {$doctorEnd})"
            );
        }
    }

    /**
     * Validate business hours
     */
    private function validateBusinessHours(Carbon $appointmentDateTime): void
    {
        $hour = $appointmentDateTime->hour;

        // Business hours: 8 AM to 8 PM
        if ($hour < 8 || $hour >= 20) {
            throw new BusinessRuleViolationException('Appointments can only be scheduled between 8:00 AM and 8:00 PM');
        }

        // No appointments on Sundays
        if ($appointmentDateTime->dayOfWeek === Carbon::SUNDAY) {
            throw new BusinessRuleViolationException('Appointments cannot be scheduled on Sundays');
        }
    }

    /**
     * Validate appointment status transitions
     */
    private function validateStatusTransition(string $currentStatus, string $newStatus): void
    {
        $allowedTransitions = [
            'scheduled' => ['confirmed', 'cancelled', 'no_show'],
            'confirmed' => ['in_progress', 'cancelled', 'no_show'],
            'in_progress' => ['completed', 'cancelled'],
            'completed' => [], // No transitions allowed from completed
            'cancelled' => [], // No transitions allowed from cancelled
            'no_show' => [], // No transitions allowed from no_show
        ];

        if (!isset($allowedTransitions[$currentStatus])) {
            throw new AppointmentStateException("Invalid current status: {$currentStatus}");
        }

        if (!in_array($newStatus, $allowedTransitions[$currentStatus])) {
            throw new AppointmentStateException(
                "Invalid status transition from '{$currentStatus}' to '{$newStatus}'"
            );
        }
    }

    private function createMedicalRecord(Appointment $appointment, array $data)
    {
        $recordData = [
            'patient_id' => $appointment->patient_id,
            'doctor_id' => $appointment->doctor_id,
            'appointment_id' => $appointment->id,
            'record_type' => 'consultation',
            'title' => 'Consultation - ' . $appointment->appointment_date->format('Y-m-d'),
            'description' => $data['diagnosis'] ?? '',
            'findings' => $data['findings'] ?? null,
            'recommendations' => $data['treatment_plan'] ?? null,
            'record_date' => now()->toDateString(),
        ];

        return MedicalRecord::create($recordData);
    }
}