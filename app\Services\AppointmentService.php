<?php

namespace App\Services;

use App\Contracts\AppointmentRepositoryInterface;
use App\Models\Appointment;
use App\Models\MedicalRecord;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AppointmentService
{
    public function __construct(
        private AppointmentRepositoryInterface $appointmentRepository
    ) {}

    public function getAllAppointments()
    {
        return $this->appointmentRepository->all();
    }

    public function getAppointmentById(int $id)
    {
        return $this->appointmentRepository->find($id);
    }

    public function createAppointment(array $data)
    {
        return DB::transaction(function () use ($data) {
            // Check for conflicts
            $appointmentDateTime = Carbon::parse($data['appointment_date']);
            $duration = $data['duration_minutes'] ?? 60;

            $hasConflict = $this->appointmentRepository->checkConflict(
                $data['doctor_id'],
                $appointmentDateTime,
                $duration
            );

            if ($hasConflict) {
                throw new \Exception('Doctor is not available at the selected time');
            }

            // Validate appointment is in the future
            if ($appointmentDateTime->isPast()) {
                throw new \Exception('Cannot schedule appointments in the past');
            }

            $appointmentData = [
                'patient_id' => $data['patient_id'],
                'doctor_id' => $data['doctor_id'],
                'branch_id' => $data['branch_id'],
                'appointment_date' => $appointmentDateTime,
                'duration_minutes' => $duration,
                'status' => 'scheduled',
                'notes' => $data['notes'] ?? null,
                'symptoms' => $data['symptoms'] ?? null,
                'fee' => $data['fee'],
                'payment_status' => 'pending',
            ];

            return $this->appointmentRepository->create($appointmentData);
        });
    }

    public function updateAppointment(int $id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $appointment = $this->appointmentRepository->find($id);
            
            if (!$appointment) {
                throw new \Exception('Appointment not found');
            }

            // Check if appointment can be modified
            if (in_array($appointment->status, ['completed', 'cancelled'])) {
                throw new \Exception('Cannot modify completed or cancelled appointments');
            }

            // If rescheduling, check for conflicts
            if (isset($data['appointment_date']) || isset($data['doctor_id'])) {
                $newDateTime = isset($data['appointment_date']) 
                    ? Carbon::parse($data['appointment_date'])
                    : $appointment->appointment_date;
                    
                $doctorId = $data['doctor_id'] ?? $appointment->doctor_id;
                $duration = $data['duration_minutes'] ?? $appointment->duration_minutes;

                $hasConflict = $this->appointmentRepository->checkConflict($doctorId, $newDateTime, $duration);
                
                if ($hasConflict) {
                    throw new \Exception('Doctor is not available at the selected time');
                }
            }

            return $this->appointmentRepository->update($id, $data);
        });
    }

    public function cancelAppointment(int $id, string $reason = null)
    {
        return DB::transaction(function () use ($id, $reason) {
            $appointment = $this->appointmentRepository->find($id);
            
            if (!$appointment) {
                throw new \Exception('Appointment not found');
            }

            if (!$appointment->canBeCancelled()) {
                throw new \Exception('This appointment cannot be cancelled');
            }

            $updateData = [
                'status' => 'cancelled',
                'notes' => $appointment->notes . ($reason ? "\nCancellation reason: $reason" : '')
            ];

            return $this->appointmentRepository->update($id, $updateData);
        });
    }

    public function completeAppointment(int $id, array $consultationData)
    {
        return DB::transaction(function () use ($id, $consultationData) {
            $appointment = $this->appointmentRepository->find($id);
            
            if (!$appointment) {
                throw new \Exception('Appointment not found');
            }

            if ($appointment->status !== 'in_progress') {
                throw new \Exception('Only in-progress appointments can be completed');
            }

            // Update appointment
            $updateData = [
                'status' => 'completed',
                'diagnosis' => $consultationData['diagnosis'] ?? null,
                'treatment_plan' => $consultationData['treatment_plan'] ?? null,
                'prescription' => $consultationData['prescription'] ?? null,
                'notes' => $consultationData['notes'] ?? $appointment->notes,
            ];

            $updatedAppointment = $this->appointmentRepository->update($id, $updateData);

            // Create medical record
            if (isset($consultationData['create_record']) && $consultationData['create_record']) {
                $this->createMedicalRecord($appointment, $consultationData);
            }

            return $updatedAppointment;
        });
    }

    public function getAppointmentsByDoctor(int $doctorId)
    {
        return $this->appointmentRepository->getByDoctor($doctorId);
    }

    public function getAppointmentsByPatient(int $patientId)
    {
        return $this->appointmentRepository->getByPatient($patientId);
    }

    public function getTodaysAppointments()
    {
        return $this->appointmentRepository->getTodaysAppointments();
    }

    public function getUpcomingAppointments()
    {
        return $this->appointmentRepository->getUpcomingAppointments();
    }

    public function getAppointmentsByDateRange(string $startDate, string $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        
        return $this->appointmentRepository->getByDateRange($start, $end);
    }

    public function checkDoctorAvailability(int $doctorId, string $date, int $duration = 60)
    {
        $dateTime = Carbon::parse($date);
        
        return !$this->appointmentRepository->checkConflict($doctorId, $dateTime, $duration);
    }

    public function getAppointmentsByBranch(int $branchId)
    {
        return $this->appointmentRepository->getByBranch($branchId);
    }

    private function createMedicalRecord(Appointment $appointment, array $data)
    {
        $recordData = [
            'patient_id' => $appointment->patient_id,
            'doctor_id' => $appointment->doctor_id,
            'appointment_id' => $appointment->id,
            'record_type' => 'consultation',
            'title' => 'Consultation - ' . $appointment->appointment_date->format('Y-m-d'),
            'description' => $data['diagnosis'] ?? '',
            'findings' => $data['findings'] ?? null,
            'recommendations' => $data['treatment_plan'] ?? null,
            'record_date' => now()->toDateString(),
        ];

        return MedicalRecord::create($recordData);
    }
}