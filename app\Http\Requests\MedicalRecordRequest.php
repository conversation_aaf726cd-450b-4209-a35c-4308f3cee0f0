<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MedicalRecordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasAnyRole(['doctor', 'admin']);
    }

    public function rules(): array
    {
        return [
            'patient_id' => ['required', 'exists:users,id'],
            'appointment_id' => ['nullable', 'exists:appointments,id'],
            'record_type' => ['required', 'string', 'max:50', 'in:consultation,diagnosis,treatment,follow_up,prescription,lab_result'],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:5000'],
            'findings' => ['nullable', 'string', 'max:5000'],
            'recommendations' => ['nullable', 'string', 'max:5000'],
            'record_date' => ['required', 'date', 'before_or_equal:today'],
        ];
    }

    public function messages(): array
    {
        return [
            'patient_id.required' => 'Patient selection is required',
            'patient_id.exists' => 'Selected patient does not exist',
            'record_type.required' => 'Record type is required',
            'record_type.in' => 'Invalid record type selected',
            'title.required' => 'Record title is required',
            'description.required' => 'Record description is required',
            'record_date.required' => 'Record date is required',
            'record_date.before_or_equal' => 'Record date cannot be in the future',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate that the patient belongs to the same branch as the doctor
            if ($this->patient_id && auth()->user()->hasRole('doctor')) {
                $doctor = auth()->user();
                $patient = \App\Models\User::find($this->patient_id);
                
                if ($patient && $doctor->doctorProfile && $patient->patientProfile) {
                    if ($doctor->doctorProfile->branch_id !== $patient->patientProfile->branch_id) {
                        $validator->errors()->add('patient_id', 'You can only create records for patients in your branch');
                    }
                }
            }
        });
    }
}
