<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasAnyRole(['admin', 'branch_head']);
    }

    public function rules(): array
    {
        $userId = $this->route('user') ?? $this->route('id');
        $isUpdate = $this->isMethod('PUT') || $this->isMethod('PATCH');
        
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'unique:users,email,' . $userId],
            'phone' => ['nullable', 'string', 'max:20'],
            'is_active' => ['boolean'],
            'role' => ['required', 'in:admin,doctor,patient,branch_head'],
        ];

        if (!$isUpdate) {
            $rules['password'] = ['required', 'string', 'min:8', 'confirmed'];
            $rules['password_confirmation'] = ['required', 'string', 'min:8'];
        } else {
            $rules['password'] = ['nullable', 'string', 'min:8', 'confirmed'];
            $rules['password_confirmation'] = ['nullable', 'string', 'min:8'];
        }

        // Doctor-specific validation
        if ($this->role === 'doctor') {
            $rules = array_merge($rules, [
                'branch_id' => ['required', 'exists:branches,id'],
                'specialization' => ['required', 'string', 'max:255'],
                'license_number' => ['required', 'string', 'max:100', 'unique:doctor_profiles,license_number,' . ($isUpdate ? $userId : null) . ',user_id'],
                'qualifications' => ['required', 'string'],
                'experience_years' => ['required', 'integer', 'min:0', 'max:50'],
                'doctor_phone' => ['nullable', 'string', 'max:20'],
                'bio' => ['nullable', 'string'],
                'consultation_fee' => ['required', 'numeric', 'min:0'],
                'available_days' => ['required', 'array', 'min:1'],
                'available_days.*' => ['string', 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday'],
                'start_time' => ['required', 'date_format:H:i'],
                'end_time' => ['required', 'date_format:H:i', 'after:start_time'],
            ]);
        }

        // Patient-specific validation
        if ($this->role === 'patient') {
            $rules = array_merge($rules, [
                'branch_id' => ['required', 'exists:branches,id'],
                'date_of_birth' => ['required', 'date', 'before:today'],
                'gender' => ['required', 'in:male,female,other'],
                'patient_phone' => ['nullable', 'string', 'max:20'],
                'address' => ['required', 'string'],
                'emergency_contact_name' => ['required', 'string', 'max:255'],
                'emergency_contact_phone' => ['required', 'string', 'max:20'],
                'medical_history' => ['nullable', 'string'],
                'current_medications' => ['nullable', 'string'],
                'allergies' => ['nullable', 'string'],
                'insurance_provider' => ['nullable', 'string', 'max:255'],
                'insurance_number' => ['nullable', 'string', 'max:100'],

                // Package assignment fields (for patients)
                'therapy_package_id' => ['nullable', 'exists:therapy_packages,id'],
                'assigned_doctor_id' => ['nullable', 'exists:users,id'],
                'package_start_date' => ['nullable', 'date', 'after_or_equal:today'],
                'discount_given' => ['nullable', 'numeric', 'min:0'],
                'number_of_installments' => ['nullable', 'integer', 'min:1', 'max:24'],
                'payment_type' => ['nullable', 'in:cash,card,bank_transfer,cheque,online,installments'],
                'amount_paid' => ['nullable', 'numeric', 'min:0'],
                'payment_status' => ['nullable', 'in:pending,partial,paid'],
                'payment_notes' => ['nullable', 'string', 'max:1000'],
                'package_notes' => ['nullable', 'string', 'max:1000'],
            ]);
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Name is required',
            'email.required' => 'Email is required',
            'email.unique' => 'This email is already registered',
            'password.required' => 'Password is required',
            'role.required' => 'Role is required',
            'branch_id.required' => 'Branch selection is required',
            'specialization.required' => 'Specialization is required for doctors',
            'license_number.required' => 'License number is required for doctors',
            'license_number.unique' => 'This medical license number is already registered',
            'date_of_birth.required' => 'Date of birth is required for patients',
            'gender.required' => 'Gender is required for patients',
            'emergency_contact_name.required' => 'Emergency contact name is required',
            'emergency_contact_phone.required' => 'Emergency contact phone is required',
        ];
    }
}