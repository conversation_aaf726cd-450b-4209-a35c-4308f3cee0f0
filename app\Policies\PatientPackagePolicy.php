<?php

namespace App\Policies;

use App\Models\PatientPackage;
use App\Models\User;

class PatientPackagePolicy
{
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'branch_head', 'doctor']);
    }

    public function view(User $user, PatientPackage $patientPackage): bool
    {
        // Admin can view all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Branch head can view packages for their branch patients
        if ($user->hasRole('branch_head')) {
            $branchId = $user->branchHeadProfile?->branch_id;
            $patientBranchId = $patientPackage->patient->patientProfile?->branch_id;
            
            return $branchId && $patientBranchId && $branchId === $patientBranchId;
        }

        // Doctor can view packages they are assigned to or patients in their branch
        if ($user->hasRole('doctor')) {
            // Can view if they are the assigned doctor
            if ($patientPackage->assigned_doctor_id === $user->id) {
                return true;
            }

            // Can view if patient is in their branch
            $doctorBranchId = $user->doctorProfile?->branch_id;
            $patientBranchId = $patientPackage->patient->patientProfile?->branch_id;
            
            return $doctorBranchId && $patientBranchId && $doctorBranchId === $patientBranchId;
        }

        // Patient can view their own packages
        if ($user->hasRole('patient')) {
            return $patientPackage->patient_id === $user->id;
        }

        return false;
    }

    public function create(User $user): bool
    {
        return $user->hasAnyRole(['admin', 'branch_head', 'doctor']);
    }

    public function update(User $user, PatientPackage $patientPackage): bool
    {
        // Admin can update all
        if ($user->hasRole('admin')) {
            return true;
        }

        // Branch head can update packages for their branch patients
        if ($user->hasRole('branch_head')) {
            $branchId = $user->branchHeadProfile?->branch_id;
            $patientBranchId = $patientPackage->patient->patientProfile?->branch_id;
            
            return $branchId && $patientBranchId && $branchId === $patientBranchId;
        }

        // Doctor can update packages they are assigned to
        if ($user->hasRole('doctor')) {
            return $patientPackage->assigned_doctor_id === $user->id;
        }

        return false;
    }

    public function delete(User $user, PatientPackage $patientPackage): bool
    {
        // Only admin and branch head can delete packages
        if ($user->hasRole('admin')) {
            return true;
        }

        if ($user->hasRole('branch_head')) {
            $branchId = $user->branchHeadProfile?->branch_id;
            $patientBranchId = $patientPackage->patient->patientProfile?->branch_id;
            
            return $branchId && $patientBranchId && $branchId === $patientBranchId;
        }

        return false;
    }

    public function assignDoctor(User $user, PatientPackage $patientPackage): bool
    {
        return $user->hasAnyRole(['admin', 'branch_head']);
    }

    public function updatePayment(User $user, PatientPackage $patientPackage): bool
    {
        return $user->hasAnyRole(['admin', 'branch_head']);
    }

    public function viewSessions(User $user, PatientPackage $patientPackage): bool
    {
        return $this->view($user, $patientPackage);
    }

    public function manageSessions(User $user, PatientPackage $patientPackage): bool
    {
        // Admin can manage all sessions
        if ($user->hasRole('admin')) {
            return true;
        }

        // Branch head can manage sessions for their branch
        if ($user->hasRole('branch_head')) {
            $branchId = $user->branchHeadProfile?->branch_id;
            $patientBranchId = $patientPackage->patient->patientProfile?->branch_id;
            
            return $branchId && $patientBranchId && $branchId === $patientBranchId;
        }

        // Doctor can manage sessions they are assigned to conduct
        if ($user->hasRole('doctor')) {
            return $patientPackage->assigned_doctor_id === $user->id;
        }

        return false;
    }
}
