<?php

namespace App\Repositories;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository implements UserRepositoryInterface
{
    public function all()
    {
        return User::with(['roles', 'doctor<PERSON>rofile', 'patientProfile'])->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return User::with(['roles', 'doctorProfile', 'patientProfile'])
            ->latest()
            ->paginate($perPage);
    }

    public function find(int $id): ?User
    {
        return User::with(['roles', 'doctorProfile', 'patientProfile'])->find($id);
    }

    public function findByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    public function create(array $data): User
    {
        return User::create($data);
    }

    public function update(int $id, array $data): User
    {
        $user = User::findOrFail($id);
        $user->update($data);
        return $user->fresh();
    }

    public function delete(int $id): bool
    {
        return User::destroy($id) > 0;
    }

    public function getByRole(string $role)
    {
        return User::role($role)->with(['doctorProfile', 'patientProfile', 'managedBranch'])->get();
    }

    public function getActiveUsers()
    {
        return User::where('is_active', true)->get();
    }

    public function getDoctorsByBranch(int $branchId)
    {
        return User::role('doctor')
            ->whereHas('doctorProfile', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->with('doctorProfile')
            ->get();
    }

    public function getPatientsByBranch(int $branchId)
    {
        return User::role('patient')
            ->whereHas('patientProfile', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->with('patientProfile')
            ->get();
    }

    public function searchUsers(string $search, ?string $role = null)
    {
        $query = User::where(function ($q) use ($search) {
            $q->where('name', 'like', "%$search%")
              ->orWhere('email', 'like', "%$search%");
        });

        if ($role) {
            $query->role($role);
        }

        return $query->with(['roles', 'doctorProfile', 'patientProfile'])->get();
    }
}