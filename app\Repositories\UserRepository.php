<?php

namespace App\Repositories;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

class UserRepository implements UserRepositoryInterface
{
    public function all()
    {
        return User::with(['roles', 'doctor<PERSON>rofile', 'patientProfile'])->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return User::with(['roles', 'doctorProfile', 'patientProfile'])
            ->latest()
            ->paginate($perPage);
    }

    public function find(int $id): ?User
    {
        return User::with(['roles', 'doctorProfile', 'patientProfile'])->find($id);
    }

    public function findByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    public function create(array $data): User
    {
        return User::create($data);
    }

    public function update(int $id, array $data): User
    {
        $user = User::findOrFail($id);
        $user->update($data);
        return $user->fresh();
    }

    public function delete(int $id): bool
    {
        return User::destroy($id) > 0;
    }

    public function getByRole(string $role)
    {
        return User::role($role)->with(['doctorProfile', 'patientProfile', 'managedBranch'])->get();
    }

    public function getActiveUsers()
    {
        return User::with(['roles'])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
    }

    public function getDoctorsByBranch(int $branchId)
    {
        return User::role('doctor')
            ->whereHas('doctorProfile', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->with('doctorProfile')
            ->get();
    }

    public function getPatientsByBranch(int $branchId)
    {
        return User::role('patient')
            ->whereHas('patientProfile', function ($query) use ($branchId) {
                $query->where('branch_id', $branchId);
            })
            ->with('patientProfile')
            ->get();
    }

    public function searchUsers(string $search, ?string $role = null)
    {
        $query = User::where(function ($q) use ($search) {
            $q->where('name', 'like', "%$search%")
              ->orWhere('email', 'like', "%$search%");
        });

        if ($role) {
            $query->role($role);
        }

        return $query->with(['roles', 'doctorProfile', 'patientProfile'])->get();
    }

    public function getUsersWithProfiles()
    {
        return User::with([
            'roles:id,name',
            'doctorProfile:user_id,specialization,consultation_fee,is_active',
            'patientProfile:user_id,date_of_birth,gender,is_active',
            'managedBranch:id,name'
        ])
        ->where('is_active', true)
        ->orderBy('name')
        ->get();
    }

    public function getDoctorsWithSchedule(int $branchId = null)
    {
        $query = User::with([
            'doctorProfile:user_id,branch_id,specialization,consultation_fee,available_days,start_time,end_time,is_active',
            'doctorProfile.branch:id,name'
        ])
        ->whereHas('doctorProfile', function ($q) use ($branchId) {
            $q->where('is_active', true);
            if ($branchId) {
                $q->where('branch_id', $branchId);
            }
        })
        ->where('is_active', true);

        return $query->orderBy('name')->get();
    }

    public function getPatientsWithHistory(int $branchId = null)
    {
        $query = User::with([
            'patientProfile:user_id,branch_id,date_of_birth,gender,medical_history,is_active',
            'patientProfile.branch:id,name',
            'patientAppointments' => function ($q) {
                $q->select('id,patient_id,appointment_date,status')
                  ->latest('appointment_date')
                  ->limit(5);
            }
        ])
        ->whereHas('patientProfile', function ($q) use ($branchId) {
            $q->where('is_active', true);
            if ($branchId) {
                $q->where('branch_id', $branchId);
            }
        })
        ->where('is_active', true);

        return $query->orderBy('name')->get();
    }

    public function getUserStats(int $branchId = null)
    {
        $query = User::query();

        if ($branchId) {
            $query->where(function ($q) use ($branchId) {
                $q->whereHas('doctorProfile', function ($dq) use ($branchId) {
                    $dq->where('branch_id', $branchId);
                })
                ->orWhereHas('patientProfile', function ($pq) use ($branchId) {
                    $pq->where('branch_id', $branchId);
                });
            });
        }

        return $query->selectRaw('
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_users
        ')->first();
    }
}