<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PackageInstallment extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_package_id',
        'installment_number',
        'installment_date',
        'installment_amount',
        'due_date',
        'paid_date',
        'amount_paid',
        'status',
        'payment_method',
        'transaction_reference',
        'payment_notes',
        'received_by',
    ];

    protected $casts = [
        'installment_date' => 'date',
        'due_date' => 'date',
        'paid_date' => 'date',
        'installment_amount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
    ];

    // Relationships
    public function patientPackage()
    {
        return $this->belongsTo(PatientPackage::class);
    }

    public function receivedBy()
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function ($q) {
                        $q->where('status', 'pending')
                          ->where('due_date', '<', now()->toDateString());
                    });
    }

    public function scopeDueToday($query)
    {
        return $query->where('due_date', now()->toDateString())
                    ->where('status', 'pending');
    }

    public function scopeDueThisWeek($query)
    {
        return $query->whereBetween('due_date', [
                        now()->startOfWeek()->toDateString(),
                        now()->endOfWeek()->toDateString()
                    ])
                    ->where('status', 'pending');
    }

    // Accessors
    public function getFormattedInstallmentAmountAttribute(): string
    {
        return '$' . number_format($this->installment_amount, 2);
    }

    public function getFormattedAmountPaidAttribute(): string
    {
        return '$' . number_format($this->amount_paid, 2);
    }

    public function getRemainingAmountAttribute(): float
    {
        return $this->installment_amount - $this->amount_paid;
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return '$' . number_format($this->remaining_amount, 2);
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Pending',
            'paid' => 'Paid',
            'overdue' => 'Overdue',
            'partial' => 'Partially Paid',
            default => ucfirst($this->status),
        };
    }

    public function getPaymentMethodLabelAttribute(): string
    {
        return match($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Card',
            'bank_transfer' => 'Bank Transfer',
            'cheque' => 'Cheque',
            'online' => 'Online Payment',
            default => $this->payment_method ? ucfirst($this->payment_method) : 'Not Specified',
        };
    }

    public function getDaysOverdueAttribute(): int
    {
        if ($this->status === 'overdue' || ($this->status === 'pending' && $this->due_date < now()->toDateString())) {
            return now()->diffInDays($this->due_date);
        }
        return 0;
    }

    public function getDaysUntilDueAttribute(): int
    {
        if ($this->status === 'pending' && $this->due_date >= now()->toDateString()) {
            return $this->due_date->diffInDays(now());
        }
        return 0;
    }

    // Methods
    public function isOverdue(): bool
    {
        return $this->status === 'overdue' || 
               ($this->status === 'pending' && $this->due_date < now()->toDateString());
    }

    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    public function isPartiallyPaid(): bool
    {
        return $this->status === 'partial' || 
               ($this->amount_paid > 0 && $this->amount_paid < $this->installment_amount);
    }

    public function markAsPaid(float $amount, string $paymentMethod = null, string $transactionRef = null, int $receivedBy = null): void
    {
        $this->update([
            'amount_paid' => $amount,
            'paid_date' => now()->toDateString(),
            'status' => $amount >= $this->installment_amount ? 'paid' : 'partial',
            'payment_method' => $paymentMethod,
            'transaction_reference' => $transactionRef,
            'received_by' => $receivedBy ?? auth()->id(),
        ]);
    }

    public function markAsOverdue(): void
    {
        if ($this->status === 'pending' && $this->due_date < now()->toDateString()) {
            $this->update(['status' => 'overdue']);
        }
    }

    public function addPartialPayment(float $amount, string $paymentMethod = null, string $transactionRef = null): void
    {
        $newAmountPaid = $this->amount_paid + $amount;
        $this->update([
            'amount_paid' => $newAmountPaid,
            'status' => $newAmountPaid >= $this->installment_amount ? 'paid' : 'partial',
            'payment_method' => $paymentMethod ?? $this->payment_method,
            'transaction_reference' => $transactionRef ?? $this->transaction_reference,
            'received_by' => auth()->id(),
        ]);

        if ($newAmountPaid >= $this->installment_amount) {
            $this->update(['paid_date' => now()->toDateString()]);
        }
    }
}
