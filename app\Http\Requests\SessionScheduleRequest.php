<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\SessionSchedule;
use App\Models\User;

class SessionScheduleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->hasAnyRole(['admin', 'branch_head', 'doctor']);
    }

    public function rules(): array
    {
        return [
            'patient_package_id' => ['required', 'exists:patient_packages,id'],
            'patient_id' => ['required', 'exists:users,id'],
            'doctor_id' => ['required', 'exists:users,id'],
            'session_number' => ['required', 'integer', 'min:1'],
            'scheduled_date' => ['required', 'date', 'after_or_equal:today'],
            'scheduled_time' => ['required', 'date_format:H:i'],
            'duration_minutes' => ['required', 'integer', 'min:15', 'max:240'],
            'session_type' => ['required', 'in:individual,group,assessment,follow_up'],
            'location' => ['nullable', 'string', 'max:100'],
            'session_notes' => ['nullable', 'string', 'max:1000'],
            'preparation_notes' => ['nullable', 'string', 'max:1000'],
            'session_goals' => ['nullable', 'array'],
            'session_goals.*' => ['string', 'max:200'],
        ];
    }

    public function messages(): array
    {
        return [
            'patient_package_id.required' => 'Patient package is required',
            'patient_package_id.exists' => 'Selected patient package is invalid',
            'patient_id.required' => 'Patient is required',
            'patient_id.exists' => 'Selected patient is invalid',
            'doctor_id.required' => 'Doctor is required',
            'doctor_id.exists' => 'Selected doctor is invalid',
            'session_number.required' => 'Session number is required',
            'session_number.min' => 'Session number must be at least 1',
            'scheduled_date.required' => 'Scheduled date is required',
            'scheduled_date.after_or_equal' => 'Scheduled date cannot be in the past',
            'scheduled_time.required' => 'Scheduled time is required',
            'scheduled_time.date_format' => 'Invalid time format',
            'duration_minutes.required' => 'Session duration is required',
            'duration_minutes.min' => 'Session duration must be at least 15 minutes',
            'duration_minutes.max' => 'Session duration cannot exceed 4 hours',
            'session_type.required' => 'Session type is required',
            'session_type.in' => 'Invalid session type selected',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate patient belongs to the package
            if ($this->patient_package_id && $this->patient_id) {
                $package = \App\Models\PatientPackage::find($this->patient_package_id);
                if ($package && $package->patient_id != $this->patient_id) {
                    $validator->errors()->add('patient_id', 'Patient does not match the selected package');
                }
            }

            // Validate doctor role
            if ($this->doctor_id) {
                $doctor = User::find($this->doctor_id);
                if ($doctor && !$doctor->hasRole('doctor')) {
                    $validator->errors()->add('doctor_id', 'Selected user is not a doctor');
                }
            }

            // Validate patient role
            if ($this->patient_id) {
                $patient = User::find($this->patient_id);
                if ($patient && !$patient->hasRole('patient')) {
                    $validator->errors()->add('patient_id', 'Selected user is not a patient');
                }
            }

            // Check for scheduling conflicts
            if ($this->doctor_id && $this->scheduled_date && $this->scheduled_time && $this->duration_minutes) {
                $startTime = \Carbon\Carbon::parse($this->scheduled_date . ' ' . $this->scheduled_time);
                $endTime = $startTime->copy()->addMinutes($this->duration_minutes);

                $conflictingSession = SessionSchedule::where('doctor_id', $this->doctor_id)
                    ->where('status', 'scheduled')
                    ->whereDate('scheduled_date', $this->scheduled_date)
                    ->where(function ($query) use ($startTime, $endTime) {
                        $query->whereBetween('scheduled_time', [
                            $startTime->format('H:i:s'),
                            $endTime->format('H:i:s')
                        ]);
                    })
                    ->when($this->route('session'), function ($query) {
                        return $query->where('id', '!=', $this->route('session')->id);
                    })
                    ->exists();

                if ($conflictingSession) {
                    $validator->errors()->add('scheduled_time', 'Doctor has a conflicting appointment at this time');
                }
            }

            // Validate session number sequence
            if ($this->patient_package_id && $this->session_number) {
                $package = \App\Models\PatientPackage::find($this->patient_package_id);
                if ($package) {
                    $maxSessionNumber = SessionSchedule::where('patient_package_id', $this->patient_package_id)
                        ->when($this->route('session'), function ($query) {
                            return $query->where('id', '!=', $this->route('session')->id);
                        })
                        ->max('session_number') ?? 0;

                    if ($this->session_number > $maxSessionNumber + 1) {
                        $validator->errors()->add('session_number', 'Session number must be sequential');
                    }

                    if ($this->session_number > $package->therapyPackage->total_sessions) {
                        $validator->errors()->add('session_number', 'Session number exceeds package total sessions');
                    }
                }
            }

            // Validate business hours (9 AM to 6 PM)
            if ($this->scheduled_time) {
                $time = \Carbon\Carbon::parse($this->scheduled_time);
                $startHour = 9; // 9 AM
                $endHour = 18; // 6 PM

                if ($time->hour < $startHour || $time->hour >= $endHour) {
                    $validator->errors()->add('scheduled_time', 'Sessions can only be scheduled between 9:00 AM and 6:00 PM');
                }
            }

            // Validate weekend scheduling (optional business rule)
            if ($this->scheduled_date) {
                $date = \Carbon\Carbon::parse($this->scheduled_date);
                if ($date->isWeekend()) {
                    // You can make this a warning or error based on business rules
                    // $validator->errors()->add('scheduled_date', 'Weekend scheduling requires special approval');
                }
            }
        });
    }
}
