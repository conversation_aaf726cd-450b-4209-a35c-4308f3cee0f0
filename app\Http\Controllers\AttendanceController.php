<?php

namespace App\Http\Controllers;

use App\Models\SessionSchedule;
use App\Models\SessionAttendance;
use App\Models\PatientPackage;
use App\Models\User;
use App\Services\AttendanceService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class AttendanceController extends Controller
{
    protected $attendanceService;

    public function __construct(AttendanceService $attendanceService)
    {
        $this->attendanceService = $attendanceService;
    }

    /**
     * Display the attendance dashboard
     */
    public function dashboard(Request $request)
    {
        $user = auth()->user();
        $date = $request->get('date', today()->toDateString());
        
        // Get today's sessions based on user role
        $sessionsQuery = SessionSchedule::with(['patient', 'doctor', 'patientPackage.therapyPackage', 'attendance'])
            ->whereDate('scheduled_date', $date);

        if ($user->hasRole('doctor')) {
            $sessionsQuery->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            $sessionsQuery->where('patient_id', $user->id);
        }
        // Admin and branch_head can see all sessions

        $sessions = $sessionsQuery->orderBy('scheduled_time')->get();

        // Get attendance statistics
        $stats = $this->attendanceService->getDashboardStats($date, $user);

        return Inertia::render('Attendance/Dashboard', [
            'sessions' => $sessions,
            'stats' => $stats,
            'selectedDate' => $date,
            'canMarkAttendance' => $user->hasAnyRole(['admin', 'branch_head', 'doctor']),
        ]);
    }

    /**
     * Quick check-in for a session
     */
    public function quickCheckIn(SessionSchedule $session)
    {
        $this->authorize('markAttendance', $session);

        try {
            $attendance = $this->attendanceService->markAttendance($session, [
                'attendance_status' => 'present',
                'check_in_time' => now(),
                'marking_method' => 'manual',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Patient checked in successfully',
                'attendance' => $attendance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Mark attendance with detailed information
     */
    public function markAttendance(Request $request, SessionSchedule $session)
    {
        $this->authorize('markAttendance', $session);

        $validated = $request->validate([
            'attendance_status' => 'required|in:present,absent,late,partial,excused',
            'check_in_time' => 'nullable|date',
            'late_reason' => 'nullable|string|max:500',
            'absence_reason' => 'nullable|string|max:500',
            'is_excused' => 'boolean',
            'excuse_reason' => 'nullable|string|max:500',
        ]);

        try {
            $attendance = $this->attendanceService->markAttendance($session, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Attendance marked successfully',
                'attendance' => $attendance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Complete a session with detailed information
     */
    public function completeSession(Request $request, SessionSchedule $session)
    {
        $this->authorize('completeSession', $session);

        $validated = $request->validate([
            'check_out_time' => 'nullable|date',
            'session_completion' => 'required|in:completed,partial,interrupted,cancelled',
            'participation_score' => 'nullable|integer|min:1|max:10',
            'session_summary' => 'nullable|string|max:2000',
            'homework_assigned' => 'nullable|string|max:1000',
            'next_session_goals' => 'nullable|string|max:1000',
            'mood_before' => 'nullable|in:excellent,good,neutral,poor,very_poor',
            'mood_after' => 'nullable|in:excellent,good,neutral,poor,very_poor',
            'doctor_notes' => 'nullable|string|max:2000',
            'follow_up_instructions' => 'nullable|string|max:1000',
            'requires_follow_up' => 'boolean',
            'next_recommended_date' => 'nullable|date|after:today',
            'session_fee' => 'nullable|numeric|min:0',
        ]);

        try {
            $attendance = $this->attendanceService->completeSession($session, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Session completed successfully',
                'attendance' => $attendance,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get attendance history for a patient
     */
    public function patientHistory(User $patient, Request $request)
    {
        $this->authorize('viewPatientAttendance', $patient);

        $attendances = SessionAttendance::with(['sessionSchedule', 'doctor', 'patientPackage.therapyPackage'])
            ->where('patient_id', $patient->id)
            ->when($request->package_id, function ($query, $packageId) {
                return $query->where('patient_package_id', $packageId);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $stats = $this->attendanceService->getPatientAttendanceStats($patient->id);

        return Inertia::render('Attendance/PatientHistory', [
            'patient' => $patient,
            'attendances' => $attendances,
            'stats' => $stats,
            'packages' => $patient->patientPackages()->with('therapyPackage')->get(),
        ]);
    }

    /**
     * Get attendance reports
     */
    public function reports(Request $request)
    {
        $this->authorize('viewAttendanceReports');

        $filters = $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'doctor_id' => 'nullable|exists:users,id',
            'patient_id' => 'nullable|exists:users,id',
            'package_id' => 'nullable|exists:patient_packages,id',
            'attendance_status' => 'nullable|in:present,absent,late,partial,excused',
        ]);

        $reports = $this->attendanceService->generateReports($filters);

        return Inertia::render('Attendance/Reports', [
            'reports' => $reports,
            'filters' => $filters,
            'doctors' => User::role('doctor')->get(['id', 'name']),
            'patients' => User::role('patient')->get(['id', 'name']),
        ]);
    }

    /**
     * Bulk mark attendance for multiple sessions
     */
    public function bulkMarkAttendance(Request $request)
    {
        $this->authorize('bulkMarkAttendance');

        $validated = $request->validate([
            'sessions' => 'required|array',
            'sessions.*.session_id' => 'required|exists:session_schedules,id',
            'sessions.*.attendance_status' => 'required|in:present,absent,late,partial,excused',
            'sessions.*.check_in_time' => 'nullable|date',
            'sessions.*.absence_reason' => 'nullable|string|max:500',
        ]);

        try {
            $results = $this->attendanceService->bulkMarkAttendance($validated['sessions']);

            return response()->json([
                'success' => true,
                'message' => 'Bulk attendance marked successfully',
                'results' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get upcoming sessions for scheduling
     */
    public function upcomingSessions(Request $request)
    {
        $user = auth()->user();
        $days = $request->get('days', 7);

        $sessionsQuery = SessionSchedule::with(['patient', 'doctor', 'patientPackage.therapyPackage'])
            ->where('status', 'scheduled')
            ->whereBetween('scheduled_date', [today(), today()->addDays($days)]);

        if ($user->hasRole('doctor')) {
            $sessionsQuery->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            $sessionsQuery->where('patient_id', $user->id);
        }

        $sessions = $sessionsQuery->orderBy('scheduled_date')
                                 ->orderBy('scheduled_time')
                                 ->get();

        return response()->json([
            'sessions' => $sessions,
        ]);
    }

    /**
     * Export attendance data
     */
    public function export(Request $request)
    {
        $this->authorize('exportAttendance');

        $filters = $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'format' => 'required|in:csv,excel,pdf',
            'doctor_id' => 'nullable|exists:users,id',
            'patient_id' => 'nullable|exists:users,id',
        ]);

        return $this->attendanceService->exportAttendanceData($filters);
    }
}
