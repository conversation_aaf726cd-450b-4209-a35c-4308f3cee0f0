<?php

namespace App\Services;

use App\Models\SessionSchedule;
use App\Models\SessionAttendance;
use App\Models\PatientPackage;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AttendanceService
{
    /**
     * Mark attendance for a session
     */
    public function markAttendance(SessionSchedule $session, array $data): SessionAttendance
    {
        if ($session->attendance) {
            throw new \Exception('Attendance already marked for this session');
        }

        if (!$session->canMarkAttendance()) {
            throw new \Exception('Cannot mark attendance for this session');
        }

        return DB::transaction(function () use ($session, $data) {
            // Create attendance record
            $attendance = SessionAttendance::create([
                'session_schedule_id' => $session->id,
                'patient_id' => $session->patient_id,
                'doctor_id' => $session->doctor_id,
                'patient_package_id' => $session->patient_package_id,
                'attendance_status' => $data['attendance_status'],
                'check_in_time' => $data['check_in_time'] ?? now(),
                'marked_by' => auth()->id(),
                'marked_at' => now(),
                'marking_method' => $data['marking_method'] ?? 'manual',
                'late_minutes' => $this->calculateLateMinutes($session, $data['check_in_time'] ?? now()),
                'late_reason' => $data['late_reason'] ?? null,
                'absence_reason' => $data['absence_reason'] ?? null,
                'is_excused' => $data['is_excused'] ?? false,
                'excuse_reason' => $data['excuse_reason'] ?? null,
            ]);

            // Update session status if present
            if (in_array($data['attendance_status'], ['present', 'late', 'partial'])) {
                $session->update(['status' => 'completed']);
                
                // Update package progress
                $this->updatePackageProgress($session->patientPackage, true);
            } else {
                // Mark as no show if absent without excuse
                if ($data['attendance_status'] === 'absent' && !($data['is_excused'] ?? false)) {
                    $session->markAsNoShow();
                }
            }

            return $attendance;
        });
    }

    /**
     * Complete a session with detailed information
     */
    public function completeSession(SessionSchedule $session, array $data): SessionAttendance
    {
        $attendance = $session->attendance;
        
        if (!$attendance) {
            throw new \Exception('Attendance must be marked before completing session');
        }

        return DB::transaction(function () use ($session, $attendance, $data) {
            // Update attendance with session completion data
            $attendance->update([
                'check_out_time' => $data['check_out_time'] ?? now(),
                'session_completion' => $data['session_completion'],
                'participation_score' => $data['participation_score'] ?? null,
                'session_summary' => $data['session_summary'] ?? null,
                'homework_assigned' => $data['homework_assigned'] ?? null,
                'next_session_goals' => $data['next_session_goals'] ?? null,
                'mood_before' => $data['mood_before'] ?? null,
                'mood_after' => $data['mood_after'] ?? null,
                'doctor_notes' => $data['doctor_notes'] ?? null,
                'follow_up_instructions' => $data['follow_up_instructions'] ?? null,
                'requires_follow_up' => $data['requires_follow_up'] ?? false,
                'next_recommended_date' => $data['next_recommended_date'] ?? null,
                'session_fee' => $data['session_fee'] ?? null,
                'actual_duration_minutes' => $attendance->calculateActualDuration(),
            ]);

            // Mark session as completed
            $session->markAsCompleted();

            // Schedule next session if recommended
            if ($data['requires_follow_up'] && $data['next_recommended_date']) {
                $this->scheduleFollowUpSession($session, $data['next_recommended_date']);
            }

            return $attendance;
        });
    }

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(string $date, User $user): array
    {
        $dateObj = Carbon::parse($date);
        
        $baseQuery = SessionSchedule::whereDate('scheduled_date', $date);
        
        if ($user->hasRole('doctor')) {
            $baseQuery->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            $baseQuery->where('patient_id', $user->id);
        }

        $totalSessions = $baseQuery->count();
        $completedSessions = $baseQuery->where('status', 'completed')->count();
        $cancelledSessions = $baseQuery->where('status', 'cancelled')->count();
        $noShowSessions = $baseQuery->where('status', 'no_show')->count();

        $attendanceQuery = SessionAttendance::whereDate('created_at', $date);
        
        if ($user->hasRole('doctor')) {
            $attendanceQuery->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            $attendanceQuery->where('patient_id', $user->id);
        }

        $presentCount = $attendanceQuery->where('attendance_status', 'present')->count();
        $absentCount = $attendanceQuery->where('attendance_status', 'absent')->count();
        $lateCount = $attendanceQuery->where('attendance_status', 'late')->count();

        return [
            'total_sessions' => $totalSessions,
            'completed_sessions' => $completedSessions,
            'cancelled_sessions' => $cancelledSessions,
            'no_show_sessions' => $noShowSessions,
            'present_count' => $presentCount,
            'absent_count' => $absentCount,
            'late_count' => $lateCount,
            'attendance_rate' => $totalSessions > 0 ? round(($presentCount / $totalSessions) * 100, 1) : 0,
        ];
    }

    /**
     * Get patient attendance statistics
     */
    public function getPatientAttendanceStats(int $patientId): array
    {
        $totalSessions = SessionAttendance::where('patient_id', $patientId)->count();
        $presentSessions = SessionAttendance::where('patient_id', $patientId)->present()->count();
        $absentSessions = SessionAttendance::where('patient_id', $patientId)->absent()->count();
        $lateSessions = SessionAttendance::where('patient_id', $patientId)->late()->count();

        $attendanceRate = $totalSessions > 0 ? round(($presentSessions / $totalSessions) * 100, 1) : 0;
        
        // Get monthly attendance for the last 6 months
        $monthlyAttendance = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $monthSessions = SessionAttendance::where('patient_id', $patientId)
                ->whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->count();
            
            $monthPresent = SessionAttendance::where('patient_id', $patientId)
                ->whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->present()
                ->count();

            $monthlyAttendance[] = [
                'month' => $month->format('M Y'),
                'total' => $monthSessions,
                'present' => $monthPresent,
                'rate' => $monthSessions > 0 ? round(($monthPresent / $monthSessions) * 100, 1) : 0,
            ];
        }

        return [
            'total_sessions' => $totalSessions,
            'present_sessions' => $presentSessions,
            'absent_sessions' => $absentSessions,
            'late_sessions' => $lateSessions,
            'attendance_rate' => $attendanceRate,
            'monthly_attendance' => $monthlyAttendance,
        ];
    }

    /**
     * Bulk mark attendance for multiple sessions
     */
    public function bulkMarkAttendance(array $sessions): array
    {
        $results = [];

        DB::transaction(function () use ($sessions, &$results) {
            foreach ($sessions as $sessionData) {
                try {
                    $session = SessionSchedule::findOrFail($sessionData['session_id']);
                    $attendance = $this->markAttendance($session, $sessionData);
                    
                    $results[] = [
                        'session_id' => $session->id,
                        'success' => true,
                        'attendance_id' => $attendance->id,
                    ];
                } catch (\Exception $e) {
                    $results[] = [
                        'session_id' => $sessionData['session_id'],
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];
                }
            }
        });

        return $results;
    }

    /**
     * Calculate late minutes
     */
    private function calculateLateMinutes(SessionSchedule $session, $checkInTime): int
    {
        $scheduledDateTime = $session->scheduled_date_time;
        $checkIn = Carbon::parse($checkInTime);
        
        return $checkIn->gt($scheduledDateTime) ? $checkIn->diffInMinutes($scheduledDateTime) : 0;
    }

    /**
     * Update package progress
     */
    private function updatePackageProgress(PatientPackage $package, bool $sessionCompleted): void
    {
        if ($sessionCompleted) {
            $package->increment('sessions_completed');
            $package->decrement('sessions_remaining');
            
            // Check if package is completed
            if ($package->sessions_remaining <= 0) {
                $package->update(['status' => 'completed']);
            }
        }
    }

    /**
     * Schedule follow-up session
     */
    private function scheduleFollowUpSession(SessionSchedule $originalSession, string $recommendedDate): SessionSchedule
    {
        return SessionSchedule::create([
            'patient_package_id' => $originalSession->patient_package_id,
            'patient_id' => $originalSession->patient_id,
            'doctor_id' => $originalSession->doctor_id,
            'scheduled_by' => auth()->id(),
            'session_number' => $originalSession->session_number + 1,
            'scheduled_date' => $recommendedDate,
            'scheduled_time' => $originalSession->scheduled_time,
            'duration_minutes' => $originalSession->duration_minutes,
            'session_type' => 'follow_up',
            'location' => $originalSession->location,
        ]);
    }

    /**
     * Generate attendance reports
     */
    public function generateReports(array $filters): array
    {
        // Implementation for generating comprehensive attendance reports
        // This would include various metrics, charts, and analytics
        return [
            'summary' => $this->getReportSummary($filters),
            'trends' => $this->getAttendanceTrends($filters),
            'doctor_performance' => $this->getDoctorPerformance($filters),
            'patient_progress' => $this->getPatientProgress($filters),
        ];
    }

    private function getReportSummary(array $filters): array
    {
        // Implementation for report summary
        return [];
    }

    private function getAttendanceTrends(array $filters): array
    {
        // Implementation for attendance trends
        return [];
    }

    private function getDoctorPerformance(array $filters): array
    {
        // Implementation for doctor performance metrics
        return [];
    }

    private function getPatientProgress(array $filters): array
    {
        // Implementation for patient progress tracking
        return [];
    }

    /**
     * Export attendance data
     */
    public function exportAttendanceData(array $filters)
    {
        $attendances = SessionAttendance::with(['patient', 'doctor', 'sessionSchedule'])
            ->when($filters['date_from'], function ($query, $date) {
                return $query->whereDate('created_at', '>=', $date);
            })
            ->when($filters['date_to'], function ($query, $date) {
                return $query->whereDate('created_at', '<=', $date);
            })
            ->when($filters['doctor_id'], function ($query, $doctorId) {
                return $query->where('doctor_id', $doctorId);
            })
            ->when($filters['patient_id'], function ($query, $patientId) {
                return $query->where('patient_id', $patientId);
            })
            ->get();

        // For now, return JSON data - implement actual export logic based on format
        return response()->json(['data' => $attendances]);
    }
}
