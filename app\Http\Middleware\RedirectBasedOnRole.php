<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RedirectBasedOnRole
{
    public function handle(Request $request, Closure $next): Response
    {
        if (auth()->check()) {
            $user = auth()->user();

            // Redirect all authenticated users to the unified dashboard
            if ($request->is('/')) {
                return redirect('/dashboard');
            }
        }

        return $next($request);
    }
}