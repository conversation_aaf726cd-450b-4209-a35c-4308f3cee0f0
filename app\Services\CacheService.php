<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;

class CacheService
{
    const CACHE_TTL = 3600; // 1 hour

    public function remember(string $key, callable $callback, int $ttl = self::CACHE_TTL)
    {
        return Cache::remember($key, $ttl, $callback);
    }

    public function forget(string $key): bool
    {
        return Cache::forget($key);
    }

    public function forgetPattern(string $pattern): void
    {
        // Only use Redis pattern deletion if <PERSON><PERSON> is the cache driver
        if (config('cache.default') === 'redis') {
            $keys = Cache::getRedis()->keys($pattern);
            if (!empty($keys)) {
                Cache::getRedis()->del($keys);
            }
        }
        // For other cache drivers, we'll need to track keys manually or skip pattern deletion
    }

    // Branch-related cache keys
    public function getBranchStatsKey(int $branchId): string
    {
        return "branch_stats_{$branchId}";
    }

    public function getActiveBranchesKey(): string
    {
        return 'active_branches';
    }

    // User-related cache keys
    public function getDoctorsByBranchKey(int $branchId): string
    {
        return "doctors_branch_{$branchId}";
    }

    public function getPatientsByBranchKey(int $branchId): string
    {
        return "patients_branch_{$branchId}";
    }

    // Appointment-related cache keys
    public function getTodaysAppointmentsKey(): string
    {
        return 'todays_appointments_' . now()->format('Y-m-d');
    }

    public function getUpcomingAppointmentsKey(): string
    {
        return 'upcoming_appointments';
    }

    public function getDoctorAppointmentsKey(int $doctorId): string
    {
        return "doctor_appointments_{$doctorId}";
    }

    // Clear related caches when data changes
    public function clearBranchCaches(int $branchId): void
    {
        $this->forget($this->getBranchStatsKey($branchId));
        $this->forget($this->getActiveBranchesKey());
        $this->forget($this->getDoctorsByBranchKey($branchId));
        $this->forget($this->getPatientsByBranchKey($branchId));
    }

    public function clearAppointmentCaches(): void
    {
        $this->forget($this->getTodaysAppointmentsKey());
        $this->forget($this->getUpcomingAppointmentsKey());
        $this->forgetPattern('doctor_appointments_*');
    }

    public function clearUserCaches(int $branchId = null): void
    {
        if ($branchId) {
            $this->forget($this->getDoctorsByBranchKey($branchId));
            $this->forget($this->getPatientsByBranchKey($branchId));
        } else {
            $this->forgetPattern('doctors_branch_*');
            $this->forgetPattern('patients_branch_*');
        }
    }
}
