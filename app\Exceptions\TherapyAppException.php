<?php

namespace App\Exceptions;

use Exception;

abstract class TherapyAppException extends Exception
{
    protected $errorCode;
    protected $context = [];

    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    abstract public function getErrorType(): string;
}

class AppointmentConflictException extends TherapyAppException
{
    protected $errorCode = 'APPOINTMENT_CONFLICT';

    public function getErrorType(): string
    {
        return 'appointment_conflict';
    }
}

class BranchAccessDeniedException extends TherapyAppException
{
    protected $errorCode = 'BRANCH_ACCESS_DENIED';

    public function getErrorType(): string
    {
        return 'branch_access_denied';
    }
}

class InvalidRoleAssignmentException extends TherapyAppException
{
    protected $errorCode = 'INVALID_ROLE_ASSIGNMENT';

    public function getErrorType(): string
    {
        return 'invalid_role_assignment';
    }
}

class DataIntegrityException extends TherapyAppException
{
    protected $errorCode = 'DATA_INTEGRITY_VIOLATION';

    public function getErrorType(): string
    {
        return 'data_integrity_violation';
    }
}

class BusinessRuleViolationException extends TherapyAppException
{
    protected $errorCode = 'BUSINESS_RULE_VIOLATION';

    public function getErrorType(): string
    {
        return 'business_rule_violation';
    }
}

class PackageValidationException extends TherapyAppException
{
    protected $errorCode = 'PACKAGE_VALIDATION_ERROR';

    public function getErrorType(): string
    {
        return 'package_validation_error';
    }
}

class UserProfileMismatchException extends TherapyAppException
{
    protected $errorCode = 'USER_PROFILE_MISMATCH';

    public function getErrorType(): string
    {
        return 'user_profile_mismatch';
    }
}

class AppointmentStateException extends TherapyAppException
{
    protected $errorCode = 'INVALID_APPOINTMENT_STATE';

    public function getErrorType(): string
    {
        return 'invalid_appointment_state';
    }
}
