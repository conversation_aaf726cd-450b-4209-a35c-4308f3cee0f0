import { createContext, useContext, useState, useCallback } from 'react';
import { ToastContainer } from '@/Components/ui/Toast';

const ToastContext = createContext();

export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};

export const ToastProvider = ({ children }) => {
    const [toasts, setToasts] = useState([]);

    const addToast = useCallback((toast) => {
        const id = Date.now() + Math.random();
        const newToast = {
            id,
            duration: 5000,
            position: 'top-right',
            ...toast,
        };

        setToasts(prev => [...prev, newToast]);
        return id;
    }, []);

    const removeToast = useCallback((id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    }, []);

    const clearAllToasts = useCallback(() => {
        setToasts([]);
    }, []);

    // Convenience methods
    const success = useCallback((title, message, options = {}) => {
        return addToast({
            type: 'success',
            title,
            message,
            ...options
        });
    }, [addToast]);

    const error = useCallback((title, message, options = {}) => {
        return addToast({
            type: 'error',
            title,
            message,
            duration: 7000, // Longer duration for errors
            ...options
        });
    }, [addToast]);

    const warning = useCallback((title, message, options = {}) => {
        return addToast({
            type: 'warning',
            title,
            message,
            duration: 6000,
            ...options
        });
    }, [addToast]);

    const info = useCallback((title, message, options = {}) => {
        return addToast({
            type: 'info',
            title,
            message,
            ...options
        });
    }, [addToast]);

    const loading = useCallback((title, message, options = {}) => {
        return addToast({
            type: 'loading',
            title,
            message,
            duration: 0, // No auto-close for loading
            ...options
        });
    }, [addToast]);

    // Promise-based toast for async operations
    const promise = useCallback(async (promise, messages, options = {}) => {
        const loadingId = loading(
            messages.loading || 'Loading...',
            messages.loadingMessage,
            options
        );

        try {
            const result = await promise;
            removeToast(loadingId);
            success(
                messages.success || 'Success!',
                messages.successMessage,
                options
            );
            return result;
        } catch (err) {
            removeToast(loadingId);
            error(
                messages.error || 'Error!',
                messages.errorMessage || err.message,
                options
            );
            throw err;
        }
    }, [loading, removeToast, success, error]);

    const value = {
        toasts,
        addToast,
        removeToast,
        clearAllToasts,
        success,
        error,
        warning,
        info,
        loading,
        promise
    };

    return (
        <ToastContext.Provider value={value}>
            {children}
            <ToastContainer toasts={toasts} onRemove={removeToast} />
        </ToastContext.Provider>
    );
};

export default ToastContext;
