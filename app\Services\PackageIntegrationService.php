<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\PatientPackage;
use App\Models\PackageSession;
use App\Models\TherapyPackage;
use App\Services\ValidationService;
use App\Exceptions\BusinessRuleViolationException;
use App\Exceptions\BranchAccessDeniedException;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PackageIntegrationService
{
    public function __construct(
        private ValidationService $validationService
    ) {}

    /**
     * Create appointment from package session
     */
    public function createAppointmentFromPackage(PatientPackage $patientPackage, array $sessionData): Appointment
    {
        return DB::transaction(function () use ($patientPackage, $sessionData) {
            // Validate package can schedule session
            if (!$patientPackage->canScheduleSession()) {
                throw new BusinessRuleViolationException('Package cannot schedule more sessions');
            }

            // Validate branch consistency
            $this->validationService->validateBranchAccess($patientPackage->branch_id);

            // Create appointment data
            $appointmentData = [
                'patient_id' => $patientPackage->patient_id,
                'doctor_id' => $patientPackage->assigned_doctor_id,
                'branch_id' => $patientPackage->branch_id,
                'appointment_date' => $sessionData['session_date'],
                'duration_minutes' => $sessionData['duration_minutes'] ?? $patientPackage->therapyPackage->session_duration_minutes,
                'status' => 'scheduled',
                'appointment_type' => 'package_session',
                'notes' => $sessionData['notes'] ?? "Package session {$patientPackage->sessions_completed + 1} of {$patientPackage->therapyPackage->total_sessions}",
            ];

            // Validate appointment
            $this->validationService->validateAppointment($appointmentData);

            // Create appointment
            $appointment = Appointment::create($appointmentData);

            // Create package session
            $packageSession = PackageSession::create([
                'branch_id' => $patientPackage->branch_id,
                'patient_package_id' => $patientPackage->id,
                'appointment_id' => $appointment->id,
                'session_number' => $patientPackage->sessions_completed + 1,
                'session_date' => $sessionData['session_date'],
                'session_time' => Carbon::parse($sessionData['session_date'])->format('H:i'),
                'duration_minutes' => $appointmentData['duration_minutes'],
                'status' => 'scheduled',
                'session_fee' => $patientPackage->therapyPackage->price_per_session,
                'conducted_by' => $patientPackage->assigned_doctor_id,
            ]);

            return $appointment->load(['packageSession', 'patient', 'doctor']);
        });
    }

    /**
     * Complete package session and update package progress
     */
    public function completePackageSession(Appointment $appointment, array $sessionData = []): PackageSession
    {
        return DB::transaction(function () use ($appointment, $sessionData) {
            $packageSession = $appointment->packageSession;
            
            if (!$packageSession) {
                throw new BusinessRuleViolationException('Appointment is not linked to a package session');
            }

            // Validate branch access
            $this->validationService->validateBranchAccess($packageSession->branch_id);

            // Complete the appointment
            $appointment->update([
                'status' => 'completed',
                'completed_at' => now(),
                'notes' => $sessionData['appointment_notes'] ?? $appointment->notes,
            ]);

            // Complete the package session
            $packageSession->update([
                'status' => 'completed',
                'completed_at' => now(),
                'session_notes' => $sessionData['session_notes'] ?? null,
                'homework_assigned' => $sessionData['homework_assigned'] ?? null,
                'progress_notes' => $sessionData['progress_notes'] ?? null,
            ]);

            // Update patient package progress
            $packageSession->patientPackage->completeSession();

            return $packageSession->fresh();
        });
    }

    /**
     * Assign therapy package to patient
     */
    public function assignPackageToPatient(int $packageId, int $patientId, array $assignmentData): PatientPackage
    {
        return DB::transaction(function () use ($packageId, $patientId, $assignmentData) {
            $package = TherapyPackage::findOrFail($packageId);
            
            // Validate branch access
            $this->validationService->validateBranchAccess($package->branch_id);

            // Validate patient belongs to same branch
            $patient = \App\Models\User::with('patientProfile')->findOrFail($patientId);
            if ($patient->patientProfile->branch_id !== $package->branch_id) {
                throw new BranchAccessDeniedException('Patient and package must belong to the same branch');
            }

            // Calculate financial details
            $originalPrice = $assignmentData['original_price'] ?? $package->price;
            $discountGiven = $assignmentData['discount_given'] ?? 0;
            $totalAfterDiscount = $originalPrice - $discountGiven;

            // Create patient package
            $patientPackage = PatientPackage::create([
                'branch_id' => $package->branch_id,
                'patient_id' => $patientId,
                'therapy_package_id' => $packageId,
                'assigned_by' => auth()->id(),
                'assigned_doctor_id' => $assignmentData['assigned_doctor_id'],
                'start_date' => $assignmentData['start_date'] ?? now(),
                'end_date' => null, // Will be calculated
                'sessions_completed' => 0,
                'sessions_remaining' => $package->total_sessions,
                'status' => 'active',
                'original_price' => $originalPrice,
                'discount_given' => $discountGiven,
                'total_amount_after_discount' => $totalAfterDiscount,
                'number_of_installments' => $assignmentData['number_of_installments'] ?? 1,
                'payment_type' => $assignmentData['payment_type'] ?? 'full_payment',
                'amount_paid' => $assignmentData['amount_paid'] ?? 0,
                'payment_status' => $assignmentData['payment_status'] ?? 'pending',
                'payment_notes' => $assignmentData['payment_notes'] ?? null,
                'notes' => $assignmentData['notes'] ?? null,
            ]);

            // Calculate and set end date
            $endDate = $patientPackage->calculateEndDate();
            $patientPackage->update(['end_date' => $endDate]);

            // Create installments if needed
            if ($patientPackage->number_of_installments > 1) {
                $this->createInstallments($patientPackage);
            }

            return $patientPackage->load(['therapyPackage', 'patient', 'assignedDoctor']);
        });
    }

    /**
     * Get package progress and statistics
     */
    public function getPackageProgress(PatientPackage $patientPackage): array
    {
        $this->validationService->validateBranchAccess($patientPackage->branch_id);

        $completedSessions = $patientPackage->completedSessions()->count();
        $upcomingSessions = $patientPackage->upcomingSessions()->count();
        $totalSessions = $patientPackage->therapyPackage->total_sessions;

        return [
            'package_id' => $patientPackage->id,
            'package_title' => $patientPackage->therapyPackage->title,
            'patient_name' => $patientPackage->patient->name,
            'doctor_name' => $patientPackage->assignedDoctor->name,
            'total_sessions' => $totalSessions,
            'completed_sessions' => $completedSessions,
            'upcoming_sessions' => $upcomingSessions,
            'remaining_sessions' => $patientPackage->sessions_remaining,
            'progress_percentage' => $patientPackage->progress_percentage,
            'status' => $patientPackage->status,
            'start_date' => $patientPackage->start_date->format('Y-m-d'),
            'end_date' => $patientPackage->end_date?->format('Y-m-d'),
            'is_expired' => $patientPackage->isExpired(),
            'is_completed' => $patientPackage->isCompleted(),
            'financial_summary' => [
                'original_price' => $patientPackage->original_price,
                'discount_given' => $patientPackage->discount_given,
                'total_amount' => $patientPackage->total_amount_after_discount,
                'amount_paid' => $patientPackage->amount_paid,
                'remaining_amount' => $patientPackage->remaining_amount,
                'payment_status' => $patientPackage->payment_status,
                'is_fully_paid' => $patientPackage->isFullyPaid(),
            ],
        ];
    }

    /**
     * Get branch package statistics
     */
    public function getBranchPackageStats(int $branchId): array
    {
        $this->validationService->validateBranchAccess($branchId);

        $activePackages = PatientPackage::where('branch_id', $branchId)
            ->where('status', 'active')
            ->count();

        $completedPackages = PatientPackage::where('branch_id', $branchId)
            ->where('status', 'completed')
            ->count();

        $totalRevenue = PatientPackage::where('branch_id', $branchId)
            ->sum('amount_paid');

        $pendingRevenue = PatientPackage::where('branch_id', $branchId)
            ->where('payment_status', '!=', 'paid')
            ->sum('total_amount_after_discount');

        return [
            'active_packages' => $activePackages,
            'completed_packages' => $completedPackages,
            'total_packages' => $activePackages + $completedPackages,
            'total_revenue' => $totalRevenue,
            'pending_revenue' => $pendingRevenue,
            'completion_rate' => $activePackages + $completedPackages > 0 
                ? ($completedPackages / ($activePackages + $completedPackages)) * 100 
                : 0,
        ];
    }

    /**
     * Create installments for patient package
     */
    private function createInstallments(PatientPackage $patientPackage): void
    {
        $installmentAmount = $patientPackage->total_amount_after_discount / $patientPackage->number_of_installments;
        
        for ($i = 1; $i <= $patientPackage->number_of_installments; $i++) {
            \App\Models\PackageInstallment::create([
                'branch_id' => $patientPackage->branch_id,
                'patient_package_id' => $patientPackage->id,
                'installment_number' => $i,
                'installment_date' => $patientPackage->start_date->addMonths($i - 1),
                'installment_amount' => $installmentAmount,
                'due_date' => $patientPackage->start_date->addMonths($i - 1)->addDays(30),
                'status' => $i === 1 ? 'pending' : 'scheduled',
            ]);
        }
    }
}
