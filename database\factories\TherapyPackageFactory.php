<?php

namespace Database\Factories;

use App\Models\TherapyPackage;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class TherapyPackageFactory extends Factory
{
    protected $model = TherapyPackage::class;

    public function definition(): array
    {
        $packageTypes = ['individual', 'group', 'family', 'couples'];
        $packageType = $this->faker->randomElement($packageTypes);
        
        $totalSessions = match($packageType) {
            'individual' => $this->faker->numberBetween(8, 24),
            'group' => $this->faker->numberBetween(6, 12),
            'family' => $this->faker->numberBetween(6, 16),
            'couples' => $this->faker->numberBetween(8, 20),
        };

        $sessionDuration = match($packageType) {
            'individual' => $this->faker->randomElement([45, 50, 60]),
            'group' => $this->faker->randomElement([60, 90, 120]),
            'family' => $this->faker->randomElement([60, 75, 90]),
            'couples' => $this->faker->randomElement([50, 60, 75]),
        };

        $basePrice = match($packageType) {
            'individual' => $this->faker->numberBetween(80, 150),
            'group' => $this->faker->numberBetween(40, 80),
            'family' => $this->faker->numberBetween(100, 180),
            'couples' => $this->faker->numberBetween(120, 200),
        };

        return [
            'title' => $this->generatePackageTitle($packageType),
            'description' => $this->generatePackageDescription($packageType),
            'details' => $this->faker->paragraphs(3, true),
            'total_sessions' => $totalSessions,
            'price' => $basePrice * $totalSessions,
            'session_duration_minutes' => $sessionDuration,
            'package_type' => $packageType,
            'objectives' => $this->generateObjectives($packageType),
            'target_audience' => $this->generateTargetAudience($packageType),
            'included_services' => $this->generateIncludedServices($packageType),
            'validity_days' => $this->faker->randomElement([180, 365, 540]), // 6 months, 1 year, 1.5 years
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
            'created_by' => User::factory(),
        ];
    }

    private function generatePackageTitle(string $type): string
    {
        $titles = [
            'individual' => [
                'Anxiety Management Program',
                'Depression Recovery Package',
                'Stress Relief Therapy',
                'Personal Growth Journey',
                'Trauma Recovery Program',
                'Cognitive Behavioral Therapy Package',
            ],
            'group' => [
                'Social Skills Development Group',
                'Anxiety Support Group',
                'Addiction Recovery Group',
                'Mindfulness Group Therapy',
                'Grief Support Group',
            ],
            'family' => [
                'Family Communication Enhancement',
                'Parent-Child Relationship Therapy',
                'Family Conflict Resolution',
                'Blended Family Support Program',
                'Teen Family Therapy Package',
            ],
            'couples' => [
                'Relationship Enhancement Program',
                'Marriage Counseling Package',
                'Communication Skills for Couples',
                'Pre-marital Counseling Program',
                'Couples Conflict Resolution',
            ],
        ];

        return $this->faker->randomElement($titles[$type]);
    }

    private function generatePackageDescription(string $type): string
    {
        $descriptions = [
            'individual' => 'Personalized therapy sessions designed to address individual mental health concerns and promote personal growth.',
            'group' => 'Supportive group environment where participants can share experiences and learn from others facing similar challenges.',
            'family' => 'Comprehensive family therapy program focused on improving communication and resolving family dynamics.',
            'couples' => 'Specialized therapy for couples to strengthen relationships and improve communication patterns.',
        ];

        return $descriptions[$type];
    }

    private function generateObjectives(string $type): string
    {
        $objectives = [
            'individual' => "• Develop coping strategies\n• Improve emotional regulation\n• Build self-awareness\n• Enhance problem-solving skills",
            'group' => "• Foster peer support\n• Practice social skills\n• Share experiences\n• Learn from others",
            'family' => "• Improve family communication\n• Resolve conflicts\n• Strengthen family bonds\n• Develop healthy boundaries",
            'couples' => "• Enhance communication\n• Rebuild trust\n• Resolve conflicts\n• Strengthen intimacy",
        ];

        return $objectives[$type];
    }

    private function generateTargetAudience(string $type): string
    {
        $audiences = [
            'individual' => 'Adults experiencing anxiety, depression, trauma, or seeking personal growth',
            'group' => 'Individuals who benefit from peer support and group dynamics',
            'family' => 'Families experiencing communication difficulties or relationship challenges',
            'couples' => 'Couples seeking to improve their relationship or resolve conflicts',
        ];

        return $audiences[$type];
    }

    private function generateIncludedServices(string $type): array
    {
        $baseServices = ['therapy_sessions', 'progress_reports'];
        
        $additionalServices = [
            'individual' => ['consultation', 'assessment', 'homework_assignments'],
            'group' => ['consultation', 'group_activities'],
            'family' => ['consultation', 'family_sessions', 'homework_assignments'],
            'couples' => ['consultation', 'assessment', 'homework_assignments'],
        ];

        return array_merge($baseServices, $this->faker->randomElements($additionalServices[$type], $this->faker->numberBetween(1, 3)));
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'package_type' => 'individual',
        ]);
    }

    public function group(): static
    {
        return $this->state(fn (array $attributes) => [
            'package_type' => 'group',
        ]);
    }

    public function family(): static
    {
        return $this->state(fn (array $attributes) => [
            'package_type' => 'family',
        ]);
    }

    public function couples(): static
    {
        return $this->state(fn (array $attributes) => [
            'package_type' => 'couples',
        ]);
    }
}
