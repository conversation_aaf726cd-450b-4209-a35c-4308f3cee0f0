import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Building2, Plus, Users, UserCheck, Eye, Edit, Trash2 } from 'lucide-react';

export default function BranchesIndex({ auth, branches, canManageAll, stats, filters }) {
    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this branch?')) {
            router.delete(route('branches.destroy', id));
        }
    };

    return (
        <AppLayout>
            <Head title="Branches" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Branches</h1>
                            <p className="text-gray-600">Manage therapy center branches</p>
                        </div>
                        
                        {canManageAll && (
                            <Link href={route('branches.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Branch
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
                                <Building2 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_branches}</div>
                                <p className="text-xs text-muted-foreground">
                                    {stats.active_branches} active
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                                <UserCheck className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_doctors}</div>
                                <p className="text-xs text-muted-foreground">Across all branches</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_patients}</div>
                                <p className="text-xs text-muted-foreground">Registered patients</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Active Rate</CardTitle>
                                <Building2 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {stats.total_branches > 0 ? Math.round((stats.active_branches / stats.total_branches) * 100) : 0}%
                                </div>
                                <p className="text-xs text-muted-foreground">Branch activation rate</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Branches List */}
                    <Card>
                        <CardHeader>
                            <CardTitle>All Branches</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {branches.length > 0 ? (
                                <div className="space-y-4">
                                    {branches.map((branch) => (
                                        <div key={branch.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <Building2 className="h-5 w-5 text-muted-foreground" />
                                                        <h3 className="font-medium">{branch.name}</h3>
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            branch.is_active 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {branch.is_active ? 'Active' : 'Inactive'}
                                                        </span>
                                                    </div>
                                                    
                                                    <p className="text-sm text-gray-600 mb-2">{branch.address}</p>
                                                    
                                                    <div className="flex items-center gap-4 text-sm text-gray-500">
                                                        <span>📧 {branch.email}</span>
                                                        <span>📞 {branch.phone}</span>
                                                        {branch.branch_head && (
                                                            <span>👤 Manager: {branch.branch_head.name}</span>
                                                        )}
                                                    </div>

                                                    <div className="flex items-center gap-4 mt-2 text-sm">
                                                        <span className="flex items-center gap-1">
                                                            <UserCheck className="h-3 w-3" />
                                                            {branch.doctor_profiles_count || 0} Doctors
                                                        </span>
                                                        <span className="flex items-center gap-1">
                                                            <Users className="h-3 w-3" />
                                                            {branch.patient_profiles_count || 0} Patients
                                                        </span>
                                                    </div>
                                                </div>
                                                
                                                <div className="flex items-center gap-2">
                                                    <Link href={route('branches.show', branch.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {canManageAll && (
                                                        <>
                                                            <Link href={route('branches.edit', branch.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            
                                                            <Button 
                                                                variant="outline" 
                                                                size="sm"
                                                                onClick={() => handleDelete(branch.id)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No branches found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        {canManageAll 
                                            ? 'Get started by creating your first branch'
                                            : 'No branches have been assigned to you'
                                        }
                                    </p>
                                    {canManageAll && (
                                        <Link href={route('branches.create')}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Create Branch
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}