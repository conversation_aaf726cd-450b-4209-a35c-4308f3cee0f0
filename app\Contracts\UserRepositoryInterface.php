<?php

namespace App\Contracts;

use App\Models\User;
use Illuminate\Pagination\LengthAwarePaginator;

interface UserRepositoryInterface
{
    public function all();
    public function paginate(int $perPage = 15): LengthAwarePaginator;
    public function find(int $id): ?User;
    public function findByEmail(string $email): ?User;
    public function create(array $data): User;
    public function update(int $id, array $data): User;
    public function delete(int $id): bool;
    public function getByRole(string $role);
    public function getActiveUsers();
    public function getDoctorsByBranch(int $branchId);
    public function getPatientsByBranch(int $branchId);
    public function searchUsers(string $search, ?string $role = null);
}