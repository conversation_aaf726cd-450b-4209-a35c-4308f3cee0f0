import AppLayout from '@/layouts/app-layout';
import { Head } from '@inertiajs/react';

// Import dashboard components
import AdminStats from '@/Components/dashboard/AdminStats';
import BranchHeadStats from '@/Components/dashboard/BranchHeadStats';
import DoctorStats from '@/Components/dashboard/DoctorStats';
import PatientStats from '@/Components/dashboard/PatientStats';
import QuickActions from '@/Components/dashboard/QuickActions';
import AppointmentsList from '@/Components/dashboard/AppointmentsList';
import BranchOverview from '@/Components/dashboard/BranchOverview';
import ProfileHeader from '@/Components/dashboard/ProfileHeader';
import NoRoleWarning from '@/Components/dashboard/NoRoleWarning';
import PackageTracker from '@/Components/dashboard/PackageTracker';
import FinancialCollections from '@/Components/dashboard/FinancialCollections';

export default function Dashboard({
    auth,
    stats = {},
    recentActivity = {},
    upcomingAppointments = [],
    branchStats = [],
    branch = null,
    doctorProfile = null,
    patientProfile = null,
    todaysAppointments = [],
    pastAppointments = [],
    recentPatients = [],
    recentDoctors = [],
    doctors = [],
    patients = [],
    packageAnalytics = null,
    availableFilters = null
}) {
    const user = auth.user;
    const userRole = user.roles?.[0]?.name;

    // Role check helpers
    const isAdmin = () => userRole === 'admin';
    const isBranchHead = () => userRole === 'branch_head';
    const isDoctor = () => userRole === 'doctor';
    const isPatient = () => userRole === 'patient';

    return (
        <AppLayout>
            <Head title={
                isAdmin() ? 'Admin Dashboard' :
                isBranchHead() ? 'Branch Dashboard' :
                isDoctor() ? 'Doctor Dashboard' :
                isPatient() ? 'Patient Dashboard' :
                'Dashboard'
            } />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Profile Header */}
                    <ProfileHeader 
                        user={user}
                        userRole={userRole}
                        branch={branch}
                        doctorProfile={doctorProfile}
                        patientProfile={patientProfile}
                    />

                    {/* No Role Warning */}
                    {!userRole && <NoRoleWarning />}

                    {/* Stats Overview - Role-based */}
                    {userRole && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            {isAdmin() && <AdminStats stats={stats} />}
                            {isBranchHead() && <BranchHeadStats stats={stats} />}
                            {isDoctor() && <DoctorStats stats={stats} />}
                            {isPatient() && <PatientStats stats={stats} />}
                        </div>
                    )}

                    {/* Quick Actions */}
                    {userRole && (
                        <QuickActions 
                            userRole={userRole}
                            isAdmin={isAdmin}
                            isBranchHead={isBranchHead}
                            isDoctor={isDoctor}
                            isPatient={isPatient}
                        />
                    )}

                    {/* Dynamic Content Grid */}
                    {userRole && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            {/* Appointments List */}
                            {(upcomingAppointments.length > 0 || todaysAppointments.length > 0 || pastAppointments.length > 0) && (
                                <AppointmentsList 
                                    userRole={userRole}
                                    upcomingAppointments={upcomingAppointments}
                                    todaysAppointments={todaysAppointments}
                                    pastAppointments={pastAppointments}
                                    isDoctor={isDoctor}
                                    isPatient={isPatient}
                                />
                            )}

                            {/* Branch Overview for Admin */}
                            {isAdmin() && branchStats.length > 0 && (
                                <BranchOverview branchStats={branchStats} />
                            )}
                        </div>
                    )}

                    {/* Package Analytics for Admin */}
                    {isAdmin() && packageAnalytics && (
                        <div className="space-y-6">
                            <PackageTracker
                                initialData={packageAnalytics}
                                availableFilters={availableFilters}
                            />

                            <FinancialCollections
                                initialData={packageAnalytics}
                                availableFilters={availableFilters}
                            />
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}