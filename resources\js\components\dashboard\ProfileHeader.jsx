import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/Components/ui/card';
import { User, Building2, Stethoscope, Heart } from 'lucide-react';

export default function ProfileHeader({ user, userRole, branch, doctorProfile, patientProfile }) {
    const getRoleIcon = () => {
        switch (userRole) {
            case 'admin': return User;
            case 'branch_head': return Building2;
            case 'doctor': return Stethoscope;
            case 'patient': return Heart;
            default: return User;
        }
    };

    const RoleIcon = getRoleIcon();

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-3">
                    <RoleIcon className="h-6 w-6" />
                    <div>
                        <h2 className="text-xl font-bold">Welcome back, {user.name}</h2>
                        <p className="text-sm text-muted-foreground capitalize">
                            {userRole ? userRole.replace('_', ' ') : 'User'}
                        </p>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span className="font-medium">Email:</span> {user.email}
                    </div>
                    
                    {branch && (
                        <div>
                            <span className="font-medium">Branch:</span> {branch.name}
                        </div>
                    )}
                    
                    {doctorProfile && (
                        <div>
                            <span className="font-medium">Specialization:</span> {doctorProfile.specialization || 'General'}
                        </div>
                    )}
                    
                    {patientProfile && (
                        <div>
                            <span className="font-medium">Patient ID:</span> {patientProfile.id}
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}