import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { UserCheck, Plus, Eye, Edit, Trash2, Building2 } from 'lucide-react';

export default function BranchHeadsIndex({ auth, branchHeads }) {
    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this branch head?')) {
            router.delete(route('users.destroy', id));
        }
    };

    return (
        <AppLayout>
            <Head title="Branch Heads" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Branch Heads</h1>
                            <p className="text-gray-600">Manage branch managers</p>
                        </div>
                        
                        <Link href={route('users.create', { role: 'branch_head' })}>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Branch Head
                            </Button>
                        </Link>
                    </div>

                    {/* Back to Users */}
                    <div>
                        <Link href={route('users.index')} className="text-blue-600 hover:text-blue-800 text-sm">
                            ← Back to All Users
                        </Link>
                    </div>

                    {/* Branch Heads List */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <UserCheck className="h-5 w-5" />
                                Branch Heads ({branchHeads?.length || 0})
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {branchHeads && branchHeads.length > 0 ? (
                                <div className="space-y-4">
                                    {branchHeads.map((branchHead) => (
                                        <div key={branchHead.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <UserCheck className="h-4 w-4 text-purple-600" />
                                                        <h3 className="font-medium">{branchHead.name}</h3>
                                                        
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            branchHead.is_active 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {branchHead.is_active ? 'Active' : 'Inactive'}
                                                        </span>

                                                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                            Branch Head
                                                        </span>
                                                    </div>
                                                    
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                                                        <div>
                                                            <span className="font-medium">Email:</span> {branchHead.email}
                                                        </div>
                                                        {branchHead.phone && (
                                                            <div>
                                                                <span className="font-medium">Phone:</span> {branchHead.phone}
                                                            </div>
                                                        )}
                                                    </div>

                                                    {branchHead.managedBranch ? (
                                                        <div className="text-sm">
                                                            <span className="font-medium text-gray-700">Manages Branch:</span>
                                                            <p className="text-gray-600 flex items-center gap-1 mt-1">
                                                                <Building2 className="h-3 w-3" />
                                                                {branchHead.managedBranch.name}
                                                            </p>
                                                            <p className="text-gray-500 text-xs ml-4">
                                                                {branchHead.managedBranch.address}
                                                            </p>
                                                        </div>
                                                    ) : (
                                                        <div className="text-sm">
                                                            <span className="font-medium text-yellow-700">Status:</span>
                                                            <p className="text-yellow-600">No branch assigned</p>
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2 ml-4">
                                                    <Link href={route('users.show', branchHead.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    <Link href={route('users.edit', branchHead.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    <Button 
                                                        variant="outline" 
                                                        size="sm"
                                                        onClick={() => handleDelete(branchHead.id)}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <UserCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No branch heads found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        No branch heads have been assigned yet
                                    </p>
                                    <Link href={route('users.create', { role: 'branch_head' })}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Add Branch Head
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}