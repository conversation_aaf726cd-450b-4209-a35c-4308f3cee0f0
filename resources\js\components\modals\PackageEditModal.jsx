import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import Modal from '@/Components/ui/Modal';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { useToast } from '@/contexts/ToastContext';
import { Save, X, Loader2 } from 'lucide-react';

const PackageEditModal = ({ isOpen, onClose, packageData, onSuccess }) => {
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        total_sessions: '',
        price: '',
        duration_months: '',
        is_active: true
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const toast = useToast();

    useEffect(() => {
        if (packageData) {
            setFormData({
                name: packageData.name || '',
                description: packageData.description || '',
                total_sessions: packageData.total_sessions || '',
                price: packageData.price || '',
                duration_months: packageData.duration_months || '',
                is_active: packageData.is_active ?? true
            });
        }
    }, [packageData]);

    const handleInputChange = (name, value) => {
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        router.put(`/packages/${packageData.id}`, formData, {
            onSuccess: () => {
                toast.success('Package Updated!', 'Therapy package has been updated successfully.');
                onClose();
                if (onSuccess) onSuccess();
            },
            onError: (errors) => {
                setErrors(errors);
                toast.error('Update Failed', 'Please check the form and try again.');
            },
            onFinish: () => {
                setLoading(false);
            }
        });
    };

    const handleClose = () => {
        if (!loading) {
            setErrors({});
            onClose();
        }
    };

    const footerContent = (
        <>
            <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
            >
                <X className="h-4 w-4 mr-2" />
                Cancel
            </Button>
            <Button
                type="submit"
                form="package-edit-form"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
            >
                {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                    <Save className="h-4 w-4 mr-2" />
                )}
                {loading ? 'Updating...' : 'Update Package'}
            </Button>
        </>
    );

    return (
        <Modal
            isOpen={isOpen}
            onClose={handleClose}
            title={`Edit Package: ${packageData?.name || ''}`}
            size="lg"
            showFooter={true}
            footerContent={footerContent}
            closeOnOverlayClick={!loading}
            closeOnEscape={!loading}
            loading={loading}
        >
            <form id="package-edit-form" onSubmit={handleSubmit} className="space-y-6">
                {/* Package Name */}
                <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium">
                        Package Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter package name"
                        required
                        className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>

                {/* Description */}
                <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">
                        Description
                    </Label>
                    <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter package description"
                        rows={3}
                        className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                </div>

                {/* Total Sessions and Price */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="total_sessions" className="text-sm font-medium">
                            Total Sessions <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="total_sessions"
                            type="number"
                            value={formData.total_sessions}
                            onChange={(e) => handleInputChange('total_sessions', e.target.value)}
                            placeholder="Enter total sessions"
                            required
                            min="1"
                            className={errors.total_sessions ? 'border-red-500' : ''}
                        />
                        {errors.total_sessions && <p className="text-sm text-red-500">{errors.total_sessions}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="price" className="text-sm font-medium">
                            Price (₹) <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="price"
                            type="number"
                            value={formData.price}
                            onChange={(e) => handleInputChange('price', e.target.value)}
                            placeholder="Enter price"
                            required
                            min="0"
                            step="0.01"
                            className={errors.price ? 'border-red-500' : ''}
                        />
                        {errors.price && <p className="text-sm text-red-500">{errors.price}</p>}
                    </div>
                </div>

                {/* Duration and Status */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="duration_months" className="text-sm font-medium">
                            Duration (Months) <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="duration_months"
                            type="number"
                            value={formData.duration_months}
                            onChange={(e) => handleInputChange('duration_months', e.target.value)}
                            placeholder="Enter duration in months"
                            required
                            min="1"
                            className={errors.duration_months ? 'border-red-500' : ''}
                        />
                        {errors.duration_months && <p className="text-sm text-red-500">{errors.duration_months}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="is_active" className="text-sm font-medium">
                            Status
                        </Label>
                        <Select
                            value={formData.is_active ? 'true' : 'false'}
                            onValueChange={(value) => handleInputChange('is_active', value === 'true')}
                        >
                            <SelectTrigger className={errors.is_active ? 'border-red-500' : ''}>
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="true">Active</SelectItem>
                                <SelectItem value="false">Inactive</SelectItem>
                            </SelectContent>
                        </Select>
                        {errors.is_active && <p className="text-sm text-red-500">{errors.is_active}</p>}
                    </div>
                </div>

                {/* Package Info */}
                {packageData && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Package Information</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <span className="font-medium">Created:</span> {new Date(packageData.created_at).toLocaleDateString()}
                            </div>
                            <div>
                                <span className="font-medium">Last Updated:</span> {new Date(packageData.updated_at).toLocaleDateString()}
                            </div>
                        </div>
                    </div>
                )}
            </form>
        </Modal>
    );
};

export default PackageEditModal;
