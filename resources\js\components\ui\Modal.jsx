import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { X, Maximize2, Minimize2 } from 'lucide-react';
import { Button } from '@/Components/ui/button';

const Modal = ({
    isOpen,
    onClose,
    title,
    children,
    size = 'md',
    showCloseButton = true,
    closeOnOverlayClick = true,
    closeOnEscape = true,
    showHeader = true,
    showFooter = false,
    footerContent,
    className = '',
    overlayClassName = '',
    maxHeight = 'auto',
    fullScreen = false,
    onFullScreenToggle,
    loading = false
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isClosing, setIsClosing] = useState(false);
    const [isFullScreen, setIsFullScreen] = useState(fullScreen);
    const modalRef = useRef(null);
    const previousFocusRef = useRef(null);

    // Size configurations
    const sizeClasses = {
        xs: 'max-w-xs',
        sm: 'max-w-sm',
        md: 'max-w-md',
        lg: 'max-w-lg',
        xl: 'max-w-xl',
        '2xl': 'max-w-2xl',
        '3xl': 'max-w-3xl',
        '4xl': 'max-w-4xl',
        '5xl': 'max-w-5xl',
        '6xl': 'max-w-6xl',
        '7xl': 'max-w-7xl',
        full: 'max-w-full'
    };

    useEffect(() => {
        if (isOpen) {
            // Store current focus
            previousFocusRef.current = document.activeElement;
            
            // Prevent body scroll
            document.body.style.overflow = 'hidden';
            
            // Show modal with animation
            setTimeout(() => setIsVisible(true), 10);
            
            // Focus modal
            if (modalRef.current) {
                modalRef.current.focus();
            }
        } else {
            setIsVisible(false);
            setIsClosing(false);
            document.body.style.overflow = 'unset';
            
            // Restore focus
            if (previousFocusRef.current) {
                previousFocusRef.current.focus();
            }
        }

        return () => {
            document.body.style.overflow = 'unset';
        };
    }, [isOpen]);

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e) => {
            if (e.key === 'Escape' && closeOnEscape && isOpen) {
                handleClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
        };
    }, [isOpen, closeOnEscape]);

    const handleClose = () => {
        if (loading) return; // Prevent closing while loading
        
        setIsClosing(true);
        setTimeout(() => {
            onClose();
        }, 200);
    };

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget && closeOnOverlayClick) {
            handleClose();
        }
    };

    const toggleFullScreen = () => {
        setIsFullScreen(!isFullScreen);
        if (onFullScreenToggle) {
            onFullScreenToggle(!isFullScreen);
        }
    };

    if (!isOpen) return null;

    const modalContent = (
        <div
            className={`
                fixed inset-0 z-50 flex items-center justify-center p-4
                ${overlayClassName}
            `}
            onClick={handleOverlayClick}
        >
            {/* Backdrop */}
            <div
                className={`
                    absolute inset-0 bg-black transition-opacity duration-200
                    ${isVisible && !isClosing ? 'opacity-50' : 'opacity-0'}
                `}
            />

            {/* Modal */}
            <div
                ref={modalRef}
                tabIndex={-1}
                className={`
                    relative bg-white rounded-lg shadow-2xl w-full
                    transform transition-all duration-200 ease-out
                    ${isFullScreen ? 'max-w-full h-full max-h-full' : sizeClasses[size]}
                    ${maxHeight !== 'auto' ? `max-h-[${maxHeight}]` : ''}
                    ${isVisible && !isClosing 
                        ? 'opacity-100 scale-100 translate-y-0' 
                        : 'opacity-0 scale-95 translate-y-4'
                    }
                    ${className}
                `}
                style={{
                    maxHeight: isFullScreen ? '100vh' : maxHeight
                }}
            >
                {/* Header */}
                {showHeader && (
                    <div className="flex items-center justify-between p-6 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-900">
                            {title}
                        </h2>
                        <div className="flex items-center gap-2">
                            {onFullScreenToggle && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={toggleFullScreen}
                                    className="h-8 w-8 p-0"
                                >
                                    {isFullScreen ? (
                                        <Minimize2 className="h-4 w-4" />
                                    ) : (
                                        <Maximize2 className="h-4 w-4" />
                                    )}
                                </Button>
                            )}
                            {showCloseButton && (
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleClose}
                                    disabled={loading}
                                    className="h-8 w-8 p-0"
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            )}
                        </div>
                    </div>
                )}

                {/* Content */}
                <div className={`
                    ${isFullScreen ? 'flex-1 overflow-auto' : ''}
                    ${showHeader && showFooter ? '' : showHeader ? 'pb-6' : showFooter ? 'pt-6' : 'py-6'}
                    ${showHeader ? '' : 'pt-6'}
                    ${showFooter ? '' : 'pb-6'}
                    px-6
                `}>
                    {loading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <span className="ml-3 text-gray-600">Loading...</span>
                        </div>
                    ) : (
                        children
                    )}
                </div>

                {/* Footer */}
                {showFooter && footerContent && (
                    <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200 bg-gray-50">
                        {footerContent}
                    </div>
                )}
            </div>
        </div>
    );

    return createPortal(modalContent, document.body);
};

export default Modal;
