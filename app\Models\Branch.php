<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Branch extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'email',
        'branch_head_id',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function branchHead()
    {
        return $this->belongsTo(User::class, 'branch_head_id');
    }

    public function doctors()
    {
        return $this->hasManyThrough(User::class, DoctorProfile::class, 'branch_id', 'id', 'id', 'user_id');
    }

    public function patients()
    {
        return $this->hasManyThrough(User::class, PatientProfile::class, 'branch_id', 'id', 'id', 'user_id');
    }

    public function doctorProfiles()
    {
        return $this->hasMany(DoctorProfile::class);
    }

    public function patientProfiles()
    {
        return $this->hasMany(PatientProfile::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }
}