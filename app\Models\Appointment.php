<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Appointment extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id',
        'doctor_id',
        'branch_id',
        'appointment_date',
        'duration_minutes',
        'status',
        'notes',
        'symptoms',
        'diagnosis',
        'treatment_plan',
        'prescription',
        'fee',
        'payment_status',
    ];

    protected function casts(): array
    {
        return [
            'appointment_date' => 'datetime',
            'fee' => 'decimal:2',
            'duration_minutes' => 'integer',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('appointment_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('appointment_date', '>', now());
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Helper methods
    public function getEndTimeAttribute()
    {
        return $this->appointment_date->addMinutes($this->duration_minutes);
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isFuture();
    }

    public function canBeRescheduled()
    {
        return in_array($this->status, ['scheduled', 'confirmed']) && 
               $this->appointment_date->isFuture();
    }
}