<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Create a separate table to handle branch head assignments
        // This eliminates the circular dependency between users and branches
        Schema::create('branch_heads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('assigned_date');
            $table->date('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->timestamps();
            
            // Ensure only one active branch head per branch
            $table->unique(['branch_id', 'is_active'], 'unique_active_branch_head');
            $table->index(['user_id', 'is_active']);
            $table->index('assigned_date');
        });

        // Add proper constraints for appointment scheduling
        Schema::table('appointments', function (Blueprint $table) {
            // Ensure patient and doctor are from the same branch or compatible branches
            $table->index(['doctor_id', 'patient_id', 'appointment_date'], 'appointment_participants_date');
            
            // Add constraint to prevent self-appointments (if doctor is also a patient)
            $table->check('doctor_id != patient_id', 'no_self_appointment');
        });

        // Add validation for package system integration
        Schema::table('patient_packages', function (Blueprint $table) {
            // Ensure package dates are logical
            $table->check('start_date <= end_date', 'package_date_logic');
            $table->check('sessions_completed <= sessions_remaining + sessions_completed', 'session_count_logic');
            $table->check('amount_paid <= total_amount_after_discount', 'payment_amount_logic');
            $table->check('discount_given <= original_price', 'discount_logic');
            $table->check('total_amount_after_discount = original_price - discount_given', 'price_calculation_logic');
        });

        // Add constraints for session schedules
        Schema::table('session_schedules', function (Blueprint $table) {
            $table->check('session_number > 0', 'positive_session_number');
            $table->check('duration_minutes > 0 AND duration_minutes <= 480', 'session_duration_check');
        });

        // Add constraints for installments
        Schema::table('package_installments', function (Blueprint $table) {
            $table->check('installment_number > 0', 'positive_installment_number');
            $table->check('installment_amount > 0', 'positive_installment_amount');
            $table->check('amount_paid <= installment_amount', 'installment_payment_logic');
            $table->check('due_date >= installment_date', 'installment_due_date_logic');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('branch_heads');
        
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropIndex('appointment_participants_date');
            $table->dropCheckConstraint('no_self_appointment');
        });

        Schema::table('patient_packages', function (Blueprint $table) {
            $table->dropCheckConstraint('package_date_logic');
            $table->dropCheckConstraint('session_count_logic');
            $table->dropCheckConstraint('payment_amount_logic');
            $table->dropCheckConstraint('discount_logic');
            $table->dropCheckConstraint('price_calculation_logic');
        });

        Schema::table('session_schedules', function (Blueprint $table) {
            $table->dropCheckConstraint('positive_session_number');
            $table->dropCheckConstraint('session_duration_check');
        });

        Schema::table('package_installments', function (Blueprint $table) {
            $table->dropCheckConstraint('positive_installment_number');
            $table->dropCheckConstraint('positive_installment_amount');
            $table->dropCheckConstraint('installment_payment_logic');
            $table->dropCheckConstraint('installment_due_date_logic');
        });
    }
};
