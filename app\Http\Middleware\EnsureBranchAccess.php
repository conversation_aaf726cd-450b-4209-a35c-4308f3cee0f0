<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Exceptions\BranchAccessDeniedException;

class EnsureBranchAccess
{
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Admin has access to all branches
        if ($user->hasRole('admin')) {
            return $next($request);
        }

        // Get the branch context from the request
        $branchId = $this->getBranchIdFromRequest($request);
        
        if ($branchId && !$this->userHasAccessToBranch($user, $branchId)) {
            throw new BranchAccessDeniedException(
                'You do not have access to this branch',
                403,
                null,
                [
                    'user_id' => $user->id,
                    'requested_branch_id' => $branchId,
                    'user_branch_id' => $this->getUserBranchId($user)
                ]
            );
        }

        return $next($request);
    }

    private function getBranchIdFromRequest(Request $request): ?int
    {
        // Check route parameters
        if ($request->route('branch')) {
            return (int) $request->route('branch');
        }

        // Check query parameters
        if ($request->has('branch_id')) {
            return (int) $request->get('branch_id');
        }

        // Check form data
        if ($request->has('branch_id')) {
            return (int) $request->input('branch_id');
        }

        return null;
    }

    private function userHasAccessToBranch($user, int $branchId): bool
    {
        // Branch head can only access their managed branch
        if ($user->hasRole('branch_head')) {
            return $user->managedBranch && $user->managedBranch->id === $branchId;
        }

        // Doctor can only access their assigned branch
        if ($user->hasRole('doctor')) {
            return $user->doctorProfile && $user->doctorProfile->branch_id === $branchId;
        }

        // Patient can only access their assigned branch
        if ($user->hasRole('patient')) {
            return $user->patientProfile && $user->patientProfile->branch_id === $branchId;
        }

        return false;
    }

    private function getUserBranchId($user): ?int
    {
        if ($user->hasRole('branch_head')) {
            return $user->managedBranch?->id;
        }

        if ($user->hasRole('doctor')) {
            return $user->doctorProfile?->branch_id;
        }

        if ($user->hasRole('patient')) {
            return $user->patientProfile?->branch_id;
        }

        return null;
    }
}
