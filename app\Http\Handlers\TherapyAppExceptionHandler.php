<?php

namespace App\Http\Handlers;

use App\Exceptions\TherapyAppException;
use App\Exceptions\AppointmentConflictException;
use App\Exceptions\BranchAccessDeniedException;
use App\Exceptions\BusinessRuleViolationException;
use App\Exceptions\DataIntegrityException;
use App\Exceptions\InvalidRoleAssignmentException;
use App\Exceptions\PackageValidationException;
use App\Exceptions\UserProfileMismatchException;
use App\Exceptions\AppointmentStateException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;

class TherapyAppExceptionHandler
{
    /**
     * Handle therapy app specific exceptions
     */
    public function handle(TherapyAppException $exception, Request $request)
    {
        // Log the exception with context
        $this->logException($exception, $request);

        // Return appropriate response based on request type
        if ($request->expectsJson()) {
            return $this->handleJsonResponse($exception, $request);
        }

        return $this->handleWebResponse($exception, $request);
    }

    /**
     * Handle JSON responses for API requests
     */
    private function handleJsonResponse(TherapyAppException $exception, Request $request): JsonResponse
    {
        $statusCode = $this->getHttpStatusCode($exception);
        
        $response = [
            'error' => true,
            'error_type' => $exception->getErrorType(),
            'error_code' => $exception->getErrorCode(),
            'message' => $exception->getMessage(),
            'timestamp' => now()->toISOString(),
        ];

        // Add context for debugging in non-production environments
        if (!app()->isProduction()) {
            $response['context'] = $exception->getContext();
            $response['trace'] = $exception->getTraceAsString();
        }

        // Add recovery suggestions
        $response['recovery'] = $this->getRecoverySuggestions($exception);

        return response()->json($response, $statusCode);
    }

    /**
     * Handle web responses for browser requests
     */
    private function handleWebResponse(TherapyAppException $exception, Request $request): RedirectResponse
    {
        $errorMessage = $this->getUserFriendlyMessage($exception);
        $redirectRoute = $this->getRedirectRoute($exception, $request);
        
        return redirect()->to($redirectRoute)
            ->withErrors(['error' => $errorMessage])
            ->withInput($request->except(['password', 'password_confirmation']));
    }

    /**
     * Get HTTP status code for exception
     */
    private function getHttpStatusCode(TherapyAppException $exception): int
    {
        return match (get_class($exception)) {
            BranchAccessDeniedException::class => 403,
            InvalidRoleAssignmentException::class => 403,
            AppointmentConflictException::class => 409,
            BusinessRuleViolationException::class => 422,
            DataIntegrityException::class => 422,
            PackageValidationException::class => 422,
            UserProfileMismatchException::class => 422,
            AppointmentStateException::class => 422,
            default => 400,
        };
    }

    /**
     * Get user-friendly error messages
     */
    private function getUserFriendlyMessage(TherapyAppException $exception): string
    {
        return match (get_class($exception)) {
            BranchAccessDeniedException::class => 'You do not have permission to access this branch or resource.',
            InvalidRoleAssignmentException::class => 'The role assignment is not valid for this user.',
            AppointmentConflictException::class => 'This appointment time conflicts with an existing appointment. Please choose a different time.',
            BusinessRuleViolationException::class => $exception->getMessage(),
            DataIntegrityException::class => 'There was a data consistency issue. Please check your input and try again.',
            PackageValidationException::class => 'The therapy package information is invalid. Please review and correct the details.',
            UserProfileMismatchException::class => 'The user profile does not match the assigned role. Please contact an administrator.',
            AppointmentStateException::class => 'This appointment cannot be modified in its current state.',
            default => 'An error occurred while processing your request. Please try again.',
        };
    }

    /**
     * Get recovery suggestions for different exception types
     */
    private function getRecoverySuggestions(TherapyAppException $exception): array
    {
        return match (get_class($exception)) {
            BranchAccessDeniedException::class => [
                'Contact your administrator to request access to this branch',
                'Verify you are logged in with the correct account',
                'Check if your account has the necessary permissions'
            ],
            InvalidRoleAssignmentException::class => [
                'Verify the user exists and is active',
                'Check if the role is valid for this user type',
                'Ensure the user does not have conflicting roles'
            ],
            AppointmentConflictException::class => [
                'Choose a different appointment time',
                'Check the doctor\'s availability',
                'Consider scheduling for a different day'
            ],
            BusinessRuleViolationException::class => [
                'Review the business rules and requirements',
                'Check all required fields are filled correctly',
                'Verify the data meets the system constraints'
            ],
            DataIntegrityException::class => [
                'Check for duplicate entries',
                'Verify all required relationships exist',
                'Ensure data consistency across related records'
            ],
            PackageValidationException::class => [
                'Verify all package details are correct',
                'Check financial calculations',
                'Ensure the package is active and available'
            ],
            UserProfileMismatchException::class => [
                'Contact an administrator to resolve profile conflicts',
                'Verify the user\'s role assignments',
                'Check if profile data needs to be updated'
            ],
            AppointmentStateException::class => [
                'Check the current appointment status',
                'Verify the appointment can be modified',
                'Contact support if the state seems incorrect'
            ],
            default => [
                'Try refreshing the page and attempting the action again',
                'Check your internet connection',
                'Contact support if the problem persists'
            ],
        };
    }

    /**
     * Get appropriate redirect route based on exception and request
     */
    private function getRedirectRoute(TherapyAppException $exception, Request $request): string
    {
        // Try to redirect back to the previous page
        $previousUrl = url()->previous();
        
        // If no previous URL or it's the same as current, redirect to appropriate dashboard
        if (!$previousUrl || $previousUrl === $request->url()) {
            return match (get_class($exception)) {
                BranchAccessDeniedException::class => $this->getDashboardRoute(),
                InvalidRoleAssignmentException::class => route('admin.users.index'),
                AppointmentConflictException::class => route('appointments.index'),
                PackageValidationException::class => route('therapy-packages.index'),
                default => $this->getDashboardRoute(),
            };
        }

        return $previousUrl;
    }

    /**
     * Get dashboard route based on user role
     */
    private function getDashboardRoute(): string
    {
        $user = auth()->user();
        
        if (!$user) {
            return route('login');
        }

        if ($user->hasRole('admin')) {
            return route('admin.dashboard');
        } elseif ($user->hasRole('doctor')) {
            return route('doctor.dashboard');
        } elseif ($user->hasRole('patient')) {
            return route('patient.dashboard');
        } elseif ($user->hasRole('branch_head')) {
            return route('branch-head.dashboard');
        }

        return route('dashboard');
    }

    /**
     * Log exception with context
     */
    private function logException(TherapyAppException $exception, Request $request): void
    {
        $context = [
            'exception_type' => get_class($exception),
            'error_code' => $exception->getErrorCode(),
            'error_type' => $exception->getErrorType(),
            'user_id' => auth()->id(),
            'request_url' => $request->fullUrl(),
            'request_method' => $request->method(),
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'exception_context' => $exception->getContext(),
        ];

        // Log as warning for business rule violations, error for others
        $logLevel = match (get_class($exception)) {
            BusinessRuleViolationException::class,
            PackageValidationException::class,
            AppointmentConflictException::class => 'warning',
            default => 'error',
        };

        Log::$logLevel($exception->getMessage(), $context);
    }

    /**
     * Check if exception should trigger automatic recovery
     */
    public function shouldAttemptRecovery(TherapyAppException $exception): bool
    {
        return match (get_class($exception)) {
            AppointmentConflictException::class => true,
            DataIntegrityException::class => false, // Requires manual intervention
            BranchAccessDeniedException::class => false, // Security issue
            default => false,
        };
    }

    /**
     * Attempt automatic recovery for specific exceptions
     */
    public function attemptRecovery(TherapyAppException $exception, array $originalData = []): array
    {
        return match (get_class($exception)) {
            AppointmentConflictException::class => $this->suggestAlternativeAppointmentTimes($originalData),
            default => [],
        };
    }

    /**
     * Suggest alternative appointment times for conflicts
     */
    private function suggestAlternativeAppointmentTimes(array $appointmentData): array
    {
        if (!isset($appointmentData['doctor_id'], $appointmentData['appointment_date'])) {
            return [];
        }

        // This would integrate with the appointment service to find alternatives
        // For now, return empty array as this requires complex scheduling logic
        return [];
    }
}
