<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add indexes for better performance
        Schema::table('appointments', function (Blueprint $table) {
            $table->index(['doctor_id', 'appointment_date']);
            $table->index(['patient_id', 'appointment_date']);
            $table->index(['branch_id', 'appointment_date']);
            $table->index(['status', 'appointment_date']);
            $table->index('appointment_date');
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->unique('license_number');
            $table->index(['branch_id', 'is_active']);
            $table->index('is_active');
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->index(['branch_id', 'is_active']);
            $table->index('is_active');
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->index(['patient_id', 'record_date']);
            $table->index(['doctor_id', 'record_date']);
            $table->index('record_date');
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->index('is_active');
            $table->unique('email');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->index(['branch_id', 'is_active']);
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropIndex(['doctor_id', 'appointment_date']);
            $table->dropIndex(['patient_id', 'appointment_date']);
            $table->dropIndex(['branch_id', 'appointment_date']);
            $table->dropIndex(['status', 'appointment_date']);
            $table->dropIndex(['appointment_date']);
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->dropUnique(['license_number']);
            $table->dropIndex(['branch_id', 'is_active']);
            $table->dropIndex(['is_active']);
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->dropIndex(['branch_id', 'is_active']);
            $table->dropIndex(['is_active']);
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->dropIndex(['patient_id', 'record_date']);
            $table->dropIndex(['doctor_id', 'record_date']);
            $table->dropIndex(['record_date']);
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
            $table->dropUnique(['email']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['branch_id', 'is_active']);
            $table->dropIndex(['is_active']);
        });
    }
};
