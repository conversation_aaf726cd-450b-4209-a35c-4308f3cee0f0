import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, CheckCircle, XCircle, AlertTriangle, Info, Loader } from 'lucide-react';

const Toast = ({ 
    id, 
    type = 'success', 
    title, 
    message, 
    duration = 5000, 
    position = 'top-right',
    onClose 
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isLeaving, setIsLeaving] = useState(false);

    useEffect(() => {
        // Show animation
        const showTimer = setTimeout(() => setIsVisible(true), 10);
        
        // Auto close
        const closeTimer = setTimeout(() => {
            handleClose();
        }, duration);

        return () => {
            clearTimeout(showTimer);
            clearTimeout(closeTimer);
        };
    }, [duration]);

    const handleClose = () => {
        setIsLeaving(true);
        setTimeout(() => {
            onClose(id);
        }, 300);
    };

    const getIcon = () => {
        switch (type) {
            case 'success':
                return <CheckCircle className="h-5 w-5 text-green-500" />;
            case 'error':
                return <XCircle className="h-5 w-5 text-red-500" />;
            case 'warning':
                return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
            case 'info':
                return <Info className="h-5 w-5 text-blue-500" />;
            case 'loading':
                return <Loader className="h-5 w-5 text-blue-500 animate-spin" />;
            default:
                return <CheckCircle className="h-5 w-5 text-green-500" />;
        }
    };

    const getColorClasses = () => {
        switch (type) {
            case 'success':
                return 'border-l-green-500 bg-green-50';
            case 'error':
                return 'border-l-red-500 bg-red-50';
            case 'warning':
                return 'border-l-yellow-500 bg-yellow-50';
            case 'info':
                return 'border-l-blue-500 bg-blue-50';
            case 'loading':
                return 'border-l-blue-500 bg-blue-50';
            default:
                return 'border-l-green-500 bg-green-50';
        }
    };

    const getPositionClasses = () => {
        switch (position) {
            case 'top-left':
                return 'top-4 left-4';
            case 'top-center':
                return 'top-4 left-1/2 transform -translate-x-1/2';
            case 'top-right':
                return 'top-4 right-4';
            case 'bottom-left':
                return 'bottom-4 left-4';
            case 'bottom-center':
                return 'bottom-4 left-1/2 transform -translate-x-1/2';
            case 'bottom-right':
                return 'bottom-4 right-4';
            default:
                return 'top-4 right-4';
        }
    };

    const getAnimationClasses = () => {
        if (isLeaving) {
            return 'animate-slide-out-right opacity-0';
        }
        return isVisible ? 'animate-slide-in-right opacity-100' : 'opacity-0 translate-x-full';
    };

    return (
        <div
            className={`
                fixed z-50 max-w-sm w-full pointer-events-auto
                ${getPositionClasses()}
                ${getAnimationClasses()}
                transition-all duration-300 ease-in-out
            `}
        >
            <div className={`
                rounded-lg border-l-4 p-4 shadow-lg backdrop-blur-sm
                ${getColorClasses()}
                border border-gray-200
            `}>
                <div className="flex items-start">
                    <div className="flex-shrink-0">
                        {getIcon()}
                    </div>
                    <div className="ml-3 flex-1">
                        <h3 className="text-sm font-medium text-gray-900">
                            {title}
                        </h3>
                        {message && (
                            <p className="mt-1 text-sm text-gray-600">
                                {message}
                            </p>
                        )}
                    </div>
                    {type !== 'loading' && (
                        <div className="ml-4 flex-shrink-0">
                            <button
                                onClick={handleClose}
                                className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 rounded-md p-1"
                            >
                                <X className="h-4 w-4" />
                            </button>
                        </div>
                    )}
                </div>
                
                {/* Progress bar for auto-close */}
                {type !== 'loading' && duration > 0 && (
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-1">
                        <div 
                            className="bg-gray-400 h-1 rounded-full animate-progress"
                            style={{ animationDuration: `${duration}ms` }}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

// Toast Container Component
export const ToastContainer = ({ toasts, onRemove }) => {
    if (!toasts || toasts.length === 0) return null;

    return createPortal(
        <div className="fixed inset-0 pointer-events-none z-50">
            {toasts.map((toast) => (
                <Toast
                    key={toast.id}
                    {...toast}
                    onClose={onRemove}
                />
            ))}
        </div>,
        document.body
    );
};

export default Toast;
