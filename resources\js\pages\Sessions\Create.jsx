import React, { useState, useEffect } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
    Calendar,
    Clock,
    User,
    AlertTriangle
} from 'lucide-react';

export default function CreateSession({ auth, patientPackage, doctors, patients, packages }) {
    const { data, setData, post, processing, errors } = useForm({
        patient_package_id: patientPackage?.id || '',
        patient_id: patientPackage?.patient_id || '',
        doctor_id: patientPackage?.assigned_doctor_id || '',
        session_number: 1,
        scheduled_date: '',
        scheduled_time: '09:00',
        duration_minutes: 60,
        session_type: 'individual',
        location: '',
        session_notes: '',
        preparation_notes: '',
        session_goals: []
    });

    const [availableSlots, setAvailableSlots] = useState([]);
    const [loadingSlots, setLoadingSlots] = useState(false);
    const [selectedPackage, setSelectedPackage] = useState(patientPackage);

    useEffect(() => {
        if (data.doctor_id && data.scheduled_date) {
            fetchAvailableSlots();
        }
    }, [data.doctor_id, data.scheduled_date, data.duration_minutes]);

    useEffect(() => {
        if (data.patient_package_id) {
            const pkg = packages?.find(p => p.id == data.patient_package_id);
            setSelectedPackage(pkg);
            if (pkg) {
                setData(prev => ({
                    ...prev,
                    patient_id: pkg.patient_id,
                    doctor_id: pkg.assigned_doctor_id || prev.doctor_id
                }));
                
                // Calculate next session number
                const nextSessionNumber = (pkg.sessions_completed || 0) + 1;
                setData('session_number', nextSessionNumber);
            }
        }
    }, [data.patient_package_id]);

    const fetchAvailableSlots = async () => {
        if (!data.doctor_id || !data.scheduled_date) return;
        
        setLoadingSlots(true);
        try {
            const response = await fetch(`/sessions/available-slots?doctor_id=${data.doctor_id}&date=${data.scheduled_date}&duration=${data.duration_minutes}`);
            const result = await response.json();
            setAvailableSlots(result.slots || []);
        } catch (error) {
            console.error('Error fetching available slots:', error);
            setAvailableSlots([]);
        } finally {
            setLoadingSlots(false);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/sessions', {
            onSuccess: () => {
                router.get('/sessions');
            }
        });
    };

    const addGoal = () => {
        setData('session_goals', [...data.session_goals, '']);
    };

    const updateGoal = (index, value) => {
        const newGoals = [...data.session_goals];
        newGoals[index] = value;
        setData('session_goals', newGoals);
    };

    const removeGoal = (index) => {
        const newGoals = data.session_goals.filter((_, i) => i !== index);
        setData('session_goals', newGoals);
    };

    const isSlotAvailable = (time) => {
        return availableSlots.some(slot => slot.time === time && slot.available);
    };

    return (
        <AppLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Schedule New Session
                    </h2>
                    <button
                        onClick={() => router.get('/sessions')}
                        className="text-gray-600 hover:text-gray-900"
                    >
                        ← Back to Schedule
                    </button>
                </div>
            }
        >
            <Head title="Schedule Session" />

            <div className="py-6">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Session Details</h3>
                        </div>
                        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
                            {/* Patient Package Selection */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Patient Package *
                                </label>
                                <select
                                    value={data.patient_package_id}
                                    onChange={(e) => setData('patient_package_id', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    required
                                >
                                    <option value="">Select a patient package...</option>
                                    {packages?.map(pkg => (
                                        <option key={pkg.id} value={pkg.id}>
                                            {pkg.patient.name} - {pkg.therapy_package.name} 
                                            ({pkg.sessions_completed}/{pkg.therapy_package.total_sessions} sessions)
                                        </option>
                                    ))}
                                </select>
                                {errors.patient_package_id && (
                                    <p className="mt-1 text-sm text-red-600">{errors.patient_package_id}</p>
                                )}
                            </div>

                            {/* Package Info Display */}
                            {selectedPackage && (
                                <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                                    <div className="flex items-start">
                                        <div className="flex-shrink-0">
                                            <User className="h-5 w-5 text-blue-400" />
                                        </div>
                                        <div className="ml-3">
                                            <h4 className="text-sm font-medium text-blue-800">
                                                Package Information
                                            </h4>
                                            <div className="mt-2 text-sm text-blue-700">
                                                <p><strong>Patient:</strong> {selectedPackage.patient.name}</p>
                                                <p><strong>Package:</strong> {selectedPackage.therapy_package.name}</p>
                                                <p><strong>Progress:</strong> {selectedPackage.sessions_completed}/{selectedPackage.therapy_package.total_sessions} sessions completed</p>
                                                <p><strong>Remaining:</strong> {selectedPackage.sessions_remaining} sessions</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Doctor Selection */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Doctor *
                                </label>
                                <select
                                    value={data.doctor_id}
                                    onChange={(e) => setData('doctor_id', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    required
                                >
                                    <option value="">Select a doctor...</option>
                                    {doctors?.map(doctor => (
                                        <option key={doctor.id} value={doctor.id}>
                                            Dr. {doctor.name}
                                        </option>
                                    ))}
                                </select>
                                {errors.doctor_id && (
                                    <p className="mt-1 text-sm text-red-600">{errors.doctor_id}</p>
                                )}
                            </div>

                            {/* Session Number */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Number *
                                </label>
                                <input
                                    type="number"
                                    min="1"
                                    value={data.session_number}
                                    onChange={(e) => setData('session_number', parseInt(e.target.value))}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    required
                                />
                                {errors.session_number && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_number}</p>
                                )}
                            </div>

                            {/* Date and Time */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Date *
                                    </label>
                                    <input
                                        type="date"
                                        value={data.scheduled_date}
                                        onChange={(e) => setData('scheduled_date', e.target.value)}
                                        min={new Date().toISOString().split('T')[0]}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        required
                                    />
                                    {errors.scheduled_date && (
                                        <p className="mt-1 text-sm text-red-600">{errors.scheduled_date}</p>
                                    )}
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Time *
                                    </label>
                                    <select
                                        value={data.scheduled_time}
                                        onChange={(e) => setData('scheduled_time', e.target.value)}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                        required
                                    >
                                        {loadingSlots ? (
                                            <option>Loading available times...</option>
                                        ) : availableSlots.length > 0 ? (
                                            availableSlots.map(slot => (
                                                <option 
                                                    key={slot.time} 
                                                    value={slot.time}
                                                    disabled={!slot.available}
                                                >
                                                    {slot.formatted_time} {!slot.available ? '(Unavailable)' : ''}
                                                </option>
                                            ))
                                        ) : (
                                            <option value="">No available times</option>
                                        )}
                                    </select>
                                    {errors.scheduled_time && (
                                        <p className="mt-1 text-sm text-red-600">{errors.scheduled_time}</p>
                                    )}
                                    {data.scheduled_time && !isSlotAvailable(data.scheduled_time) && (
                                        <p className="mt-1 text-sm text-yellow-600 flex items-center">
                                            <AlertTriangle className="h-4 w-4 mr-1" />
                                            This time slot may not be available
                                        </p>
                                    )}
                                </div>
                            </div>

                            {/* Duration and Type */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Duration (minutes) *
                                    </label>
                                    <select
                                        value={data.duration_minutes}
                                        onChange={(e) => setData('duration_minutes', parseInt(e.target.value))}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                        <option value={30}>30 minutes</option>
                                        <option value={45}>45 minutes</option>
                                        <option value={60}>60 minutes</option>
                                        <option value={90}>90 minutes</option>
                                        <option value={120}>120 minutes</option>
                                    </select>
                                    {errors.duration_minutes && (
                                        <p className="mt-1 text-sm text-red-600">{errors.duration_minutes}</p>
                                    )}
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Session Type *
                                    </label>
                                    <select
                                        value={data.session_type}
                                        onChange={(e) => setData('session_type', e.target.value)}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                        <option value="individual">Individual</option>
                                        <option value="group">Group</option>
                                        <option value="assessment">Assessment</option>
                                        <option value="follow_up">Follow-up</option>
                                    </select>
                                    {errors.session_type && (
                                        <p className="mt-1 text-sm text-red-600">{errors.session_type}</p>
                                    )}
                                </div>
                            </div>

                            {/* Location */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Location
                                </label>
                                <input
                                    type="text"
                                    value={data.location}
                                    onChange={(e) => setData('location', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="e.g., Room 1, Online, etc."
                                />
                                {errors.location && (
                                    <p className="mt-1 text-sm text-red-600">{errors.location}</p>
                                )}
                            </div>

                            {/* Session Notes */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Notes
                                </label>
                                <textarea
                                    value={data.session_notes}
                                    onChange={(e) => setData('session_notes', e.target.value)}
                                    rows={3}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="Any notes about this session..."
                                />
                                {errors.session_notes && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_notes}</p>
                                )}
                            </div>

                            {/* Preparation Notes */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Preparation Notes
                                </label>
                                <textarea
                                    value={data.preparation_notes}
                                    onChange={(e) => setData('preparation_notes', e.target.value)}
                                    rows={3}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="What should be prepared for this session..."
                                />
                                {errors.preparation_notes && (
                                    <p className="mt-1 text-sm text-red-600">{errors.preparation_notes}</p>
                                )}
                            </div>

                            {/* Session Goals */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Goals
                                </label>
                                <div className="space-y-2">
                                    {data.session_goals.map((goal, index) => (
                                        <div key={index} className="flex items-center space-x-2">
                                            <input
                                                type="text"
                                                value={goal}
                                                onChange={(e) => updateGoal(index, e.target.value)}
                                                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                placeholder={`Goal ${index + 1}`}
                                            />
                                            <button
                                                type="button"
                                                onClick={() => removeGoal(index)}
                                                className="text-red-600 hover:text-red-800"
                                            >
                                                Remove
                                            </button>
                                        </div>
                                    ))}
                                    <button
                                        type="button"
                                        onClick={addGoal}
                                        className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                                    >
                                        + Add Goal
                                    </button>
                                </div>
                                {errors.session_goals && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_goals}</p>
                                )}
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                                <button
                                    type="button"
                                    onClick={() => router.get('/sessions')}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {processing ? 'Scheduling...' : 'Schedule Session'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
