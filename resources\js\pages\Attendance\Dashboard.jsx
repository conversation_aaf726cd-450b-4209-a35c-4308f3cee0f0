import React, { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
    Calendar,
    Clock,
    Users,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Search,
    Plus
} from 'lucide-react';

export default function AttendanceDashboard({ auth, sessions, stats, selectedDate }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('all');
    const [isLoading, setIsLoading] = useState(false);

    const filteredSessions = sessions.filter(session => {
        const matchesSearch = session.patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            session.patient.email.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = filterStatus === 'all' || 
                            (session.attendance?.attendance_status === filterStatus) ||
                            (filterStatus === 'scheduled' && !session.attendance);
        return matchesSearch && matchesStatus;
    });

    const getStatusBadge = (session) => {
        if (!session.attendance) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <Calendar className="w-3 h-3 mr-1" />
                    Scheduled
                </span>
            );
        }

        const status = session.attendance.attendance_status;
        const badges = {
            present: (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Present
                </span>
            ),
            absent: (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <XCircle className="w-3 h-3 mr-1" />
                    Absent
                </span>
            ),
            late: (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <Clock className="w-3 h-3 mr-1" />
                    Late ({session.attendance.late_minutes}m)
                </span>
            ),
            excused: (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Excused
                </span>
            ),
            partial: (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    <Clock className="w-3 h-3 mr-1" />
                    Partial
                </span>
            )
        };

        return badges[status] || badges.present;
    };

    const handleQuickCheckin = async (sessionId, status) => {
        setIsLoading(true);
        try {
            await router.post('/attendance/quick-checkin', {
                session_id: sessionId,
                attendance_status: status,
                check_in_time: new Date().toISOString(),
                marking_method: 'quick'
            });
        } catch (error) {
            console.error('Error marking attendance:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const StatCard = ({ title, value, subtitle, icon: Icon, color }) => (
        <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        <Icon className={`h-6 w-6 ${color}`} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                        <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
                            <dd className="text-lg font-medium text-gray-900">{value}</dd>
                            {subtitle && <dd className="text-sm text-gray-500">{subtitle}</dd>}
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    );

    return (
        <AppLayout>
            <Head title="Attendance Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8 flex justify-between items-center">
                        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                            Attendance Dashboard
                        </h2>
                        <div className="flex space-x-2">
                            <input
                                type="date"
                                value={selectedDate}
                                onChange={(e) => router.get('/attendance/dashboard', { date: e.target.value })}
                                className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                            />
                            {auth.user.roles.some(role => ['admin', 'branch_head', 'doctor'].includes(role.name)) && (
                                <button
                                    onClick={() => router.get('/sessions/create')}
                                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Schedule Session
                                </button>
                            )}
                        </div>
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <StatCard
                            title="Total Sessions"
                            value={stats.total_sessions}
                            icon={Calendar}
                            color="text-gray-600"
                        />
                        <StatCard
                            title="Present"
                            value={stats.present_count}
                            subtitle={`${stats.attendance_rate}%`}
                            icon={CheckCircle}
                            color="text-green-600"
                        />
                        <StatCard
                            title="Absent"
                            value={stats.absent_count}
                            icon={XCircle}
                            color="text-red-600"
                        />
                        <StatCard
                            title="Late"
                            value={stats.late_count}
                            icon={Clock}
                            color="text-yellow-600"
                        />
                    </div>

                    {/* Filters */}
                    <div className="bg-white shadow rounded-lg mb-6">
                        <div className="px-4 py-3 border-b border-gray-200">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                <div className="flex-1 min-w-0">
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <Search className="h-5 w-5 text-gray-400" />
                                        </div>
                                        <input
                                            type="text"
                                            placeholder="Search patients..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                        />
                                    </div>
                                </div>
                                <div className="flex space-x-2">
                                    <select
                                        value={filterStatus}
                                        onChange={(e) => setFilterStatus(e.target.value)}
                                        className="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                        <option value="all">All Status</option>
                                        <option value="scheduled">Scheduled</option>
                                        <option value="present">Present</option>
                                        <option value="absent">Absent</option>
                                        <option value="late">Late</option>
                                        <option value="excused">Excused</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Sessions List */}
                        <div className="divide-y divide-gray-200">
                            {filteredSessions.length === 0 ? (
                                <div className="p-6 text-center text-gray-500">
                                    <Users className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No sessions found</h3>
                                    <p className="mt-1 text-sm text-gray-500">
                                        {searchTerm || filterStatus !== 'all' 
                                            ? 'Try adjusting your search or filter criteria.'
                                            : 'No sessions scheduled for this date.'
                                        }
                                    </p>
                                </div>
                            ) : (
                                filteredSessions.map((session) => (
                                    <div key={session.id} className="p-4 hover:bg-gray-50">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                                        <span className="text-sm font-medium text-indigo-800">
                                                            {session.patient.name.charAt(0)}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <div className="flex items-center space-x-2">
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {session.patient.name}
                                                        </p>
                                                        {getStatusBadge(session)}
                                                    </div>
                                                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                                                        <span className="flex items-center">
                                                            <Clock className="w-4 h-4 mr-1" />
                                                            {session.scheduled_time}
                                                        </span>
                                                        <span>Dr. {session.doctor.name}</span>
                                                        <span>{session.patient_package.therapy_package.name}</span>
                                                        {session.location && <span>📍 {session.location}</span>}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                {!session.attendance && (
                                                    <>
                                                        <button
                                                            onClick={() => handleQuickCheckin(session.id, 'present')}
                                                            disabled={isLoading}
                                                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                                                        >
                                                            ✓ Present
                                                        </button>
                                                        <button
                                                            onClick={() => handleQuickCheckin(session.id, 'absent')}
                                                            disabled={isLoading}
                                                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                                                        >
                                                            ✗ Absent
                                                        </button>
                                                        <button
                                                            onClick={() => router.get(`/attendance/mark?session=${session.id}`)}
                                                            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                        >
                                                            📝 Details
                                                        </button>
                                                    </>
                                                )}
                                                {session.attendance && session.attendance.attendance_status === 'present' && !session.attendance.session_completion && (
                                                    <button
                                                        onClick={() => router.get(`/attendance/complete-session?session=${session.id}`)}
                                                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                    >
                                                        Complete Session
                                                    </button>
                                                )}
                                                <button
                                                    onClick={() => router.get(`/sessions/${session.id}`)}
                                                    className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                                >
                                                    View
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
