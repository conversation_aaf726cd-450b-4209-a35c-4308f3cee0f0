import { useState, useEffect } from 'react';
import { router } from '@inertiajs/react';
import Modal from '@/Components/ui/Modal';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { useToast } from '@/contexts/ToastContext';
import { Save, X, Loader2 } from 'lucide-react';

const UserEditModal = ({ isOpen, onClose, userData, onSuccess }) => {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        role: '',
        phone: '',
        is_active: true
    });
    const [loading, setLoading] = useState(false);
    const [errors, setErrors] = useState({});
    const toast = useToast();

    const roles = [
        { value: 'admin', label: 'Admin' },
        { value: 'branch_head', label: 'Branch Head' },
        { value: 'doctor', label: 'Doctor' },
        { value: 'patient', label: 'Patient' }
    ];

    useEffect(() => {
        if (userData) {
            setFormData({
                name: userData.name || '',
                email: userData.email || '',
                role: userData.roles?.[0]?.name || '',
                phone: userData.phone || '',
                is_active: userData.is_active ?? true
            });
        }
    }, [userData]);

    const handleInputChange = (name, value) => {
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({
                ...prev,
                [name]: null
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        router.put(`/users/${userData.id}`, formData, {
            onSuccess: () => {
                toast.success('User Updated!', 'User information has been updated successfully.');
                handleClose();
                if (onSuccess) onSuccess();
            },
            onError: (errors) => {
                setErrors(errors);
                const errorMessage = errors.message || 'Please check the form and try again.';
                toast.error('Update Failed', errorMessage);
            },
            onFinish: () => {
                setLoading(false);
            }
        });
    };

    const handleClose = () => {
        if (!loading) {
            setErrors({});
            onClose();
        }
    };

    const footerContent = (
        <>
            <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
            >
                <X className="h-4 w-4 mr-2" />
                Cancel
            </Button>
            <Button
                type="submit"
                form="user-edit-form"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
            >
                {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                    <Save className="h-4 w-4 mr-2" />
                )}
                {loading ? 'Updating...' : 'Update User'}
            </Button>
        </>
    );

    return (
        <Modal
            isOpen={isOpen}
            onClose={handleClose}
            title={`Edit User: ${userData?.name || ''}`}
            size="lg"
            showFooter={true}
            footerContent={footerContent}
            closeOnOverlayClick={!loading}
            closeOnEscape={!loading}
            loading={loading}
        >
            <form id="user-edit-form" onSubmit={handleSubmit} className="space-y-6">
                {/* Name and Email */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium">
                            Full Name <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="name"
                            type="text"
                            value={formData.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            placeholder="Enter full name"
                            required
                            className={errors.name ? 'border-red-500' : ''}
                        />
                        {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium">
                            Email Address <span className="text-red-500">*</span>
                        </Label>
                        <Input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            placeholder="Enter email address"
                            required
                            className={errors.email ? 'border-red-500' : ''}
                        />
                        {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                    </div>
                </div>

                {/* Role and Phone */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="role" className="text-sm font-medium">
                            Role <span className="text-red-500">*</span>
                        </Label>
                        <Select
                            value={formData.role}
                            onValueChange={(value) => handleInputChange('role', value)}
                        >
                            <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                                <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent>
                                {roles.map((role) => (
                                    <SelectItem key={role.value} value={role.value}>
                                        {role.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.role && <p className="text-sm text-red-500">{errors.role}</p>}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="phone" className="text-sm font-medium">
                            Phone Number
                        </Label>
                        <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            placeholder="Enter phone number"
                            className={errors.phone ? 'border-red-500' : ''}
                        />
                        {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                    </div>
                </div>

                {/* Status */}
                <div className="space-y-2">
                    <Label htmlFor="is_active" className="text-sm font-medium">
                        Status
                    </Label>
                    <Select
                        value={formData.is_active ? 'true' : 'false'}
                        onValueChange={(value) => handleInputChange('is_active', value === 'true')}
                    >
                        <SelectTrigger className={errors.is_active ? 'border-red-500' : ''}>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="true">Active</SelectItem>
                            <SelectItem value="false">Inactive</SelectItem>
                        </SelectContent>
                    </Select>
                    {errors.is_active && <p className="text-sm text-red-500">{errors.is_active}</p>}
                </div>

                {/* User Info */}
                {userData && (
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">User Information</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                            <div>
                                <span className="font-medium">User ID:</span> #{userData.id}
                            </div>
                            <div>
                                <span className="font-medium">Current Role:</span> {userData.role}
                            </div>
                            <div>
                                <span className="font-medium">Created:</span> {new Date(userData.created_at).toLocaleDateString()}
                            </div>
                            <div>
                                <span className="font-medium">Last Updated:</span> {new Date(userData.updated_at).toLocaleDateString()}
                            </div>
                            {userData.last_login_at && (
                                <div>
                                    <span className="font-medium">Last Login:</span> {new Date(userData.last_login_at).toLocaleDateString()}
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Password Reset Note */}
                <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">Password Reset</h4>
                    <p className="text-sm text-blue-700">
                        To change the user's password, use the separate password reset functionality or ask the user to reset their password through the forgot password feature.
                    </p>
                </div>
            </form>
        </Modal>
    );
};

export default UserEditModal;
