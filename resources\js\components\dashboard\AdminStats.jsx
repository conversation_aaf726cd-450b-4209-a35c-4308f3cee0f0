import StatsCard from '@/Components/ui/StatsCard';
import { Users, Building2, Calendar } from 'lucide-react';

export default function AdminStats({ stats }) {
    return (
        <>
            <StatsCard 
                title="Total Branches"
                icon={Building2}
                value={stats.total_branches}
                subtitle={`${stats.active_branches || 0} active`}
            />
            
            <StatsCard 
                title="Total Doctors"
                icon={Users}
                value={stats.total_doctors}
                subtitle="Across all branches"
            />
            
            <StatsCard 
                title="Total Patients"
                icon={Users}
                value={stats.total_patients}
                subtitle="Registered patients"
            />
            
            <StatsCard 
                title="Today's Appointments"
                icon={Calendar}
                value={stats.todays_appointments}
                subtitle={`${stats.completed_today || 0} completed`}
            />
        </>
    );
}