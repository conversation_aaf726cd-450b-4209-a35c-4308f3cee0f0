<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use App\Exceptions\BranchAccessDeniedException;

trait BranchScoped
{
    /**
     * Scope query to user's accessible branches
     */
    public function scopeForUser(Builder $query, $user = null)
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            return $query->whereRaw('1 = 0'); // Return no results
        }

        // Admin can see all branches
        if ($user->hasRole('admin')) {
            return $query;
        }

        $branchId = $this->getUserBranchId($user);
        
        if (!$branchId) {
            return $query->whereRaw('1 = 0'); // Return no results
        }

        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope query to specific branch with access check
     */
    public function scopeForBranch(Builder $query, int $branchId, $user = null)
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            throw new BranchAccessDeniedException('Authentication required');
        }

        // Admin can access any branch
        if ($user->hasRole('admin')) {
            return $query->where('branch_id', $branchId);
        }

        $userBranchId = $this->getUserBranchId($user);
        
        if ($userBranchId !== $branchId) {
            throw new BranchAccessDeniedException(
                'Access denied to branch',
                403,
                null,
                [
                    'user_id' => $user->id,
                    'requested_branch_id' => $branchId,
                    'user_branch_id' => $userBranchId
                ]
            );
        }

        return $query->where('branch_id', $branchId);
    }

    /**
     * Get user's branch ID
     */
    private function getUserBranchId($user): ?int
    {
        if ($user->hasRole('branch_head')) {
            return $user->managedBranch?->id;
        }

        if ($user->hasRole('doctor')) {
            return $user->doctorProfile?->branch_id;
        }

        if ($user->hasRole('patient')) {
            return $user->patientProfile?->branch_id;
        }

        return null;
    }

    /**
     * Ensure the model belongs to user's branch before saving
     */
    public function ensureBranchAccess($user = null)
    {
        $user = $user ?? auth()->user();
        
        if (!$user || $user->hasRole('admin')) {
            return;
        }

        $userBranchId = $this->getUserBranchId($user);
        
        if ($this->branch_id && $this->branch_id !== $userBranchId) {
            throw new BranchAccessDeniedException(
                'Cannot access resource from different branch',
                403,
                null,
                [
                    'user_id' => $user->id,
                    'resource_branch_id' => $this->branch_id,
                    'user_branch_id' => $userBranchId
                ]
            );
        }

        // Auto-assign branch if not set
        if (!$this->branch_id && $userBranchId) {
            $this->branch_id = $userBranchId;
        }
    }
}
