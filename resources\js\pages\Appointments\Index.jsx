import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Calendar, Clock, Plus, Eye, Edit, X, CheckCircle, User } from 'lucide-react';

export default function AppointmentsIndex({ auth, appointments, userRole, stats, canCreate, canManageAll }) {
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const formatTime = (dateString) => {
        return new Date(dateString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'confirmed': return 'bg-green-100 text-green-800';
            case 'scheduled': return 'bg-blue-100 text-blue-800';
            case 'completed': return 'bg-gray-100 text-gray-800';
            case 'cancelled': return 'bg-red-100 text-red-800';
            case 'in_progress': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleCancel = (id) => {
        if (confirm('Are you sure you want to cancel this appointment?')) {
            router.post(route('appointments.cancel', id));
        }
    };

    return (
        <AppLayout>
            <Head title="Appointments" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Appointments</h1>
                            <p className="text-gray-600">
                                {userRole === 'patient' && 'Manage your therapy appointments'}
                                {userRole === 'doctor' && 'Your patient appointments'}
                                {userRole === 'branch_head' && 'Branch appointments overview'}
                                {userRole === 'admin' && 'System-wide appointments'}
                            </p>
                        </div>
                        
                        {canCreate && (
                            <Link href={route('appointments.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    {userRole === 'patient' ? 'Book Appointment' : 'New Appointment'}
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.today}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.upcoming}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.completed}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
                                <X className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.cancelled}</div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Appointments List */}
                    <Card>
                        <CardHeader>
                            <CardTitle>All Appointments</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {appointments.length > 0 ? (
                                <div className="space-y-4">
                                    {appointments.map((appointment) => (
                                        <div key={appointment.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                                        <span className="font-medium">
                                                            {formatDate(appointment.appointment_date)} at {formatTime(appointment.appointment_date)}
                                                        </span>
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                                                            {appointment.status}
                                                        </span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                                        {(userRole === 'admin' || userRole === 'branch_head' || userRole === 'patient') && appointment.doctor && (
                                                            <span className="flex items-center gap-1">
                                                                <User className="h-3 w-3" />
                                                                Dr. {appointment.doctor.name}
                                                            </span>
                                                        )}
                                                        
                                                        {(userRole === 'admin' || userRole === 'branch_head' || userRole === 'doctor') && appointment.patient && (
                                                            <span className="flex items-center gap-1">
                                                                <User className="h-3 w-3" />
                                                                {appointment.patient.name}
                                                            </span>
                                                        )}
                                                        
                                                        <span>
                                                            Duration: {appointment.duration_minutes || 60} minutes
                                                        </span>
                                                        
                                                        {appointment.fee && (
                                                            <span>Fee: ${appointment.fee}</span>
                                                        )}
                                                    </div>

                                                    {appointment.symptoms && (
                                                        <p className="text-sm text-gray-600 mb-2">
                                                            <strong>Symptoms:</strong> {appointment.symptoms}
                                                        </p>
                                                    )}

                                                    {appointment.notes && (
                                                        <p className="text-sm text-gray-600">
                                                            <strong>Notes:</strong> {appointment.notes}
                                                        </p>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2">
                                                    <Link href={route('appointments.show', appointment.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {(canManageAll || (userRole === 'doctor' && appointment.doctor_id === auth.user.id)) && 
                                                     appointment.status !== 'completed' && appointment.status !== 'cancelled' && (
                                                        <Link href={route('appointments.edit', appointment.id)}>
                                                            <Button variant="outline" size="sm">
                                                                <Edit className="h-4 w-4" />
                                                            </Button>
                                                        </Link>
                                                    )}
                                                    
                                                    {appointment.status !== 'completed' && appointment.status !== 'cancelled' && (
                                                        <Button 
                                                            variant="outline" 
                                                            size="sm"
                                                            onClick={() => handleCancel(appointment.id)}
                                                            className="text-red-600 hover:text-red-700"
                                                        >
                                                            <X className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No appointments found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        {userRole === 'patient' 
                                            ? 'You have no appointments scheduled. Book your first session!'
                                            : 'No appointments are currently scheduled.'
                                        }
                                    </p>
                                    {canCreate && (
                                        <Link href={route('appointments.create')}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                {userRole === 'patient' ? 'Book Appointment' : 'Schedule Appointment'}
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}