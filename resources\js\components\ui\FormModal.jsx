import { useState, useEffect } from 'react';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { useModal } from '@/contexts/ModalContext';
import { useToast } from '@/contexts/ToastContext';
import { Save, X, Loader2 } from 'lucide-react';

// Generic Form Modal Component
export const FormModal = ({
    title,
    fields,
    initialData = {},
    onSubmit,
    onCancel,
    submitText = 'Save',
    cancelText = 'Cancel',
    size = 'lg',
    loading = false,
    validationErrors = {}
}) => {
    const [formData, setFormData] = useState(initialData);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const toast = useToast();

    useEffect(() => {
        setFormData(initialData);
    }, [initialData]);

    const handleInputChange = (name, value) => {
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            await onSubmit(formData);
        } catch (error) {
            console.error('Form submission error:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    const renderField = (field) => {
        const { name, label, type, required, options, placeholder, rows } = field;
        const value = formData[name] || '';
        const error = validationErrors[name];

        switch (type) {
            case 'text':
            case 'email':
            case 'password':
            case 'number':
                return (
                    <div key={name} className="space-y-2">
                        <Label htmlFor={name} className="text-sm font-medium">
                            {label} {required && <span className="text-red-500">*</span>}
                        </Label>
                        <Input
                            id={name}
                            type={type}
                            value={value}
                            onChange={(e) => handleInputChange(name, e.target.value)}
                            placeholder={placeholder}
                            required={required}
                            className={error ? 'border-red-500' : ''}
                        />
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            case 'textarea':
                return (
                    <div key={name} className="space-y-2">
                        <Label htmlFor={name} className="text-sm font-medium">
                            {label} {required && <span className="text-red-500">*</span>}
                        </Label>
                        <Textarea
                            id={name}
                            value={value}
                            onChange={(e) => handleInputChange(name, e.target.value)}
                            placeholder={placeholder}
                            required={required}
                            rows={rows || 3}
                            className={error ? 'border-red-500' : ''}
                        />
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            case 'select':
                return (
                    <div key={name} className="space-y-2">
                        <Label htmlFor={name} className="text-sm font-medium">
                            {label} {required && <span className="text-red-500">*</span>}
                        </Label>
                        <Select
                            value={value}
                            onValueChange={(val) => handleInputChange(name, val)}
                            required={required}
                        >
                            <SelectTrigger className={error ? 'border-red-500' : ''}>
                                <SelectValue placeholder={placeholder} />
                            </SelectTrigger>
                            <SelectContent>
                                {options?.map((option) => (
                                    <SelectItem key={option.value} value={option.value}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            case 'checkbox':
                return (
                    <div key={name} className="flex items-center space-x-2">
                        <input
                            id={name}
                            type="checkbox"
                            checked={value}
                            onChange={(e) => handleInputChange(name, e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <Label htmlFor={name} className="text-sm font-medium">
                            {label}
                        </Label>
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            case 'date':
                return (
                    <div key={name} className="space-y-2">
                        <Label htmlFor={name} className="text-sm font-medium">
                            {label} {required && <span className="text-red-500">*</span>}
                        </Label>
                        <Input
                            id={name}
                            type="date"
                            value={value}
                            onChange={(e) => handleInputChange(name, e.target.value)}
                            required={required}
                            className={error ? 'border-red-500' : ''}
                        />
                        {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                );

            default:
                return null;
        }
    };

    const footerContent = (
        <>
            <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
            >
                <X className="h-4 w-4 mr-2" />
                {cancelText}
            </Button>
            <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
            >
                {isSubmitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                    <Save className="h-4 w-4 mr-2" />
                )}
                {isSubmitting ? 'Saving...' : submitText}
            </Button>
        </>
    );

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
                {fields.map(renderField)}
            </div>
            
            {/* Footer buttons are handled by the modal */}
            <div className="hidden">
                {footerContent}
            </div>
        </form>
    );
};

// Specific CRUD Modal Components
export const CreateModal = ({ title, fields, onSubmit, onCancel, ...props }) => {
    return (
        <FormModal
            title={`Create ${title}`}
            fields={fields}
            onSubmit={onSubmit}
            onCancel={onCancel}
            submitText="Create"
            {...props}
        />
    );
};

export const EditModal = ({ title, fields, data, onSubmit, onCancel, ...props }) => {
    return (
        <FormModal
            title={`Edit ${title}`}
            fields={fields}
            initialData={data}
            onSubmit={onSubmit}
            onCancel={onCancel}
            submitText="Update"
            {...props}
        />
    );
};

export const ViewModal = ({ title, data, onClose, fields }) => {
    const renderViewField = (field) => {
        const { name, label, type } = field;
        const value = data[name];

        if (!value && value !== 0) return null;

        return (
            <div key={name} className="space-y-1">
                <Label className="text-sm font-medium text-gray-600">
                    {label}
                </Label>
                <div className="text-sm text-gray-900">
                    {type === 'checkbox' ? (value ? 'Yes' : 'No') : value}
                </div>
            </div>
        );
    };

    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {fields.map(renderViewField)}
            </div>
            
            <div className="flex justify-end pt-4 border-t">
                <Button onClick={onClose} variant="outline">
                    Close
                </Button>
            </div>
        </div>
    );
};

export default FormModal;
