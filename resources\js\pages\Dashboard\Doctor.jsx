import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Calendar, Users, Clock, CheckCircle, Stethoscope } from 'lucide-react';

export default function DoctorDashboard({ 
    auth, 
    doctorProfile, 
    stats, 
    todaysAppointments, 
    upcomingAppointments, 
    recentPatients 
}) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={<h2 className="font-semibold text-xl text-gray-800 leading-tight">Doctor Dashboard</h2>}
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    {/* Doctor Profile */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Stethoscope className="mr-2 h-5 w-5" />
                                Dr. {auth.user.name}
                            </CardTitle>
                            <CardDescription>
                                {doctorProfile?.specialization || 'Medical Professional'} • {doctorProfile?.branch?.name || 'Therapy Center'}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                <span>{doctorProfile?.experience_years || 0} years experience</span>
                                <span>•</span>
                                <span>License: {doctorProfile?.license_number || 'N/A'}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Appointments</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.total_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    All time
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today's Schedule</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.todays_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Appointments today
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.upcoming_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Future appointments
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.completed_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Sessions completed
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common doctor tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-4">
                                <Button variant="default" onClick={() => window.location.href = '/appointments/today/list'}>
                                    <Clock className="mr-2 h-4 w-4" />
                                    Today's Schedule
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    All Appointments
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/medical-records'}>
                                    <Stethoscope className="mr-2 h-4 w-4" />
                                    Medical Records
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/medical-records/create'}>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    New Record
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Today's Appointments & Upcoming */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Today's Appointments</CardTitle>
                                <CardDescription>Your schedule for today</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {todaysAppointments?.length > 0 ? (
                                        todaysAppointments.slice(0, 5).map((appointment, index) => (
                                            <div key={index} className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-medium truncate">
                                                        {new Date(appointment.appointment_date).toLocaleTimeString([], {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {appointment.patient?.name}
                                                    </p>
                                                </div>
                                                <div className="flex-shrink-0">
                                                    <Badge variant={
                                                        appointment.status === 'completed' ? 'default' :
                                                        appointment.status === 'confirmed' ? 'secondary' : 'outline'
                                                    }>
                                                        {appointment.status}
                                                    </Badge>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground">No appointments today</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Upcoming Appointments</CardTitle>
                                <CardDescription>Next scheduled sessions</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {upcomingAppointments?.length > 0 ? (
                                        upcomingAppointments.slice(0, 5).map((appointment, index) => (
                                            <div key={index} className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-medium truncate">
                                                        {new Date(appointment.appointment_date).toLocaleDateString()}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {appointment.patient?.name} • {new Date(appointment.appointment_date).toLocaleTimeString([], {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                </div>
                                                <div className="flex-shrink-0">
                                                    <Badge variant="outline">
                                                        {appointment.status}
                                                    </Badge>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground">No upcoming appointments</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Recent Patients */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Patients</CardTitle>
                            <CardDescription>Patients you've recently treated</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                {recentPatients?.length > 0 ? (
                                    recentPatients.slice(0, 6).map((patient, index) => (
                                        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                                            <div className="flex-shrink-0">
                                                <Users className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">
                                                    {patient.name}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    Patient
                                                </p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground col-span-3">No recent patients</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}