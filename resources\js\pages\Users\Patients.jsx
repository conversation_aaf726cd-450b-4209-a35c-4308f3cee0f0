import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Heart, Plus, Eye, Edit, Trash2, Building2, Phone, MapPin, AlertTriangle } from 'lucide-react';

export default function PatientsIndex({ auth, patients, canManageAll }) {
    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this patient?')) {
            router.delete(route('users.destroy', id));
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'Not specified';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const calculateAge = (dateString) => {
        if (!dateString) return 'Unknown';
        const today = new Date();
        const birthDate = new Date(dateString);
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        return age;
    };

    return (
        <AppLayout>
            <Head title="Patients" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Patients</h1>
                            <p className="text-gray-600">Manage patient records</p>
                        </div>
                        
                        {canManageAll && (
                            <Link href={route('users.create', { role: 'patient' })}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Patient
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Back to Users */}
                    <div>
                        <Link href={route('users.index')} className="text-blue-600 hover:text-blue-800 text-sm">
                            ← Back to All Users
                        </Link>
                    </div>

                    {/* Patients List */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Heart className="h-5 w-5" />
                                Patients ({patients?.length || 0})
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {patients && patients.length > 0 ? (
                                <div className="space-y-4">
                                    {patients.map((patient) => (
                                        <div key={patient.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <Heart className="h-4 w-4 text-green-600" />
                                                        <h3 className="font-medium">{patient.name}</h3>
                                                        
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            patient.is_active 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {patient.is_active ? 'Active' : 'Inactive'}
                                                        </span>

                                                        {patient.patientProfile && (
                                                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                {patient.patientProfile.gender?.charAt(0).toUpperCase() + patient.patientProfile.gender?.slice(1)}
                                                            </span>
                                                        )}
                                                    </div>
                                                    
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                                                        <div>
                                                            <span className="font-medium">Email:</span> {patient.email}
                                                        </div>
                                                        {patient.phone && (
                                                            <div>
                                                                <span className="font-medium">Phone:</span> {patient.phone}
                                                            </div>
                                                        )}
                                                    </div>

                                                    {patient.patientProfile && (
                                                        <div className="space-y-2 text-sm">
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Date of Birth:</span>
                                                                    <p className="text-gray-600">
                                                                        {formatDate(patient.patientProfile.date_of_birth)}
                                                                        {patient.patientProfile.date_of_birth && (
                                                                            <span className="ml-2 text-gray-500">
                                                                                ({calculateAge(patient.patientProfile.date_of_birth)} years old)
                                                                            </span>
                                                                        )}
                                                                    </p>
                                                                </div>
                                                                
                                                                {patient.patientProfile.branch && (
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Branch:</span>
                                                                        <p className="text-gray-600 flex items-center gap-1">
                                                                            <Building2 className="h-3 w-3" />
                                                                            {patient.patientProfile.branch.name}
                                                                        </p>
                                                                    </div>
                                                                )}
                                                            </div>

                                                            {patient.patientProfile.address && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Address:</span>
                                                                    <p className="text-gray-600 flex items-start gap-1">
                                                                        <MapPin className="h-3 w-3 mt-0.5" />
                                                                        {patient.patientProfile.address}
                                                                    </p>
                                                                </div>
                                                            )}

                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                {patient.patientProfile.emergency_contact_name && (
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Emergency Contact:</span>
                                                                        <p className="text-gray-600">{patient.patientProfile.emergency_contact_name}</p>
                                                                        {patient.patientProfile.emergency_contact_phone && (
                                                                            <p className="text-gray-600 flex items-center gap-1">
                                                                                <Phone className="h-3 w-3" />
                                                                                {patient.patientProfile.emergency_contact_phone}
                                                                            </p>
                                                                        )}
                                                                    </div>
                                                                )}

                                                                {patient.patientProfile.insurance_provider && (
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Insurance:</span>
                                                                        <p className="text-gray-600">{patient.patientProfile.insurance_provider}</p>
                                                                        {patient.patientProfile.insurance_number && (
                                                                            <p className="text-gray-500 text-xs">#{patient.patientProfile.insurance_number}</p>
                                                                        )}
                                                                    </div>
                                                                )}
                                                            </div>

                                                            {patient.patientProfile.allergies && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700 flex items-center gap-1">
                                                                        <AlertTriangle className="h-3 w-3 text-red-500" />
                                                                        Allergies:
                                                                    </span>
                                                                    <p className="text-red-600">{patient.patientProfile.allergies}</p>
                                                                </div>
                                                            )}

                                                            {patient.patientProfile.current_medications && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Current Medications:</span>
                                                                    <p className="text-gray-600">{patient.patientProfile.current_medications}</p>
                                                                </div>
                                                            )}

                                                            {patient.patientProfile.medical_history && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Medical History:</span>
                                                                    <p className="text-gray-600 line-clamp-2">{patient.patientProfile.medical_history}</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2 ml-4">
                                                    <Link href={route('users.show', patient.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {canManageAll && (
                                                        <>
                                                            <Link href={route('users.edit', patient.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            
                                                            <Button 
                                                                variant="outline" 
                                                                size="sm"
                                                                onClick={() => handleDelete(patient.id)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No patients found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        No patients have been registered yet
                                    </p>
                                    {canManageAll && (
                                        <Link href={route('users.create', { role: 'patient' })}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Add Patient
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}