import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { BookOpen, Folder, LayoutGrid, Building2, Users, Calendar, FileText, UserCheck, Heart, Stethoscope, Clock, User } from 'lucide-react';
import AppLogo from './app-logo';

export function AppSidebar() {
    const { auth } = usePage().props as any;
    const user = auth?.user;
    const userRole = user?.roles?.[0]?.name;

    // Base navigation items for all authenticated users
    const baseNavItems: NavItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
            icon: LayoutGrid,
        },
    ];

    // Role-specific navigation items
    const roleNavItems: NavItem[] = [];

    // Navigation based on specific roles
    switch (userRole) {
        case 'admin':
            roleNavItems.push(
                {
                    title: 'Branches',
                    href: '/branches',
                    icon: Building2,
                },
                {
                    title: 'Users',
                    href: '/users',
                    icon: Users,
                },
                {
                    title: 'Appointments',
                    href: '/appointments',
                    icon: Calendar,
                },
                {
                    title: 'Medical Records',
                    href: '/medical-records',
                    icon: FileText,
                }
            );
            break;

        case 'branch_head':
            roleNavItems.push(
                {
                    title: 'Branches',
                    href: '/branches',
                    icon: Building2,
                },
                {
                    title: 'Users',
                    href: '/users',
                    icon: Users,
                },
                {
                    title: 'Appointments',
                    href: '/appointments',
                    icon: Calendar,
                },
                {
                    title: 'Medical Records',
                    href: '/medical-records',
                    icon: FileText,
                }
            );
            break;

        case 'doctor':
            roleNavItems.push(
                {
                    title: 'Appointments',
                    href: '/appointments',
                    icon: Calendar,
                },
                {
                    title: 'Medical Records',
                    href: '/medical-records',
                    icon: FileText,
                },
                {
                    title: 'Today\'s Schedule',
                    href: '/appointments/today/list',
                    icon: Clock,
                }
            );
            break;

        case 'patient':
            roleNavItems.push(
                {
                    title: 'Appointments',
                    href: '/appointments',
                    icon: Calendar,
                },
                {
                    title: 'Medical Records',
                    href: '/medical-records',
                    icon: FileText,
                },
                {
                    title: 'Book Appointment',
                    href: '/appointments/create',
                    icon: Heart,
                }
            );
            break;

        default:
            // For users without roles or unrecognized roles
            roleNavItems.push({
                title: 'Profile',
                href: '/profile',
                icon: User,
            });
            break;
    }

    const mainNavItems: NavItem[] = [...baseNavItems, ...roleNavItems];

    const footerNavItems: NavItem[] = [
        {
            title: 'Repository',
            href: 'https://github.com/laravel/react-starter-kit',
            icon: Folder,
        },
        {
            title: 'Documentation',
            href: 'https://laravel.com/docs/starter-kits#react',
            icon: BookOpen,
        },
    ];

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
                
                {/* Role indicator */}
                {userRole && (
                    <div className="px-2 py-1 mx-2 text-xs bg-muted rounded-md text-center">
                        <span className="font-medium capitalize">
                            {userRole.replace('_', ' ')} Mode
                        </span>
                    </div>
                )}
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
                
                {/* No role warning */}
                {!userRole && (
                    <div className="px-4 py-2 mx-2 text-xs bg-yellow-50 border border-yellow-200 rounded-md">
                        <p className="text-yellow-800 font-medium">No Role Assigned</p>
                        <p className="text-yellow-600">Contact administrator</p>
                    </div>
                )}
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
