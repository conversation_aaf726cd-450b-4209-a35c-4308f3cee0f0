<?php

use App\Services\UserService;
use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'doctor']);
    Role::create(['name' => 'patient']);
    Role::create(['name' => 'branch_head']);
});

test('user service can create doctor user', function () {
    $userRepository = app(UserRepositoryInterface::class);
    $userService = new UserService($userRepository);

    $userData = [
        'name' => 'Dr. John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '**********',
        'branch_id' => 1,
        'specialization' => 'Cardiology',
        'license_number' => 'MD123456',
        'qualifications' => 'MBBS, MD',
        'experience_years' => 10,
        'consultation_fee' => 150.00,
        'available_days' => ['monday', 'tuesday', 'wednesday'],
        'start_time' => '09:00',
        'end_time' => '17:00',
    ];

    $user = $userService->createUser($userData, 'doctor');

    expect($user)->toBeInstanceOf(User::class);
    expect($user->hasRole('doctor'))->toBeTrue();
    expect($user->doctorProfile)->not->toBeNull();
    expect($user->doctorProfile->specialization)->toBe('Cardiology');
});

test('user service can create patient user', function () {
    $userRepository = app(UserRepositoryInterface::class);
    $userService = new UserService($userRepository);

    $userData = [
        'name' => 'Jane Smith',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'phone' => '**********',
        'branch_id' => 1,
        'date_of_birth' => '1990-01-01',
        'gender' => 'female',
        'address' => '123 Main St',
        'emergency_contact_name' => 'John Smith',
        'emergency_contact_phone' => '**********',
    ];

    $user = $userService->createUser($userData, 'patient');

    expect($user)->toBeInstanceOf(User::class);
    expect($user->hasRole('patient'))->toBeTrue();
    expect($user->patientProfile)->not->toBeNull();
    expect($user->patientProfile->gender)->toBe('female');
});

test('user service validates required fields for doctor', function () {
    $userRepository = app(UserRepositoryInterface::class);
    $userService = new UserService($userRepository);

    $userData = [
        'name' => 'Dr. John Doe',
        'email' => '<EMAIL>',
        'password' => 'password123',
        // Missing required doctor fields
    ];

    expect(fn() => $userService->createUser($userData, 'doctor'))
        ->toThrow(Exception::class);
});
