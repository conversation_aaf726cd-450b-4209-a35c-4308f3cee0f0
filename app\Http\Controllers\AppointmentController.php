<?php

namespace App\Http\Controllers;

use App\Http\Requests\AppointmentRequest;
use App\Http\Requests\AppointmentUpdateRequest;
use App\Services\AppointmentService;
use App\Services\UserService;
use App\Services\BranchService;
use Inertia\Inertia;

class AppointmentController extends Controller
{
    public function __construct(
        private AppointmentService $appointmentService,
        private UserService $userService,
        private BranchService $branchService
    ) {}

    public function index()
    {
        $user = auth()->user();
        
        // Get appointments based on user role
        $appointments = $this->getAppointmentsForRole($user);
        
        return Inertia::render('Appointments/Index', [
            'appointments' => $appointments,
            'userRole' => $user->roles->first()->name,
            'stats' => $this->getAppointmentStats($appointments),
            'canCreate' => $this->canCreateAppointment($user),
            'canManageAll' => $user->hasAnyRole(['admin', 'branch_head'])
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        
        if (!$this->canCreateAppointment($user)) {
            abort(403, 'Unauthorized');
        }

        $data = [];
        
        if ($user->hasRole('patient')) {
            // Patient can only book with doctors from their branch
            $branchId = $user->patientProfile->branch_id;
            $data['doctors'] = $this->userService->getDoctorsByBranch($branchId);
            $data['branchId'] = $branchId;
        } elseif ($user->hasRole('admin')) {
            // Admin can create appointments for any patient/doctor
            $data['doctors'] = $this->userService->getUsersByRole('doctor');
            $data['patients'] = $this->userService->getUsersByRole('patient');
            $data['branches'] = $this->branchService->getActiveBranches();
        } elseif ($user->hasRole('branch_head')) {
            // Branch head can create appointments for their branch
            $branchId = $user->managedBranch->id;
            $data['doctors'] = $this->userService->getDoctorsByBranch($branchId);
            $data['patients'] = $this->userService->getPatientsByBranch($branchId);
            $data['branchId'] = $branchId;
        }

        return Inertia::render('Appointments/Create', $data);
    }

    public function store(AppointmentRequest $request)
    {
        $user = auth()->user();
        
        if (!$this->canCreateAppointment($user)) {
            abort(403, 'Unauthorized');
        }

        try {
            $data = $request->validated();
            
            // Set patient_id based on user role
            if ($user->hasRole('patient')) {
                $data['patient_id'] = $user->id;
                $data['branch_id'] = $user->patientProfile->branch_id;
            }

            $appointment = $this->appointmentService->createAppointment($data);
            
            return redirect()->route('appointments.index')
                ->with('success', 'Appointment created successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || !$this->canViewAppointment($user, $appointment)) {
            abort(403, 'Unauthorized');
        }

        return Inertia::render('Appointments/Show', [
            'appointment' => $appointment->load(['patient.patientProfile', 'doctor.doctorProfile', 'medicalRecords']),
            'canEdit' => $this->canEditAppointment($user, $appointment),
            'canComplete' => $user->hasRole('doctor') && $appointment->doctor_id === $user->id,
            'canCancel' => $this->canCancelAppointment($user, $appointment)
        ]);
    }

    public function edit($id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || !$this->canEditAppointment($user, $appointment)) {
            abort(403, 'Unauthorized');
        }

        return Inertia::render('Appointments/Edit', [
            'appointment' => $appointment->load(['patient', 'doctor', 'branch']),
            'doctors' => $this->getAvailableDoctors($user, $appointment),
            'patients' => $this->getAvailablePatients($user, $appointment)
        ]);
    }

    public function update(AppointmentUpdateRequest $request, $id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || !$this->canEditAppointment($user, $appointment)) {
            abort(403, 'Unauthorized');
        }

        try {
            $this->appointmentService->updateAppointment($id, $request->validated());
            
            return redirect()->route('appointments.index')
                ->with('success', 'Appointment updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || !$this->canEditAppointment($user, $appointment)) {
            abort(403, 'Unauthorized');
        }

        try {
            $this->appointmentService->cancelAppointment($id, 'Deleted by ' . $user->name);
            
            return redirect()->route('appointments.index')
                ->with('success', 'Appointment cancelled successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function complete(AppointmentUpdateRequest $request, $id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$user->hasRole('doctor') || !$appointment || $appointment->doctor_id !== $user->id) {
            abort(403, 'Unauthorized');
        }

        try {
            $this->appointmentService->completeAppointment($id, $request->validated());
            
            return redirect()->route('appointments.index')
                ->with('success', 'Appointment completed successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function cancel($id)
    {
        $user = auth()->user();
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || !$this->canCancelAppointment($user, $appointment)) {
            abort(403, 'Unauthorized');
        }

        try {
            $reason = $user->hasRole('patient') ? 'Cancelled by patient' : 'Cancelled by staff';
            $this->appointmentService->cancelAppointment($id, $reason);
            
            return back()->with('success', 'Appointment cancelled successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function today()
    {
        $user = auth()->user();
        $appointments = $this->appointmentService->getTodaysAppointments();
        
        // Filter based on role
        $appointments = $this->filterAppointmentsByRole($user, $appointments);

        return Inertia::render('Appointments/Today', [
            'appointments' => $appointments,
            'userRole' => $user->roles->first()->name
        ]);
    }

    public function getStats()
    {
        $user = auth()->user();
        $appointments = $this->getAppointmentsForRole($user);

        return response()->json([
            'total' => $appointments->count(),
            'today' => $appointments->filter(fn($a) => $a->appointment_date->isToday())->count(),
            'upcoming' => $appointments->filter(fn($a) => $a->appointment_date->isFuture())->count(),
            'completed' => $appointments->where('status', 'completed')->count(),
            'cancelled' => $appointments->where('status', 'cancelled')->count(),
            'pending' => $appointments->whereIn('status', ['scheduled', 'confirmed'])->count(),
        ]);
    }

    // Private helper methods
    private function getAppointmentsForRole($user)
    {
        if ($user->hasRole('admin')) {
            return $this->appointmentService->getAllAppointments();
        } elseif ($user->hasRole('branch_head')) {
            return $this->appointmentService->getAppointmentsByBranch($user->managedBranch->id);
        } elseif ($user->hasRole('doctor')) {
            return $this->appointmentService->getAppointmentsByDoctor($user->id);
        } elseif ($user->hasRole('patient')) {
            return $this->appointmentService->getAppointmentsByPatient($user->id);
        }
        
        return collect();
    }

    private function filterAppointmentsByRole($user, $appointments)
    {
        if ($user->hasRole('doctor')) {
            return $appointments->where('doctor_id', $user->id);
        } elseif ($user->hasRole('patient')) {
            return $appointments->where('patient_id', $user->id);
        } elseif ($user->hasRole('branch_head')) {
            return $appointments->where('branch_id', $user->managedBranch->id);
        }
        
        return $appointments; // Admin sees all
    }

    private function canCreateAppointment($user)
    {
        return $user->hasAnyRole(['patient', 'admin', 'branch_head']);
    }

    private function canViewAppointment($user, $appointment)
    {
        if ($user->hasRole('admin')) {
            return true;
        } elseif ($user->hasRole('branch_head')) {
            return $appointment->branch_id === $user->managedBranch->id;
        } elseif ($user->hasRole('doctor')) {
            return $appointment->doctor_id === $user->id;
        } elseif ($user->hasRole('patient')) {
            return $appointment->patient_id === $user->id;
        }
        
        return false;
    }

    private function canEditAppointment($user, $appointment)
    {
        if ($user->hasRole('admin')) {
            return true;
        } elseif ($user->hasRole('branch_head')) {
            return $appointment->branch_id === $user->managedBranch->id;
        } elseif ($user->hasRole('doctor')) {
            return $appointment->doctor_id === $user->id && in_array($appointment->status, ['scheduled', 'confirmed']);
        }
        
        return false;
    }

    private function canCancelAppointment($user, $appointment)
    {
        if (!$appointment->canBeCancelled()) {
            return false;
        }
        
        if ($user->hasAnyRole(['admin', 'branch_head'])) {
            return true;
        } elseif ($user->hasRole('patient')) {
            return $appointment->patient_id === $user->id;
        } elseif ($user->hasRole('doctor')) {
            return $appointment->doctor_id === $user->id;
        }
        
        return false;
    }

    private function getAppointmentStats($appointments)
    {
        return [
            'total' => $appointments->count(),
            'today' => $appointments->filter(fn($a) => $a->appointment_date->isToday())->count(),
            'upcoming' => $appointments->filter(fn($a) => $a->appointment_date->isFuture())->count(),
            'completed' => $appointments->where('status', 'completed')->count(),
            'cancelled' => $appointments->where('status', 'cancelled')->count(),
            'pending' => $appointments->whereIn('status', ['scheduled', 'confirmed'])->count(),
        ];
    }

    private function getAvailableDoctors($user, $appointment)
    {
        if ($user->hasRole('admin')) {
            return $this->userService->getUsersByRole('doctor');
        } elseif ($user->hasRole('branch_head')) {
            return $this->userService->getDoctorsByBranch($user->managedBranch->id);
        }
        
        return collect([$appointment->doctor]);
    }

    private function getAvailablePatients($user, $appointment)
    {
        if ($user->hasRole('admin')) {
            return $this->userService->getUsersByRole('patient');
        } elseif ($user->hasRole('branch_head')) {
            return $this->userService->getPatientsByBranch($user->managedBranch->id);
        }
        
        return collect([$appointment->patient]);
    }
}