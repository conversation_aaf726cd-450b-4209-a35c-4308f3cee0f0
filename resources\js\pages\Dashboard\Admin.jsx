import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Users, Building2, Calendar, TrendingUp, Clock, CheckCircle } from 'lucide-react';

export default function AdminDashboard({ 
    auth, 
    stats, 
    recentActivity, 
    upcomingAppointments, 
    branchStats 
}) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={<h2 className="font-semibold text-xl text-gray-800 leading-tight">Admin Dashboard</h2>}
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    {/* Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
                                <Building2 className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_branches}</div>
                                <p className="text-xs text-muted-foreground">
                                    {stats.active_branches} active
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Doctors</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_doctors}</div>
                                <p className="text-xs text-muted-foreground">
                                    Across all branches
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Patients</CardTitle>
                                <Users className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total_patients}</div>
                                <p className="text-xs text-muted-foreground">
                                    Registered patients
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today's Appointments</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.todays_appointments}</div>
                                <p className="text-xs text-muted-foreground">
                                    {stats.completed_today} completed
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common administrative tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-4">
                                <Button variant="default" onClick={() => window.location.href = '/branches/create'}>
                                    <Building2 className="mr-2 h-4 w-4" />
                                    Add New Branch
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/users/create'}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Add User
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    View Appointments
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/branches'}>
                                    <TrendingUp className="mr-2 h-4 w-4" />
                                    Manage Branches
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Branch Statistics */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Branch Overview</CardTitle>
                            <CardDescription>
                                Performance metrics for all therapy branches
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {branchStats.map((branch, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="space-y-1">
                                            <p className="text-sm font-medium leading-none">{branch.name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {branch.doctors} doctors • {branch.patients} patients
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm font-medium">{branch.appointments} appointments</p>
                                            <Badge variant="secondary" className="text-xs">
                                                Active
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Activity & Upcoming Appointments */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Activity</CardTitle>
                                <CardDescription>Latest appointments and registrations</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {recentActivity.appointments.slice(0, 5).map((appointment, index) => (
                                        <div key={index} className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">
                                                    Appointment with {appointment.doctor?.name}
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                    Patient: {appointment.patient?.name}
                                                </p>
                                            </div>
                                            <div className="flex-shrink-0">
                                                <Badge variant={
                                                    appointment.status === 'completed' ? 'default' :
                                                    appointment.status === 'confirmed' ? 'secondary' : 'outline'
                                                }>
                                                    {appointment.status}
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Upcoming Appointments</CardTitle>
                                <CardDescription>Next scheduled appointments</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {upcomingAppointments.slice(0, 5).map((appointment, index) => (
                                        <div key={index} className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                <Clock className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">
                                                    {new Date(appointment.appointment_date).toLocaleDateString()}
                                                </p>
                                                <p className="text-sm text-muted-foreground">
                                                    {appointment.doctor?.name} - {appointment.patient?.name}
                                                </p>
                                            </div>
                                            <div className="flex-shrink-0">
                                                <span className="text-sm text-muted-foreground">
                                                    {new Date(appointment.appointment_date).toLocaleTimeString([], {
                                                        hour: '2-digit',
                                                        minute: '2-digit'
                                                    })}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}