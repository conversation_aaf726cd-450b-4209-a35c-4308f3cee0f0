<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\MedicalRecordController;
use App\Http\Controllers\ProfileController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    if (auth()->check()) {
        return redirect('/dashboard');
    }
    
    return Inertia::render('welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
})->name('home');

// Authenticated routes with clean URLs
Route::middleware(['auth', 'verified'])->group(function () {
    // Universal Dashboard - shows different content based on user role
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Branch Management - accessible by admin and branch heads
    Route::middleware(['role:admin,branch_head'])->group(function () {
        Route::resource('branches', BranchController::class);
        Route::get('/branches/{id}/stats', [BranchController::class, 'stats'])->name('branches.stats');
    });
    
    // User Management - accessible by admin and branch heads
    Route::middleware(['role:admin,branch_head'])->group(function () {
        Route::resource('users', UserController::class);
        Route::get('/doctors', [UserController::class, 'doctors'])->name('users.doctors');
        Route::get('/patients', [UserController::class, 'patients'])->name('users.patients');
        Route::get('/branch-heads', [UserController::class, 'branchHeads'])->name('users.branch-heads');
    });
    
    // Appointment Management - accessible by all authenticated users with different permissions
    Route::get('/appointments', [AppointmentController::class, 'index'])->name('appointments.index');
    Route::get('/appointments/create', [AppointmentController::class, 'create'])->name('appointments.create');
    Route::post('/appointments', [AppointmentController::class, 'store'])->name('appointments.store');
    Route::get('/appointments/{id}', [AppointmentController::class, 'show'])->name('appointments.show');
    Route::get('/appointments/{id}/edit', [AppointmentController::class, 'edit'])->name('appointments.edit');
    Route::put('/appointments/{id}', [AppointmentController::class, 'update'])->name('appointments.update');
    Route::delete('/appointments/{id}', [AppointmentController::class, 'destroy'])->name('appointments.destroy');
    Route::post('/appointments/{id}/complete', [AppointmentController::class, 'complete'])->name('appointments.complete');
    Route::post('/appointments/{id}/cancel', [AppointmentController::class, 'cancel'])->name('appointments.cancel');
    Route::get('/appointments/today/list', [AppointmentController::class, 'today'])->name('appointments.today');
    
    // Medical Records - accessible by doctors and patients (with different permissions)
    Route::middleware(['role:doctor,patient,admin,branch_head'])->group(function () {
        Route::get('/medical-records', [MedicalRecordController::class, 'index'])->name('medical-records.index');
        Route::get('/medical-records/create', [MedicalRecordController::class, 'create'])->name('medical-records.create');
        Route::post('/medical-records', [MedicalRecordController::class, 'store'])->name('medical-records.store');
        Route::get('/medical-records/{id}', [MedicalRecordController::class, 'show'])->name('medical-records.show');
        Route::get('/medical-records/{id}/edit', [MedicalRecordController::class, 'edit'])->name('medical-records.edit');
        Route::put('/medical-records/{id}', [MedicalRecordController::class, 'update'])->name('medical-records.update');
        Route::delete('/medical-records/{id}', [MedicalRecordController::class, 'destroy'])->name('medical-records.destroy');
    });

    // Therapy Packages - accessible by admin and branch_head
    Route::middleware(['role:admin,branch_head'])->group(function () {
        Route::resource('therapy-packages', TherapyPackageController::class);
        Route::patch('therapy-packages/{therapyPackage}/toggle', [TherapyPackageController::class, 'toggle'])
            ->name('therapy-packages.toggle');
    });

    // Patient Packages - accessible by admin, branch_head, and doctor
    Route::middleware(['role:admin,branch_head,doctor'])->group(function () {
        Route::resource('patient-packages', PatientPackageController::class);
        Route::patch('patient-packages/{patientPackage}/transfer-doctor', [PatientPackageController::class, 'transferDoctor'])
            ->name('patient-packages.transfer-doctor');
        Route::patch('patient-packages/{patientPackage}/extend', [PatientPackageController::class, 'extend'])
            ->name('patient-packages.extend');
        Route::patch('patient-packages/{patientPackage}/pause', [PatientPackageController::class, 'pause'])
            ->name('patient-packages.pause');
        Route::patch('patient-packages/{patientPackage}/resume', [PatientPackageController::class, 'resume'])
            ->name('patient-packages.resume');
    });

    // Profile Management - accessible by all authenticated users
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
    // Data API Routes - clean URLs for data fetching
    Route::get('/dashboard-data', [DashboardController::class, 'getData'])->name('dashboard.data');
    Route::get('/dashboard/package-analytics', [DashboardController::class, 'getPackageAnalytics'])->name('dashboard.package-analytics');
    Route::get('/user-stats', [UserController::class, 'getStats'])->name('users.stats');
    Route::get('/appointment-stats', [AppointmentController::class, 'getStats'])->name('appointments.stats');
    Route::get('/branch-stats', [BranchController::class, 'getStats'])->name('branches.stats');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
