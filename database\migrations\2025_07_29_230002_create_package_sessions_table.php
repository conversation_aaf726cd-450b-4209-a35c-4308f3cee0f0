<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('package_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_package_id')->constrained()->onDelete('cascade');
            $table->foreignId('appointment_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('session_number');
            $table->date('session_date');
            $table->time('session_time');
            $table->integer('duration_minutes');
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'no_show', 'rescheduled'])->default('scheduled');
            $table->text('session_notes')->nullable();
            $table->text('homework_assigned')->nullable();
            $table->text('progress_notes')->nullable();
            $table->decimal('session_fee', 8, 2)->nullable();
            $table->foreignId('conducted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            $table->index(['patient_package_id', 'session_number']);
            $table->index(['session_date', 'status']);
            $table->index(['conducted_by', 'session_date']);
            $table->index('status');
            $table->unique(['patient_package_id', 'session_number']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('package_sessions');
    }
};
