<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, HasRoles, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    // Relationships
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function managedBranch()
    {
        return $this->hasOne(Branch::class, 'branch_head_id');
    }

    public function branchHeadAssignments()
    {
        return $this->hasMany(BranchHead::class);
    }

    public function currentBranchHeadAssignment()
    {
        return $this->hasOne(BranchHead::class)->current();
    }

    public function activeBranchHeadAssignments()
    {
        return $this->hasMany(BranchHead::class)->active();
    }

    public function doctorProfile()
    {
        return $this->hasOne(DoctorProfile::class);
    }

    public function patientProfile()
    {
        return $this->hasOne(PatientProfile::class);
    }

    public function doctorAppointments()
    {
        return $this->hasMany(Appointment::class, 'doctor_id');
    }

    public function patientAppointments()
    {
        return $this->hasMany(Appointment::class, 'patient_id');
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class, 'patient_id');
    }

    // Package relationships
    public function patientPackages(): HasMany
    {
        return $this->hasMany(PatientPackage::class, 'patient_id');
    }

    public function activePatientPackages(): HasMany
    {
        return $this->hasMany(PatientPackage::class, 'patient_id')->where('status', 'active');
    }

    public function assignedPatientPackages(): HasMany
    {
        return $this->hasMany(PatientPackage::class, 'assigned_by');
    }

    public function doctorPatientPackages(): HasMany
    {
        return $this->hasMany(PatientPackage::class, 'assigned_doctor_id');
    }

    public function createdTherapyPackages(): HasMany
    {
        return $this->hasMany(TherapyPackage::class, 'created_by');
    }

    public function conductedSessions(): HasMany
    {
        return $this->hasMany(PackageSession::class, 'conducted_by');
    }

    // Helper methods
    public function isAdmin()
    {
        return $this->hasRole('admin');
    }

    public function isBranchHead()
    {
        return $this->hasRole('branch_head');
    }

    public function isDoctor()
    {
        return $this->hasRole('doctor');
    }

    public function isPatient()
    {
        return $this->hasRole('patient');
    }
}
