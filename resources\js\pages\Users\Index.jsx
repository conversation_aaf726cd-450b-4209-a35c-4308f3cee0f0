import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Users, Plus, Eye, Edit, Trash2, User<PERSON>heck, Heart, Stethoscope } from 'lucide-react';
import { useState } from 'react';
import { useToast } from '@/contexts/ToastContext';
import { showDeleteConfirmation } from '@/utils/sweetAlert';
import UserCreateModal from '@/Components/modals/UserCreateModal';
import UserEditModal from '@/Components/modals/UserEditModal';

export default function UsersIndex({ auth, users, canManageAll, currentRole = 'all', filters = {} }) {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const toast = useToast();

    const formatRole = (role) => {
        const roleMap = {
            'doctor': 'Doctor',
            'patient': 'Patient',
            'branch_head': 'Branch Head',
            'admin': 'Admin'
        };
        return roleMap[role] || role;
    };

    const getRoleIcon = (role) => {
        switch (role) {
            case 'doctor': return <Stethoscope className="h-4 w-4" />;
            case 'patient': return <Heart className="h-4 w-4" />;
            case 'branch_head': return <UserCheck className="h-4 w-4" />;
            default: return <Users className="h-4 w-4" />;
        }
    };

    const getRoleColor = (role) => {
        switch (role) {
            case 'doctor': return 'bg-blue-100 text-blue-800';
            case 'patient': return 'bg-green-100 text-green-800';
            case 'branch_head': return 'bg-purple-100 text-purple-800';
            case 'admin': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleDelete = async (user) => {
        const confirmed = await showDeleteConfirmation(user.name);
        if (confirmed) {
            router.delete(route('users.destroy', user.id), {
                onSuccess: () => {
                    toast.success('User Deleted!', `${user.name} has been deleted successfully.`);
                },
                onError: () => {
                    toast.error('Deletion Failed', 'Unable to delete the user. Please try again.');
                }
            });
        }
    };

    const handleEdit = (user) => {
        setSelectedUser(user);
        setShowEditModal(true);
    };

    const handleModalSuccess = () => {
        // Close modals and reset state
        setShowCreateModal(false);
        setShowEditModal(false);
        setSelectedUser(null);
        // Refresh the page to get updated data
        router.reload();
    };

    return (
        <AppLayout>
            <Head title="Users" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Users</h1>
                            <p className="text-gray-600">Manage system users and their roles</p>
                        </div>
                        
                        {canManageAll && (
                            <Button onClick={() => setShowCreateModal(true)}>
                                <Plus className="mr-2 h-4 w-4" />
                                Add User
                            </Button>
                        )}
                    </div>

                    {/* Filter Tabs */}
                    <div className="flex gap-2">
                        <Link 
                            href={route('users.index')}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${
                                currentRole === 'all' 
                                    ? 'bg-blue-100 text-blue-700' 
                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                        >
                            All Users
                        </Link>
                        <Link 
                            href={route('users.doctors')}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${
                                currentRole === 'doctor' 
                                    ? 'bg-blue-100 text-blue-700' 
                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                        >
                            Doctors
                        </Link>
                        <Link 
                            href={route('users.patients')}
                            className={`px-4 py-2 rounded-lg text-sm font-medium ${
                                currentRole === 'patient' 
                                    ? 'bg-blue-100 text-blue-700' 
                                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                            }`}
                        >
                            Patients
                        </Link>
                        {canManageAll && (
                            <Link 
                                href={route('users.branch-heads')}
                                className={`px-4 py-2 rounded-lg text-sm font-medium ${
                                    currentRole === 'branch_head' 
                                        ? 'bg-blue-100 text-blue-700' 
                                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                                }`}
                            >
                                Branch Heads
                            </Link>
                        )}
                    </div>

                    {/* Users List */}
                    <Card>
                        <CardHeader>
                            <CardTitle>
                                {currentRole === 'all' ? 'All Users' : `${formatRole(currentRole)}s`}
                                <span className="ml-2 text-sm font-normal text-gray-500">
                                    ({users?.length || 0} total)
                                </span>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {users && users.length > 0 ? (
                                <div className="space-y-4">
                                    {users.map((user) => (
                                        <div key={user.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-center justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <div className="flex items-center gap-2">
                                                            {getRoleIcon(user.roles?.[0]?.name)}
                                                            <h3 className="font-medium">{user.name}</h3>
                                                        </div>
                                                        
                                                        {user.roles?.[0]?.name && (
                                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.roles[0].name)}`}>
                                                                {formatRole(user.roles[0].name)}
                                                            </span>
                                                        )}
                                                        
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            user.is_active 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {user.is_active ? 'Active' : 'Inactive'}
                                                        </span>
                                                    </div>
                                                    
                                                    <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                                        <span>📧 {user.email}</span>
                                                        {user.phone && <span>📞 {user.phone}</span>}
                                                    </div>

                                                    {user.doctor_profile && (
                                                        <div className="text-sm text-gray-600">
                                                            <span className="font-medium">Specialization:</span> {user.doctor_profile.specialization}
                                                            {user.doctor_profile.branch && (
                                                                <span className="ml-4">
                                                                    <span className="font-medium">Branch:</span> {user.doctor_profile.branch.name}
                                                                </span>
                                                            )}
                                                        </div>
                                                    )}

                                                    {user.patient_profile && (
                                                        <div className="text-sm text-gray-600">
                                                            <span className="font-medium">DOB:</span> {user.patient_profile.date_of_birth}
                                                            {user.patient_profile.branch && (
                                                                <span className="ml-4">
                                                                    <span className="font-medium">Branch:</span> {user.patient_profile.branch.name}
                                                                </span>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2">
                                                    <Link href={route('users.show', user.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {canManageAll && (
                                                        <>
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleEdit(user)}
                                                            >
                                                                <Edit className="h-4 w-4" />
                                                            </Button>

                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => handleDelete(user)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No users found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        {currentRole === 'all' 
                                            ? 'No users have been created yet'
                                            : `No ${formatRole(currentRole).toLowerCase()}s found`
                                        }
                                    </p>
                                    {canManageAll && (
                                        <Button onClick={() => setShowCreateModal(true)}>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Add User
                                        </Button>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Modals */}
            <UserCreateModal
                isOpen={showCreateModal}
                onClose={() => setShowCreateModal(false)}
                onSuccess={handleModalSuccess}
            />

            <UserEditModal
                isOpen={showEditModal}
                onClose={() => {
                    setShowEditModal(false);
                    setSelectedUser(null);
                }}
                userData={selectedUser}
                onSuccess={handleModalSuccess}
            />
        </AppLayout>
    );
}