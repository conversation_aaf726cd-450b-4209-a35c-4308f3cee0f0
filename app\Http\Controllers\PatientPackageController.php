<?php

namespace App\Http\Controllers;

use App\Models\PatientPackage;
use App\Models\TherapyPackage;
use App\Models\User;
use App\Http\Requests\PatientPackageRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class PatientPackageController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:admin|branch_head|doctor');
    }

    public function index(Request $request)
    {
        $user = auth()->user();
        
        $query = PatientPackage::with([
            'patient:id,name,email',
            'therapyPackage:id,title,total_sessions,price',
            'assignedDoctor:id,name',
            'assignedBy:id,name'
        ]);

        // Filter based on user role
        if ($user->hasRole('doctor')) {
            $query->where('assigned_doctor_id', $user->id);
        } elseif ($user->hasRole('branch_head')) {
            // Branch head can see packages for their branch patients
            $branchId = $user->branchHeadProfile?->branch_id ?? $user->doctorProfile?->branch_id;
            if ($branchId) {
                $query->whereHas('patient.patientProfile', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                });
            }
        }

        // Apply filters
        $query->when($request->search, function ($q, $search) {
            $q->whereHas('patient', function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('therapyPackage', function ($query) use ($search) {
                $query->where('title', 'like', "%{$search}%");
            });
        })
        ->when($request->status, function ($q, $status) {
            $q->where('status', $status);
        })
        ->when($request->payment_status, function ($q, $paymentStatus) {
            $q->where('payment_status', $paymentStatus);
        });

        $patientPackages = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('PatientPackages/Index', [
            'patientPackages' => $patientPackages,
            'filters' => $request->only(['search', 'status', 'payment_status']),
            'statusOptions' => [
                'active' => 'Active',
                'completed' => 'Completed',
                'cancelled' => 'Cancelled',
                'expired' => 'Expired',
                'on_hold' => 'On Hold',
            ],
            'paymentStatusOptions' => [
                'pending' => 'Payment Pending',
                'partial' => 'Partially Paid',
                'paid' => 'Fully Paid',
                'refunded' => 'Refunded',
            ],
        ]);
    }

    public function show(PatientPackage $patientPackage)
    {
        $this->authorize('view', $patientPackage);

        $patientPackage->load([
            'patient:id,name,email,phone',
            'patient.patientProfile',
            'therapyPackage',
            'assignedDoctor:id,name,email',
            'assignedBy:id,name',
            'packageSessions' => function ($query) {
                $query->with('conductedBy:id,name')->orderBy('session_number');
            }
        ]);

        return Inertia::render('PatientPackages/Show', [
            'patientPackage' => $patientPackage,
            'canManage' => auth()->user()->hasAnyRole(['admin', 'branch_head']) || 
                          auth()->id() === $patientPackage->assigned_doctor_id,
        ]);
    }

    public function create(Request $request)
    {
        $this->authorize('create', PatientPackage::class);

        $user = auth()->user();
        
        // Get available patients based on user role
        $patientsQuery = User::role('patient')->with('patientProfile');
        
        if ($user->hasRole('doctor')) {
            $branchId = $user->doctorProfile?->branch_id;
            if ($branchId) {
                $patientsQuery->whereHas('patientProfile', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                });
            }
        } elseif ($user->hasRole('branch_head')) {
            $branchId = $user->branchHeadProfile?->branch_id;
            if ($branchId) {
                $patientsQuery->whereHas('patientProfile', function ($q) use ($branchId) {
                    $q->where('branch_id', $branchId);
                });
            }
        }

        $patients = $patientsQuery->get();
        $packages = TherapyPackage::active()->get();
        $doctors = User::role('doctor')->with('doctorProfile')->get();

        return Inertia::render('PatientPackages/Create', [
            'patients' => $patients,
            'packages' => $packages,
            'doctors' => $doctors,
            'selectedPatient' => $request->patient_id ? 
                $patients->firstWhere('id', $request->patient_id) : null,
        ]);
    }

    public function store(PatientPackageRequest $request)
    {
        $this->authorize('create', PatientPackage::class);

        $validated = $request->validated();
        $package = TherapyPackage::findOrFail($validated['therapy_package_id']);

        $startDate = Carbon::parse($validated['start_date']);
        $endDate = $startDate->copy()->addDays($package->validity_days);

        $patientPackage = PatientPackage::create([
            ...$validated,
            'end_date' => $endDate,
            'sessions_remaining' => $package->total_sessions,
            'assigned_by' => auth()->id(),
        ]);

        return redirect()->route('patient-packages.show', $patientPackage)
            ->with('success', 'Package assigned to patient successfully.');
    }

    public function edit(PatientPackage $patientPackage)
    {
        $this->authorize('update', $patientPackage);

        $patientPackage->load(['patient', 'therapyPackage', 'assignedDoctor']);
        $doctors = User::role('doctor')->with('doctorProfile')->get();

        return Inertia::render('PatientPackages/Edit', [
            'patientPackage' => $patientPackage,
            'doctors' => $doctors,
            'statusOptions' => [
                'active' => 'Active',
                'completed' => 'Completed',
                'cancelled' => 'Cancelled',
                'expired' => 'Expired',
                'on_hold' => 'On Hold',
            ],
            'paymentStatusOptions' => [
                'pending' => 'Payment Pending',
                'partial' => 'Partially Paid',
                'paid' => 'Fully Paid',
                'refunded' => 'Refunded',
            ],
        ]);
    }

    public function update(Request $request, PatientPackage $patientPackage)
    {
        $this->authorize('update', $patientPackage);

        $validated = $request->validate([
            'assigned_doctor_id' => ['nullable', 'exists:users,id'],
            'status' => ['required', 'in:active,completed,cancelled,expired,on_hold'],
            'payment_status' => ['required', 'in:pending,partial,paid,refunded'],
            'amount_paid' => ['required', 'numeric', 'min:0'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $patientPackage->update($validated);

        return redirect()->route('patient-packages.show', $patientPackage)
            ->with('success', 'Patient package updated successfully.');
    }
}
