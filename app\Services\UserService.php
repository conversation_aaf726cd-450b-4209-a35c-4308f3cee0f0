<?php

namespace App\Services;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use App\Models\DoctorProfile;
use App\Models\PatientProfile;
use App\Models\BranchHead;
use App\Services\ValidationService;
use App\Exceptions\InvalidRoleAssignmentException;
use App\Exceptions\UserProfileMismatchException;
use App\Exceptions\BranchAccessDeniedException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function __construct(
        private UserRepositoryInterface $userRepository,
        private ValidationService $validationService
    ) {}

    public function getAllUsers()
    {
        return $this->userRepository->all();
    }

    public function getUserById(int $id)
    {
        return $this->userRepository->find($id);
    }

    public function createUser(array $data)
    {
        return DB::transaction(function () use ($data) {
            // Create user
            $userData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ];

            $user = $this->userRepository->create($userData);

            // Assign role and create profile atomically
            if (isset($data['role'])) {
                $this->assignRoleWithProfile($user, $data['role'], $data);
            }

            return $user->load(['roles', 'doctorProfile', 'patientProfile']);
        });
    }

    public function updateUser(int $id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $user = $this->userRepository->find($id);

            if (!$user) {
                throw new InvalidRoleAssignmentException('User not found');
            }

            // Update basic user data
            $userData = array_intersect_key($data, array_flip([
                'name', 'email', 'phone', 'is_active'
            ]));

            if (isset($data['password'])) {
                $userData['password'] = Hash::make($data['password']);
            }

            $user = $this->userRepository->update($id, $userData);

            // Handle role changes
            if (isset($data['role'])) {
                $this->updateUserRole($user, $data['role'], $data);
            }

            return $user->load(['roles', 'doctorProfile', 'patientProfile']);
        });
    }

    public function assignRoleWithProfile(User $user, string $role, array $profileData = [])
    {
        return DB::transaction(function () use ($user, $role, $profileData) {
            // Validate role and profile using ValidationService
            $this->validationService->validateUserProfile($user, $role, $profileData);

            // Validate role
            $validRoles = ['admin', 'branch_head', 'doctor', 'patient'];
            if (!in_array($role, $validRoles)) {
                throw new InvalidRoleAssignmentException("Invalid role: {$role}");
            }

            // Remove existing roles (except admin can have multiple roles)
            if (!$user->hasRole('admin')) {
                $user->syncRoles([]);
            }

            // Assign new role
            $user->assignRole($role);

            // Create appropriate profile
            switch ($role) {
                case 'doctor':
                    $this->createDoctorProfile($user, $profileData);
                    break;
                case 'patient':
                    $this->createPatientProfile($user, $profileData);
                    break;
                case 'branch_head':
                    if (isset($profileData['branch_id'])) {
                        BranchHead::assignBranchHead(
                            $profileData['branch_id'],
                            $user->id,
                            $profileData['notes'] ?? null
                        );
                    }
                    break;
            }

            return $user;
        });
    }

    public function getUsersByRole(string $role)
    {
        return $this->userRepository->getByRole($role);
    }

    public function updateUserRole(User $user, string $newRole, array $profileData = [])
    {
        return DB::transaction(function () use ($user, $newRole, $profileData) {
            $currentRoles = $user->getRoleNames()->toArray();

            // If role hasn't changed, just update profile
            if (in_array($newRole, $currentRoles)) {
                $this->updateUserProfile($user, $newRole, $profileData);
                return $user;
            }

            // Clean up old profiles and roles
            $this->cleanupUserProfiles($user);

            // Assign new role and profile
            $this->assignRoleWithProfile($user, $newRole, $profileData);

            return $user;
        });
    }

    public function deleteUser(int $id)
    {
        return DB::transaction(function () use ($id) {
            $user = $this->userRepository->find($id);

            if (!$user) {
                throw new InvalidRoleAssignmentException('User not found');
            }

            // Clean up profiles first
            $this->cleanupUserProfiles($user);

            // Soft delete or hard delete based on business rules
            return $this->userRepository->delete($id);
        });
    }

    public function getUsersByBranch(int $branchId)
    {
        $user = auth()->user();

        // Validate branch access
        if (!$user->hasRole('admin')) {
            $userBranchId = $this->getUserBranchId($user);
            if ($branchId !== $userBranchId) {
                throw new BranchAccessDeniedException('Cannot access users from different branch');
            }
        }

        return [
            'doctors' => $this->userRepository->getDoctorsByBranch($branchId),
            'patients' => $this->userRepository->getPatientsByBranch($branchId),
        ];
    }

    private function createDoctorProfile(User $user, array $data)
    {
        $this->validateBranchAccess($data['branch_id'] ?? null);

        $profileData = [
            'user_id' => $user->id,
            'branch_id' => $data['branch_id'],
            'specialization' => $data['specialization'] ?? null,
            'license_number' => $data['license_number'] ?? null,
            'qualifications' => $data['qualifications'] ?? null,
            'experience_years' => $data['experience_years'] ?? 0,
            'phone' => $data['doctor_phone'] ?? $user->phone,
            'bio' => $data['bio'] ?? null,
            'consultation_fee' => $data['consultation_fee'] ?? 0,
            'available_days' => $data['available_days'] ?? ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            'start_time' => $data['start_time'] ?? '09:00',
            'end_time' => $data['end_time'] ?? '17:00',
            'is_active' => $data['is_active'] ?? true,
        ];

        return DoctorProfile::create($profileData);
    }

    private function createPatientProfile(User $user, array $data)
    {
        $this->validateBranchAccess($data['branch_id'] ?? null);

        $profileData = [
            'user_id' => $user->id,
            'branch_id' => $data['branch_id'],
            'date_of_birth' => $data['date_of_birth'] ?? null,
            'gender' => $data['gender'] ?? null,
            'phone' => $data['patient_phone'] ?? $user->phone,
            'address' => $data['address'] ?? null,
            'emergency_contact_name' => $data['emergency_contact_name'] ?? null,
            'emergency_contact_phone' => $data['emergency_contact_phone'] ?? null,
            'medical_history' => $data['medical_history'] ?? null,
            'current_medications' => $data['current_medications'] ?? null,
            'allergies' => $data['allergies'] ?? null,
            'insurance_provider' => $data['insurance_provider'] ?? null,
            'insurance_number' => $data['insurance_number'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ];

        return PatientProfile::create($profileData);
    }

    private function updateUserProfile(User $user, string $role, array $data)
    {
        switch ($role) {
            case 'doctor':
                if ($user->doctorProfile) {
                    $updateData = array_intersect_key($data, array_flip([
                        'specialization', 'license_number', 'qualifications', 'experience_years',
                        'phone', 'bio', 'consultation_fee', 'available_days', 'start_time', 'end_time', 'is_active'
                    ]));
                    $user->doctorProfile->update($updateData);
                }
                break;
            case 'patient':
                if ($user->patientProfile) {
                    $updateData = array_intersect_key($data, array_flip([
                        'date_of_birth', 'gender', 'phone', 'address', 'emergency_contact_name',
                        'emergency_contact_phone', 'medical_history', 'current_medications', 'allergies',
                        'insurance_provider', 'insurance_number', 'is_active'
                    ]));
                    $user->patientProfile->update($updateData);
                }
                break;
        }
    }

    private function cleanupUserProfiles(User $user)
    {
        // Remove doctor profile if exists
        if ($user->doctorProfile) {
            $user->doctorProfile->delete();
        }

        // Remove patient profile if exists
        if ($user->patientProfile) {
            $user->patientProfile->delete();
        }

        // Deactivate branch head assignments
        BranchHead::where('user_id', $user->id)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'end_date' => now(),
                'notes' => 'Role changed'
            ]);

        // Remove all roles except admin
        if (!$user->hasRole('admin')) {
            $user->syncRoles([]);
        }
    }

    private function validateBranchAccess(?int $branchId)
    {
        if (!$branchId) {
            throw new InvalidRoleAssignmentException('Branch ID is required for this role');
        }

        $user = auth()->user();
        if (!$user || !$user->hasRole('admin')) {
            // Non-admin users can only assign to their own branch
            $userBranchId = $this->getUserBranchId($user);
            if ($branchId !== $userBranchId) {
                throw new BranchAccessDeniedException('Cannot assign users to different branch');
            }
        }
    }

    private function getUserBranchId($user): ?int
    {
        if ($user->hasRole('branch_head')) {
            return $user->managedBranch?->id;
        }

        if ($user->hasRole('doctor')) {
            return $user->doctorProfile?->branch_id;
        }

        if ($user->hasRole('patient')) {
            return $user->patientProfile?->branch_id;
        }

        return null;
    }
}