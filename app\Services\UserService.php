<?php

namespace App\Services;

use App\Contracts\UserRepositoryInterface;
use App\Models\User;
use App\Models\DoctorProfile;
use App\Models\PatientProfile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserService
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    public function getAllUsers()
    {
        return $this->userRepository->all();
    }

    public function getUserById(int $id)
    {
        return $this->userRepository->find($id);
    }

    public function createUser(array $data, string $role)
    {
        return DB::transaction(function () use ($data, $role) {
            // Create user
            $userData = [
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'phone' => $data['phone'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ];

            $user = $this->userRepository->create($userData);
            $user->assignRole($role);

            // Create role-specific profile
            if ($role === 'doctor') {
                $this->createDoctorProfile($user->id, $data);
            } elseif ($role === 'patient') {
                $this->createPatientProfile($user->id, $data);
            }

            return $user->fresh();
        });
    }

    public function updateUser(int $id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $user = $this->userRepository->find($id);
            
            if (!$user) {
                throw new \Exception('User not found');
            }

            $userData = collect($data)->only(['name', 'email', 'phone', 'is_active'])->toArray();
            
            if (isset($data['password']) && !empty($data['password'])) {
                $userData['password'] = Hash::make($data['password']);
            }

            $updatedUser = $this->userRepository->update($id, $userData);

            // Update role if provided
            if (isset($data['role'])) {
                $updatedUser->syncRoles([$data['role']]);
            }

            // Update role-specific profile
            if ($user->isDoctor() && isset($data['doctor_profile'])) {
                $this->updateDoctorProfile($user->id, $data['doctor_profile']);
            } elseif ($user->isPatient() && isset($data['patient_profile'])) {
                $this->updatePatientProfile($user->id, $data['patient_profile']);
            }

            return $updatedUser->fresh();
        });
    }

    public function deleteUser(int $id)
    {
        return DB::transaction(function () use ($id) {
            $user = $this->userRepository->find($id);
            
            if (!$user) {
                throw new \Exception('User not found');
            }

            // Check if user has active appointments
            if ($user->isDoctor()) {
                $activeAppointments = $user->doctorAppointments()
                    ->whereIn('status', ['scheduled', 'confirmed', 'in_progress'])
                    ->exists();
                    
                if ($activeAppointments) {
                    throw new \Exception('Cannot delete doctor with active appointments');
                }
            } elseif ($user->isPatient()) {
                $activeAppointments = $user->patientAppointments()
                    ->whereIn('status', ['scheduled', 'confirmed', 'in_progress'])
                    ->exists();
                    
                if ($activeAppointments) {
                    throw new \Exception('Cannot delete patient with active appointments');
                }
            }

            return $this->userRepository->delete($id);
        });
    }

    public function getUsersByRole(string $role)
    {
        return $this->userRepository->getByRole($role);
    }

    public function searchUsers(string $search, ?string $role = null)
    {
        return $this->userRepository->searchUsers($search, $role);
    }

    public function getDoctorsByBranch(int $branchId)
    {
        return $this->userRepository->getDoctorsByBranch($branchId);
    }

    public function getPatientsByBranch(int $branchId)
    {
        return $this->userRepository->getPatientsByBranch($branchId);
    }

    private function createDoctorProfile(int $userId, array $data)
    {
        $profileData = [
            'user_id' => $userId,
            'branch_id' => $data['branch_id'],
            'specialization' => $data['specialization'],
            'license_number' => $data['license_number'],
            'qualifications' => $data['qualifications'],
            'experience_years' => $data['experience_years'],
            'phone' => $data['doctor_phone'] ?? $data['phone'],
            'bio' => $data['bio'] ?? null,
            'consultation_fee' => $data['consultation_fee'],
            'available_days' => $data['available_days'],
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time'],
            'is_active' => $data['is_active'] ?? true,
        ];

        return DoctorProfile::create($profileData);
    }

    private function createPatientProfile(int $userId, array $data)
    {
        $profileData = [
            'user_id' => $userId,
            'branch_id' => $data['branch_id'],
            'date_of_birth' => $data['date_of_birth'],
            'gender' => $data['gender'],
            'phone' => $data['patient_phone'] ?? $data['phone'],
            'address' => $data['address'],
            'emergency_contact_name' => $data['emergency_contact_name'],
            'emergency_contact_phone' => $data['emergency_contact_phone'],
            'medical_history' => $data['medical_history'] ?? null,
            'current_medications' => $data['current_medications'] ?? null,
            'allergies' => $data['allergies'] ?? null,
            'insurance_provider' => $data['insurance_provider'] ?? null,
            'insurance_number' => $data['insurance_number'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ];

        return PatientProfile::create($profileData);
    }

    private function updateDoctorProfile(int $userId, array $data)
    {
        $doctorProfile = DoctorProfile::where('user_id', $userId)->first();
        
        if ($doctorProfile) {
            $doctorProfile->update($data);
        }

        return $doctorProfile;
    }

    private function updatePatientProfile(int $userId, array $data)
    {
        $patientProfile = PatientProfile::where('user_id', $userId)->first();
        
        if ($patientProfile) {
            $patientProfile->update($data);
        }

        return $patientProfile;
    }
}