<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add branch_id to therapy_packages
        Schema::table('therapy_packages', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('id')->constrained()->onDelete('cascade');
            $table->index(['branch_id', 'is_active'], 'idx_therapy_packages_branch_active');
        });

        // Add branch_id to patient_packages
        Schema::table('patient_packages', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('id')->constrained()->onDelete('cascade');
            $table->index(['branch_id', 'status'], 'idx_patient_packages_branch_status');
            $table->index(['branch_id', 'patient_id'], 'idx_patient_packages_branch_patient');
        });

        // Add branch_id to package_sessions
        Schema::table('package_sessions', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('id')->constrained()->onDelete('cascade');
            $table->index(['branch_id', 'session_date'], 'idx_package_sessions_branch_date');
            $table->index(['branch_id', 'status'], 'idx_package_sessions_branch_status');
        });

        // Add branch_id to session_schedules
        Schema::table('session_schedules', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('id')->constrained()->onDelete('cascade');
            $table->index(['branch_id', 'scheduled_date'], 'idx_session_schedules_branch_date');
            $table->index(['branch_id', 'status'], 'idx_session_schedules_branch_status');
        });

        // Add branch_id to package_installments
        Schema::table('package_installments', function (Blueprint $table) {
            $table->foreignId('branch_id')->after('id')->constrained()->onDelete('cascade');
            $table->index(['branch_id', 'status'], 'idx_package_installments_branch_status');
            $table->index(['branch_id', 'due_date'], 'idx_package_installments_branch_due');
        });

        // Add constraints to ensure data consistency
        Schema::table('patient_packages', function (Blueprint $table) {
            // Ensure patient and package belong to same branch
            $table->index(['patient_id', 'therapy_package_id'], 'idx_patient_packages_consistency');
        });

        Schema::table('package_sessions', function (Blueprint $table) {
            // Ensure session and appointment belong to same branch
            $table->index(['appointment_id', 'patient_package_id'], 'idx_package_sessions_consistency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove indexes first
        Schema::table('therapy_packages', function (Blueprint $table) {
            $table->dropIndex('idx_therapy_packages_branch_active');
        });

        Schema::table('patient_packages', function (Blueprint $table) {
            $table->dropIndex('idx_patient_packages_branch_status');
            $table->dropIndex('idx_patient_packages_branch_patient');
            $table->dropIndex('idx_patient_packages_consistency');
        });

        Schema::table('package_sessions', function (Blueprint $table) {
            $table->dropIndex('idx_package_sessions_branch_date');
            $table->dropIndex('idx_package_sessions_branch_status');
            $table->dropIndex('idx_package_sessions_consistency');
        });

        Schema::table('session_schedules', function (Blueprint $table) {
            $table->dropIndex('idx_session_schedules_branch_date');
            $table->dropIndex('idx_session_schedules_branch_status');
        });

        Schema::table('package_installments', function (Blueprint $table) {
            $table->dropIndex('idx_package_installments_branch_status');
            $table->dropIndex('idx_package_installments_branch_due');
        });

        // Remove foreign key columns
        Schema::table('therapy_packages', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn('branch_id');
        });

        Schema::table('patient_packages', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn('branch_id');
        });

        Schema::table('package_sessions', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn('branch_id');
        });

        Schema::table('session_schedules', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn('branch_id');
        });

        Schema::table('package_installments', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropColumn('branch_id');
        });
    }
};
