import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Calendar, Users, Clock, CheckCircle, Heart, User } from 'lucide-react';

export default function PatientDashboard({ 
    auth, 
    patientProfile, 
    stats, 
    upcomingAppointments, 
    pastAppointments, 
    recentDoctors 
}) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={<h2 className="font-semibold text-xl text-gray-800 leading-tight">Patient Dashboard</h2>}
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    {/* Patient Profile */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <User className="mr-2 h-5 w-5" />
                                {auth.user.name}
                            </CardTitle>
                            <CardDescription>
                                Patient at {patientProfile?.branch?.name || 'Therapy Center'}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                <span>DOB: {patientProfile?.date_of_birth ? new Date(patientProfile.date_of_birth).toLocaleDateString() : 'N/A'}</span>
                                <span>•</span>
                                <span>Gender: {patientProfile?.gender || 'N/A'}</span>
                                {patientProfile?.insurance_provider && (
                                    <>
                                        <span>•</span>
                                        <span>Insurance: {patientProfile.insurance_provider}</span>
                                    </>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Appointments</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.total_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    All time
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.upcoming_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Scheduled sessions
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Completed</CardTitle>
                                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.completed_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Sessions attended
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats?.cancelled_appointments || 0}</div>
                                <p className="text-xs text-muted-foreground">
                                    Cancelled sessions
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Quick Actions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Manage your appointments and health records</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-wrap gap-4">
                                <Button variant="default" onClick={() => window.location.href = '/appointments/create'}>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Book Appointment
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/appointments'}>
                                    <Clock className="mr-2 h-4 w-4" />
                                    My Appointments
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/medical-records'}>
                                    <Heart className="mr-2 h-4 w-4" />
                                    Medical Records
                                </Button>
                                <Button variant="outline" onClick={() => window.location.href = '/profile'}>
                                    <User className="mr-2 h-4 w-4" />
                                    Update Profile
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Upcoming & Past Appointments */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Upcoming Appointments</CardTitle>
                                <CardDescription>Your scheduled therapy sessions</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {upcomingAppointments?.length > 0 ? (
                                        upcomingAppointments.slice(0, 5).map((appointment, index) => (
                                            <div key={index} className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-medium truncate">
                                                        {new Date(appointment.appointment_date).toLocaleDateString()}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Dr. {appointment.doctor?.name} • {new Date(appointment.appointment_date).toLocaleTimeString([], {
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                </div>
                                                <div className="flex-shrink-0">
                                                    <Badge variant={
                                                        appointment.status === 'confirmed' ? 'default' : 'secondary'
                                                    }>
                                                        {appointment.status}
                                                    </Badge>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-center py-4">
                                            <Calendar className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
                                            <p className="text-sm text-muted-foreground">No upcoming appointments</p>
                                            <Button 
                                                variant="outline" 
                                                size="sm" 
                                                className="mt-2"
                                                onClick={() => window.location.href = '/appointments/create'}
                                            >
                                                Book an Appointment
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Recent Sessions</CardTitle>
                                <CardDescription>Your past therapy appointments</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {pastAppointments?.length > 0 ? (
                                        pastAppointments.slice(0, 5).map((appointment, index) => (
                                            <div key={index} className="flex items-center space-x-4">
                                                <div className="flex-shrink-0">
                                                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                                                </div>
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-medium truncate">
                                                        {new Date(appointment.appointment_date).toLocaleDateString()}
                                                    </p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Dr. {appointment.doctor?.name}
                                                    </p>
                                                </div>
                                                <div className="flex-shrink-0">
                                                    <Badge variant={
                                                        appointment.status === 'completed' ? 'default' : 'outline'
                                                    }>
                                                        {appointment.status}
                                                    </Badge>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        <p className="text-sm text-muted-foreground text-center py-4">No past appointments</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Healthcare Team */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Your Healthcare Team</CardTitle>
                            <CardDescription>Doctors you've recently consulted with</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                {recentDoctors?.length > 0 ? (
                                    recentDoctors.slice(0, 6).map((doctor, index) => (
                                        <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                                            <div className="flex-shrink-0">
                                                <Heart className="h-4 w-4 text-muted-foreground" />
                                            </div>
                                            <div className="min-w-0 flex-1">
                                                <p className="text-sm font-medium truncate">
                                                    Dr. {doctor.name}
                                                </p>
                                                <p className="text-xs text-muted-foreground">
                                                    {doctor.doctorProfile?.specialization || 'Therapist'}
                                                </p>
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="text-sm text-muted-foreground col-span-3 text-center py-4">
                                        No recent consultations
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}