import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/Components/ui/card';
import { AlertTriangle } from 'lucide-react';

export default function NoRoleWarning() {
    return (
        <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
                <CardTitle className="flex items-center gap-2 text-yellow-800">
                    <AlertTriangle className="h-5 w-5" />
                    No Role Assigned
                </CardTitle>
            </CardHeader>
            <CardContent>
                <p className="text-yellow-700">
                    Your account doesn't have a role assigned yet. Please contact your system administrator 
                    to get proper access permissions.
                </p>
                <p className="text-sm text-yellow-600 mt-2">
                    Until a role is assigned, you won't be able to access the main features of the system.
                </p>
            </CardContent>
        </Card>
    );
}