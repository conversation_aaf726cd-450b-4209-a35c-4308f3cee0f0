<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AppointmentUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasAnyRole(['doctor', 'admin', 'branch_head']);
    }

    public function rules(): array
    {
        return [
            'status' => ['sometimes', 'in:scheduled,confirmed,in_progress,completed,cancelled,no_show'],
            'notes' => ['nullable', 'string'],
            'symptoms' => ['nullable', 'string'],
            'diagnosis' => ['nullable', 'string'],
            'treatment_plan' => ['nullable', 'string'],
            'prescription' => ['nullable', 'string'],
            'payment_status' => ['sometimes', 'in:pending,paid,partial,refunded'],
            
            // For completion
            'findings' => ['nullable', 'string'],
            'recommendations' => ['nullable', 'string'],
            'create_record' => ['boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'status.in' => 'Invalid appointment status',
            'payment_status.in' => 'Invalid payment status',
        ];
    }
}