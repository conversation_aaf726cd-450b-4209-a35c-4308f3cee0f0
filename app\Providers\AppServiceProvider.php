<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Contracts\BranchRepositoryInterface;
use App\Contracts\UserRepositoryInterface;
use App\Contracts\AppointmentRepositoryInterface;
use App\Repositories\BranchRepository;
use App\Repositories\UserRepository;
use App\Repositories\AppointmentRepository;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind repository interfaces to their implementations
        $this->app->bind(BranchRepositoryInterface::class, BranchRepository::class);
        $this->app->bind(UserRepositoryInterface::class, UserRepository::class);
        $this->app->bind(AppointmentRepositoryInterface::class, AppointmentRepository::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
