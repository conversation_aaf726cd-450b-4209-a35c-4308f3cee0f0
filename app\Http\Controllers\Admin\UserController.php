<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserRequest;
use App\Services\UserService;
use App\Services\BranchService;
use Inertia\Inertia;

class UserController extends Controller
{
    public function __construct(
        private UserService $userService,
        private BranchService $branchService
    ) {
        // Middleware is handled in routes
    }

    public function index()
    {
        $users = $this->userService->getAllUsers();
        
        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'stats' => [
                'total_users' => $users->count(),
                'active_users' => $users->where('is_active', true)->count(),
                'doctors' => $users->filter(fn($u) => $u->hasRole('doctor'))->count(),
                'patients' => $users->filter(fn($u) => $u->hasRole('patient'))->count(),
                'branch_heads' => $users->filter(fn($u) => $u->hasRole('branch_head'))->count(),
            ]
        ]);
    }

    public function create()
    {
        $branches = $this->branchService->getActiveBranches();
        
        return Inertia::render('Admin/Users/<USER>', [
            'branches' => $branches,
            'roles' => ['doctor', 'patient', 'branch_head']
        ]);
    }

    public function store(UserRequest $request)
    {
        try {
            $user = $this->userService->createUser(
                $request->validated(), 
                $request->role
            );
            
            return redirect()->route('admin.users.index')
                ->with('success', 'User created successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $user = $this->userService->getUserById($id);
        
        if (!$user) {
            return redirect()->route('admin.users.index')
                ->withErrors(['error' => 'User not found']);
        }

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user->load(['roles', 'doctorProfile.branch', 'patientProfile.branch'])
        ]);
    }

    public function edit($id)
    {
        $user = $this->userService->getUserById($id);
        
        if (!$user) {
            return redirect()->route('admin.users.index')
                ->withErrors(['error' => 'User not found']);
        }

        $branches = $this->branchService->getActiveBranches();

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user->load(['roles', 'doctorProfile', 'patientProfile']),
            'branches' => $branches
        ]);
    }

    public function update(UserRequest $request, $id)
    {
        try {
            $user = $this->userService->updateUser($id, $request->validated());
            
            return redirect()->route('admin.users.index')
                ->with('success', 'User updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        try {
            $this->userService->deleteUser($id);
            
            return redirect()->route('admin.users.index')
                ->with('success', 'User deleted successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function doctors()
    {
        $doctors = $this->userService->getUsersByRole('doctor');
        
        return Inertia::render('Admin/Users/<USER>', [
            'doctors' => $doctors
        ]);
    }

    public function patients()
    {
        $patients = $this->userService->getUsersByRole('patient');
        
        return Inertia::render('Admin/Users/<USER>', [
            'patients' => $patients
        ]);
    }
}