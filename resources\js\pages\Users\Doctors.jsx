import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Stethoscope, Plus, Eye, Edit, Trash2, Building2, Clock } from 'lucide-react';

export default function DoctorsIndex({ auth, doctors, canManageAll }) {
    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this doctor?')) {
            router.delete(route('users.destroy', id));
        }
    };

    const formatAvailableDays = (days) => {
        if (!days || !Array.isArray(days)) return 'Not specified';
        return days.map(day => day.charAt(0).toUpperCase() + day.slice(1)).join(', ');
    };

    return (
        <AppLayout>
            <Head title="Doctors" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Doctors</h1>
                            <p className="text-gray-600">Manage healthcare providers</p>
                        </div>
                        
                        {canManageAll && (
                            <Link href={route('users.create', { role: 'doctor' })}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Doctor
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Back to Users */}
                    <div>
                        <Link href={route('users.index')} className="text-blue-600 hover:text-blue-800 text-sm">
                            ← Back to All Users
                        </Link>
                    </div>

                    {/* Doctors List */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Stethoscope className="h-5 w-5" />
                                Doctors ({doctors?.length || 0})
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {doctors && doctors.length > 0 ? (
                                <div className="space-y-4">
                                    {doctors.map((doctor) => (
                                        <div key={doctor.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <Stethoscope className="h-4 w-4 text-blue-600" />
                                                        <h3 className="font-medium">Dr. {doctor.name}</h3>
                                                        
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            doctor.is_active 
                                                                ? 'bg-green-100 text-green-800' 
                                                                : 'bg-red-100 text-red-800'
                                                        }`}>
                                                            {doctor.is_active ? 'Active' : 'Inactive'}
                                                        </span>
                                                    </div>
                                                    
                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                                                        <div>
                                                            <span className="font-medium">Email:</span> {doctor.email}
                                                        </div>
                                                        {doctor.phone && (
                                                            <div>
                                                                <span className="font-medium">Phone:</span> {doctor.phone}
                                                            </div>
                                                        )}
                                                    </div>

                                                    {doctor.doctorProfile && (
                                                        <div className="space-y-2 text-sm">
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Specialization:</span>
                                                                    <p className="text-gray-600">{doctor.doctorProfile.specialization}</p>
                                                                </div>
                                                                
                                                                {doctor.doctorProfile.branch && (
                                                                    <div>
                                                                        <span className="font-medium text-gray-700">Branch:</span>
                                                                        <p className="text-gray-600 flex items-center gap-1">
                                                                            <Building2 className="h-3 w-3" />
                                                                            {doctor.doctorProfile.branch.name}
                                                                        </p>
                                                                    </div>
                                                                )}
                                                            </div>

                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Experience:</span>
                                                                    <p className="text-gray-600">{doctor.doctorProfile.experience_years} years</p>
                                                                </div>
                                                                
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Consultation Fee:</span>
                                                                    <p className="text-gray-600">${doctor.doctorProfile.consultation_fee}</p>
                                                                </div>
                                                            </div>

                                                            <div>
                                                                <span className="font-medium text-gray-700">Available Days:</span>
                                                                <p className="text-gray-600">{formatAvailableDays(doctor.doctorProfile.available_days)}</p>
                                                            </div>

                                                            <div>
                                                                <span className="font-medium text-gray-700">Working Hours:</span>
                                                                <p className="text-gray-600 flex items-center gap-1">
                                                                    <Clock className="h-3 w-3" />
                                                                    {doctor.doctorProfile.start_time} - {doctor.doctorProfile.end_time}
                                                                </p>
                                                            </div>

                                                            {doctor.doctorProfile.qualifications && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Qualifications:</span>
                                                                    <p className="text-gray-600">{doctor.doctorProfile.qualifications}</p>
                                                                </div>
                                                            )}

                                                            {doctor.doctorProfile.bio && (
                                                                <div>
                                                                    <span className="font-medium text-gray-700">Bio:</span>
                                                                    <p className="text-gray-600">{doctor.doctorProfile.bio}</p>
                                                                </div>
                                                            )}
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2 ml-4">
                                                    <Link href={route('users.show', doctor.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {canManageAll && (
                                                        <>
                                                            <Link href={route('users.edit', doctor.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            
                                                            <Button 
                                                                variant="outline" 
                                                                size="sm"
                                                                onClick={() => handleDelete(doctor.id)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <Stethoscope className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No doctors found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        No doctors have been registered yet
                                    </p>
                                    {canManageAll && (
                                        <Link href={route('users.create', { role: 'doctor' })}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Add Doctor
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}