<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\BranchRequest;
use App\Services\BranchService;
use App\Services\UserService;
use Inertia\Inertia;

class BranchController extends Controller
{
    public function __construct(
        private BranchService $branchService,
        private UserService $userService
    ) {
        // Middleware is handled in routes
    }

    public function index()
    {
        $branches = $this->branchService->getAllBranches();
        
        return Inertia::render('Admin/Branches/Index', [
            'branches' => $branches,
            'stats' => [
                'total_branches' => $branches->count(),
                'active_branches' => $branches->where('is_active', true)->count(),
                'total_doctors' => $branches->sum('doctor_profiles_count'),
                'total_patients' => $branches->sum('patient_profiles_count'),
            ]
        ]);
    }

    public function create()
    {
        $availableBranchHeads = $this->userService->getUsersByRole('branch_head')
            ->whereDoesntHave('managedBranch');

        return Inertia::render('Admin/Branches/Create', [
            'availableBranchHeads' => $availableBranchHeads
        ]);
    }

    public function store(BranchRequest $request)
    {
        try {
            $branch = $this->branchService->createBranch($request->validated());
            
            return redirect()->route('admin.branches.index')
                ->with('success', 'Branch created successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $branch = $this->branchService->getBranchById($id);
        
        if (!$branch) {
            return redirect()->route('admin.branches.index')
                ->withErrors(['error' => 'Branch not found']);
        }

        $doctors = $this->userService->getDoctorsByBranch($id);
        $patients = $this->userService->getPatientsByBranch($id);

        return Inertia::render('Admin/Branches/Show', [
            'branch' => $branch,
            'doctors' => $doctors,
            'patients' => $patients,
            'stats' => [
                'total_doctors' => $doctors->count(),
                'total_patients' => $patients->count(),
                'active_doctors' => $doctors->where('doctorProfile.is_active', true)->count(),
                'active_patients' => $patients->where('patientProfile.is_active', true)->count(),
            ]
        ]);
    }

    public function edit($id)
    {
        $branch = $this->branchService->getBranchById($id);
        
        if (!$branch) {
            return redirect()->route('admin.branches.index')
                ->withErrors(['error' => 'Branch not found']);
        }

        $availableBranchHeads = $this->userService->getUsersByRole('branch_head')
            ->whereDoesntHave('managedBranch')
            ->push($branch->branchHead);

        return Inertia::render('Admin/Branches/Edit', [
            'branch' => $branch,
            'availableBranchHeads' => $availableBranchHeads
        ]);
    }

    public function update(BranchRequest $request, $id)
    {
        try {
            $branch = $this->branchService->updateBranch($id, $request->validated());
            
            return redirect()->route('admin.branches.index')
                ->with('success', 'Branch updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        try {
            $this->branchService->deleteBranch($id);
            
            return redirect()->route('admin.branches.index')
                ->with('success', 'Branch deleted successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
}