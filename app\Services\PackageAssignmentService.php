<?php

namespace App\Services;

use App\Models\PatientPackage;
use App\Models\PackageSession;
use App\Models\PackageInstallment;
use App\Models\TherapyPackage;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PackageAssignmentService
{
    public function assignPackageToPatient(array $data): PatientPackage
    {
        return DB::transaction(function () use ($data) {
            $package = TherapyPackage::findOrFail($data['therapy_package_id']);
            $startDate = Carbon::parse($data['start_date']);
            $endDate = $startDate->copy()->addDays($package->validity_days);

            // Calculate financial details
            $originalPrice = $package->price;
            $discountGiven = $data['discount_given'] ?? 0;
            $totalAfterDiscount = $originalPrice - $discountGiven;

            // Create patient package assignment
            $patientPackage = PatientPackage::create([
                'patient_id' => $data['patient_id'],
                'therapy_package_id' => $data['therapy_package_id'],
                'assigned_by' => auth()->id(),
                'assigned_doctor_id' => $data['assigned_doctor_id'] ?? null,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'sessions_completed' => 0,
                'sessions_remaining' => $package->total_sessions,
                'status' => 'active',
                'original_price' => $originalPrice,
                'discount_given' => $discountGiven,
                'total_amount_after_discount' => $totalAfterDiscount,
                'number_of_installments' => $data['number_of_installments'] ?? 1,
                'payment_type' => $data['payment_type'] ?? 'cash',
                'amount_paid' => $data['amount_paid'] ?? 0,
                'payment_status' => $data['payment_status'] ?? 'pending',
                'payment_notes' => $data['payment_notes'] ?? null,
                'notes' => $data['notes'] ?? null,
            ]);

            // Create installments if payment type is installments
            if ($patientPackage->payment_type === 'installments' && $patientPackage->number_of_installments > 1) {
                if (isset($data['installments']) && is_array($data['installments'])) {
                    $this->createCustomInstallmentSchedule($patientPackage, $data['installments']);
                } else {
                    $this->createInstallmentSchedule($patientPackage);
                }
            }

            // Create initial session schedule if requested
            if ($data['create_schedule'] ?? false) {
                $this->createInitialSessionSchedule($patientPackage, $data);
            }

            return $patientPackage;
        });
    }

    public function createInitialSessionSchedule(PatientPackage $patientPackage, array $scheduleData = []): void
    {
        $package = $patientPackage->therapyPackage;
        $startDate = $patientPackage->start_date;
        
        // Default scheduling parameters
        $sessionInterval = $scheduleData['session_interval_days'] ?? 7; // Weekly by default
        $sessionTime = $scheduleData['session_time'] ?? '10:00';
        $sessionDays = $scheduleData['session_days'] ?? ['monday']; // Default to Mondays

        $currentDate = $startDate->copy();
        $sessionsCreated = 0;

        while ($sessionsCreated < $package->total_sessions && $currentDate <= $patientPackage->end_date) {
            // Check if current date is a valid session day
            $dayOfWeek = strtolower($currentDate->format('l'));
            
            if (in_array($dayOfWeek, $sessionDays)) {
                PackageSession::create([
                    'patient_package_id' => $patientPackage->id,
                    'session_number' => $sessionsCreated + 1,
                    'session_date' => $currentDate->toDateString(),
                    'session_time' => $sessionTime,
                    'duration_minutes' => $package->session_duration_minutes,
                    'status' => 'scheduled',
                    'session_fee' => $package->price_per_session,
                ]);

                $sessionsCreated++;
            }

            $currentDate->addDay();
        }
    }

    public function createInstallmentSchedule(PatientPackage $patientPackage): void
    {
        $installmentAmount = $patientPackage->total_amount_after_discount / $patientPackage->number_of_installments;
        $currentDate = $patientPackage->start_date->copy();

        for ($i = 1; $i <= $patientPackage->number_of_installments; $i++) {
            // First installment is due on start date, subsequent ones monthly
            $dueDate = $i === 1 ? $currentDate : $currentDate->copy()->addMonths($i - 1);

            PackageInstallment::create([
                'patient_package_id' => $patientPackage->id,
                'installment_number' => $i,
                'installment_amount' => $installmentAmount,
                'due_date' => $dueDate,
                'status' => 'pending',
            ]);
        }
    }

    public function createCustomInstallmentSchedule(PatientPackage $patientPackage, array $installmentsData): void
    {
        foreach ($installmentsData as $index => $installmentData) {
            PackageInstallment::create([
                'patient_package_id' => $patientPackage->id,
                'installment_number' => $index + 1,
                'installment_date' => $installmentData['date'],
                'installment_amount' => $installmentData['amount'],
                'due_date' => $installmentData['due_date'],
                'status' => 'pending',
            ]);
        }
    }

    public function transferPackageToDoctor(PatientPackage $patientPackage, int $newDoctorId, string $reason = null): bool
    {
        return DB::transaction(function () use ($patientPackage, $newDoctorId, $reason) {
            $oldDoctorId = $patientPackage->assigned_doctor_id;
            
            // Update package assignment
            $patientPackage->update([
                'assigned_doctor_id' => $newDoctorId,
                'notes' => $patientPackage->notes . "\n\nTransferred from doctor ID {$oldDoctorId} to {$newDoctorId}. Reason: " . ($reason ?? 'Not specified') . " - " . now()->format('Y-m-d H:i:s'),
            ]);

            // Update future sessions
            $patientPackage->packageSessions()
                ->where('status', 'scheduled')
                ->where('session_date', '>', now()->toDateString())
                ->update(['conducted_by' => $newDoctorId]);

            return true;
        });
    }

    public function extendPackageValidity(PatientPackage $patientPackage, int $additionalDays, string $reason = null): bool
    {
        $newEndDate = $patientPackage->end_date->addDays($additionalDays);
        
        return $patientPackage->update([
            'end_date' => $newEndDate,
            'notes' => $patientPackage->notes . "\n\nPackage validity extended by {$additionalDays} days. New end date: {$newEndDate->format('Y-m-d')}. Reason: " . ($reason ?? 'Not specified') . " - " . now()->format('Y-m-d H:i:s'),
        ]);
    }

    public function addSessionsToPackage(PatientPackage $patientPackage, int $additionalSessions, float $additionalCost = 0, string $reason = null): bool
    {
        return DB::transaction(function () use ($patientPackage, $additionalSessions, $additionalCost, $reason) {
            $patientPackage->update([
                'sessions_remaining' => $patientPackage->sessions_remaining + $additionalSessions,
                'notes' => $patientPackage->notes . "\n\nAdded {$additionalSessions} sessions. Additional cost: $" . number_format($additionalCost, 2) . ". Reason: " . ($reason ?? 'Not specified') . " - " . now()->format('Y-m-d H:i:s'),
            ]);

            // Update the therapy package total sessions (this affects the original package)
            $originalPackage = $patientPackage->therapyPackage;
            $newTotalSessions = $originalPackage->total_sessions + $additionalSessions;

            // Create additional scheduled sessions if needed
            $lastSessionNumber = $patientPackage->packageSessions()->max('session_number') ?? 0;
            $package = $patientPackage->therapyPackage;

            for ($i = 1; $i <= $additionalSessions; $i++) {
                PackageSession::create([
                    'patient_package_id' => $patientPackage->id,
                    'session_number' => $lastSessionNumber + $i,
                    'session_date' => now()->addWeeks($i)->toDateString(),
                    'session_time' => '10:00',
                    'duration_minutes' => $package->session_duration_minutes,
                    'status' => 'scheduled',
                    'session_fee' => $additionalCost > 0 ? $additionalCost / $additionalSessions : $package->price_per_session,
                ]);
            }

            return true;
        });
    }

    public function pausePackage(PatientPackage $patientPackage, string $reason = null): bool
    {
        return DB::transaction(function () use ($patientPackage, $reason) {
            $patientPackage->update([
                'status' => 'on_hold',
                'notes' => $patientPackage->notes . "\n\nPackage paused. Reason: " . ($reason ?? 'Not specified') . " - " . now()->format('Y-m-d H:i:s'),
            ]);

            // Cancel future scheduled sessions
            $patientPackage->packageSessions()
                ->where('status', 'scheduled')
                ->where('session_date', '>', now()->toDateString())
                ->update(['status' => 'cancelled']);

            return true;
        });
    }

    public function resumePackage(PatientPackage $patientPackage, string $reason = null): bool
    {
        return DB::transaction(function () use ($patientPackage, $reason) {
            $patientPackage->update([
                'status' => 'active',
                'notes' => $patientPackage->notes . "\n\nPackage resumed. Reason: " . ($reason ?? 'Not specified') . " - " . now()->format('Y-m-d H:i:s'),
            ]);

            // Reschedule cancelled sessions
            $cancelledSessions = $patientPackage->packageSessions()
                ->where('status', 'cancelled')
                ->orderBy('session_number')
                ->get();

            $currentDate = now()->addDay();
            foreach ($cancelledSessions as $session) {
                $session->update([
                    'status' => 'scheduled',
                    'session_date' => $currentDate->toDateString(),
                ]);
                $currentDate->addWeek();
            }

            return true;
        });
    }

    public function getPackageStatistics(PatientPackage $patientPackage): array
    {
        $totalSessions = $patientPackage->therapyPackage->total_sessions;
        $completedSessions = $patientPackage->sessions_completed;
        $remainingSessions = $patientPackage->sessions_remaining;
        
        return [
            'total_sessions' => $totalSessions,
            'completed_sessions' => $completedSessions,
            'remaining_sessions' => $remainingSessions,
            'progress_percentage' => $totalSessions > 0 ? ($completedSessions / $totalSessions) * 100 : 0,
            'days_remaining' => $patientPackage->end_date->diffInDays(now(), false),
            'is_expired' => $patientPackage->isExpired(),
            'is_completed' => $patientPackage->isCompleted(),
            'can_schedule_sessions' => $patientPackage->canScheduleSession(),
            'next_session' => $patientPackage->upcomingSessions()->first(),
            'last_session' => $patientPackage->completedSessions()->latest('session_date')->first(),
        ];
    }
}
