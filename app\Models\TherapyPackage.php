<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TherapyPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'details',
        'total_sessions',
        'price',
        'session_duration_minutes',
        'package_type',
        'objectives',
        'target_audience',
        'included_services',
        'validity_days',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'included_services' => 'array',
        'price' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function patientPackages(): HasMany
    {
        return $this->hasMany(PatientPackage::class);
    }

    public function activePatientPackages(): Has<PERSON>any
    {
        return $this->hasMany(PatientPackage::class)->where('status', 'active');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('package_type', $type);
    }

    // Accessors
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getPricePerSessionAttribute(): float
    {
        return $this->total_sessions > 0 ? $this->price / $this->total_sessions : 0;
    }

    public function getFormattedPricePerSessionAttribute(): string
    {
        return '$' . number_format($this->price_per_session, 2);
    }

    // Methods
    public function isActive(): bool
    {
        return $this->is_active;
    }

    public function getPackageTypeLabel(): string
    {
        return match($this->package_type) {
            'individual' => 'Individual Therapy',
            'group' => 'Group Therapy',
            'family' => 'Family Therapy',
            'couples' => 'Couples Therapy',
            default => ucfirst($this->package_type),
        };
    }

    public function getIncludedServicesText(): string
    {
        if (!$this->included_services) {
            return 'No services specified';
        }

        $services = collect($this->included_services)->map(function ($service) {
            return match($service) {
                'consultation' => 'Initial Consultation',
                'assessment' => 'Psychological Assessment',
                'therapy_sessions' => 'Therapy Sessions',
                'progress_reports' => 'Progress Reports',
                'homework_assignments' => 'Homework Assignments',
                'family_sessions' => 'Family Sessions',
                default => ucfirst(str_replace('_', ' ', $service)),
            };
        });

        return $services->join(', ');
    }
}
