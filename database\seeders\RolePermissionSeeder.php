<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Admin permissions
            'manage-branches',
            'manage-users',
            'view-all-appointments',
            'manage-system-settings',
            
            // Branch Head permissions
            'manage-branch-users',
            'view-branch-appointments',
            'manage-branch-settings',
            
            // Doctor permissions
            'manage-appointments',
            'view-patient-records',
            'create-medical-records',
            'update-appointments',
            
            // Patient permissions
            'book-appointments',
            'view-own-appointments',
            'view-own-medical-records',
            'cancel-appointments',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo([
            'manage-branches',
            'manage-users',
            'view-all-appointments',
            'manage-system-settings',
        ]);

        $branchHeadRole = Role::firstOrCreate(['name' => 'branch_head']);
        $branchHeadRole->givePermissionTo([
            'manage-branch-users',
            'view-branch-appointments',
            'manage-branch-settings',
        ]);

        $doctorRole = Role::firstOrCreate(['name' => 'doctor']);
        $doctorRole->givePermissionTo([
            'manage-appointments',
            'view-patient-records',
            'create-medical-records',
            'update-appointments',
        ]);

        $patientRole = Role::firstOrCreate(['name' => 'patient']);
        $patientRole->givePermissionTo([
            'book-appointments',
            'view-own-appointments',
            'view-own-medical-records',
            'cancel-appointments',
        ]);

        // Create default admin user
        $adminUser = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
                'email_verified_at' => now(),
                'phone' => '+**********',
                'is_active' => true,
            ]
        );

        // Assign admin role to the default admin user
        if (!$adminUser->hasRole('admin')) {
            $adminUser->assignRole('admin');
        }
    }
}
