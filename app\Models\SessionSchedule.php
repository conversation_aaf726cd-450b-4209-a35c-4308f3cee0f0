<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SessionSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_package_id',
        'patient_id',
        'doctor_id',
        'scheduled_by',
        'session_number',
        'scheduled_date',
        'scheduled_time',
        'duration_minutes',
        'status',
        'session_type',
        'location',
        'rescheduled_from',
        'rescheduled_at',
        'rescheduled_by',
        'reschedule_reason',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
        'session_notes',
        'preparation_notes',
        'session_goals',
        'completed_at',
        'completed_by',
    ];

    protected $casts = [
        'scheduled_date' => 'date',
        'scheduled_time' => 'datetime:H:i',
        'session_goals' => 'array',
        'rescheduled_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function patientPackage()
    {
        return $this->belongsTo(PatientPackage::class);
    }

    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function scheduledBy()
    {
        return $this->belongsTo(User::class, 'scheduled_by');
    }

    public function rescheduledBy()
    {
        return $this->belongsTo(User::class, 'rescheduled_by');
    }

    public function cancelledBy()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    public function completedBy()
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    public function originalSession()
    {
        return $this->belongsTo(SessionSchedule::class, 'rescheduled_from');
    }

    public function rescheduledSessions()
    {
        return $this->hasMany(SessionSchedule::class, 'rescheduled_from');
    }

    public function attendance()
    {
        return $this->hasOne(SessionAttendance::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('scheduled_date', today());
    }

    public function scopeUpcoming($query)
    {
        return $query->where('scheduled_date', '>=', today())
                    ->where('status', 'scheduled');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('scheduled_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeOverdue($query)
    {
        return $query->where('scheduled_date', '<', today())
                    ->where('status', 'scheduled');
    }

    // Accessors
    public function getScheduledDateTimeAttribute()
    {
        return Carbon::parse($this->scheduled_date->format('Y-m-d') . ' ' . $this->scheduled_time);
    }

    public function getFormattedScheduledTimeAttribute()
    {
        return $this->scheduled_time ? Carbon::parse($this->scheduled_time)->format('g:i A') : null;
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'scheduled' => 'Scheduled',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'no_show' => 'No Show',
            'rescheduled' => 'Rescheduled',
            default => ucfirst($this->status),
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'scheduled' => 'blue',
            'completed' => 'green',
            'cancelled' => 'red',
            'no_show' => 'orange',
            'rescheduled' => 'yellow',
            default => 'gray',
        };
    }

    public function getSessionTypeLabel()
    {
        return match($this->session_type) {
            'individual' => 'Individual',
            'group' => 'Group',
            'assessment' => 'Assessment',
            'follow_up' => 'Follow-up',
            default => ucfirst($this->session_type),
        };
    }

    // Methods
    public function isToday(): bool
    {
        return $this->scheduled_date->isToday();
    }

    public function isUpcoming(): bool
    {
        return $this->scheduled_date >= today() && $this->status === 'scheduled';
    }

    public function isPast(): bool
    {
        return $this->scheduled_date < today();
    }

    public function isOverdue(): bool
    {
        return $this->scheduled_date < today() && $this->status === 'scheduled';
    }

    public function canBeRescheduled(): bool
    {
        return in_array($this->status, ['scheduled']) && $this->scheduled_date >= today();
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['scheduled']) && $this->scheduled_date >= today();
    }

    public function canMarkAttendance(): bool
    {
        return $this->status === 'scheduled' && 
               $this->scheduled_date <= today() &&
               !$this->attendance;
    }

    public function markAsCompleted($completedBy = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'completed_by' => $completedBy ?? auth()->id(),
        ]);
    }

    public function markAsCancelled($reason = null, $cancelledBy = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancelled_by' => $cancelledBy ?? auth()->id(),
            'cancellation_reason' => $reason,
        ]);
    }

    public function markAsNoShow(): void
    {
        $this->update([
            'status' => 'no_show',
        ]);
    }

    public function reschedule($newDate, $newTime, $reason = null, $rescheduledBy = null): SessionSchedule
    {
        // Mark current session as rescheduled
        $this->update([
            'status' => 'rescheduled',
            'rescheduled_at' => now(),
            'rescheduled_by' => $rescheduledBy ?? auth()->id(),
            'reschedule_reason' => $reason,
        ]);

        // Create new session
        return self::create([
            'patient_package_id' => $this->patient_package_id,
            'patient_id' => $this->patient_id,
            'doctor_id' => $this->doctor_id,
            'scheduled_by' => $rescheduledBy ?? auth()->id(),
            'session_number' => $this->session_number,
            'scheduled_date' => $newDate,
            'scheduled_time' => $newTime,
            'duration_minutes' => $this->duration_minutes,
            'session_type' => $this->session_type,
            'location' => $this->location,
            'rescheduled_from' => $this->id,
            'session_goals' => $this->session_goals,
            'preparation_notes' => $this->preparation_notes,
        ]);
    }
}
