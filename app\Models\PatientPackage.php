<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PatientPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id',
        'therapy_package_id',
        'assigned_by',
        'assigned_doctor_id',
        'start_date',
        'end_date',
        'sessions_completed',
        'sessions_remaining',
        'status',
        'original_price',
        'discount_given',
        'total_amount_after_discount',
        'number_of_installments',
        'payment_type',
        'amount_paid',
        'payment_status',
        'payment_notes',
        'notes',
        'last_session_date',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'last_session_date' => 'datetime',
        'original_price' => 'decimal:2',
        'discount_given' => 'decimal:2',
        'total_amount_after_discount' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function patient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function therapyPackage(): BelongsTo
    {
        return $this->belongsTo(TherapyPackage::class);
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function assignedDoctor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_doctor_id');
    }

    public function packageSessions(): HasMany
    {
        return $this->hasMany(PackageSession::class);
    }

    public function installments()
    {
        return $this->hasMany(PackageInstallment::class);
    }

    public function sessionSchedules()
    {
        return $this->hasMany(SessionSchedule::class);
    }

    public function sessionAttendances()
    {
        return $this->hasMany(SessionAttendance::class);
    }

    public function completedSessions(): HasMany
    {
        return $this->hasMany(PackageSession::class)->where('status', 'completed');
    }

    public function upcomingSessions(): HasMany
    {
        return $this->hasMany(PackageSession::class)
            ->where('status', 'scheduled')
            ->where('session_date', '>=', now()->toDateString());
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeForDoctor($query, $doctorId)
    {
        return $query->where('assigned_doctor_id', $doctorId);
    }

    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('end_date', '<=', now()->addDays($days))
            ->where('status', 'active');
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        $totalSessions = $this->therapyPackage->total_sessions;
        return $totalSessions > 0 ? ($this->sessions_completed / $totalSessions) * 100 : 0;
    }

    public function getFormattedAmountPaidAttribute(): string
    {
        return '$' . number_format($this->amount_paid, 2);
    }

    public function getRemainingAmountAttribute(): float
    {
        return $this->total_amount_after_discount - $this->amount_paid;
    }

    public function getDiscountPercentageAttribute(): float
    {
        if ($this->original_price > 0) {
            return ($this->discount_given / $this->original_price) * 100;
        }
        return 0;
    }

    public function getPaymentProgressPercentageAttribute(): float
    {
        if ($this->total_amount_after_discount > 0) {
            return ($this->amount_paid / $this->total_amount_after_discount) * 100;
        }
        return 0;
    }

    public function getInstallmentAmountAttribute(): float
    {
        if ($this->number_of_installments > 0) {
            return $this->total_amount_after_discount / $this->number_of_installments;
        }
        return $this->total_amount_after_discount;
    }

    public function getFormattedRemainingAmountAttribute(): string
    {
        return '$' . number_format($this->remaining_amount, 2);
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'expired' => 'Expired',
            'on_hold' => 'On Hold',
            default => ucfirst($this->status),
        };
    }

    public function getPaymentStatusLabelAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'Payment Pending',
            'partial' => 'Partially Paid',
            'paid' => 'Fully Paid',
            'refunded' => 'Refunded',
            default => ucfirst($this->payment_status),
        };
    }

    // Methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isExpired(): bool
    {
        return $this->end_date < now()->toDateString();
    }

    public function isCompleted(): bool
    {
        return $this->sessions_completed >= $this->therapyPackage->total_sessions;
    }

    public function canScheduleSession(): bool
    {
        return $this->isActive() &&
               !$this->isExpired() &&
               !$this->isCompleted() &&
               $this->sessions_remaining > 0;
    }

    // Additional financial methods
    public function isFullyPaid(): bool
    {
        return $this->payment_status === 'paid' ||
               $this->amount_paid >= $this->total_amount_after_discount;
    }

    public function hasOverdueInstallments(): bool
    {
        return $this->installments()
            ->where('status', 'overdue')
            ->exists();
    }

    public function getNextDueInstallment()
    {
        return $this->installments()
            ->where('status', 'pending')
            ->orderBy('due_date')
            ->first();
    }

    public function getPaidInstallmentsCount(): int
    {
        return $this->installments()
            ->where('status', 'paid')
            ->count();
    }

    public function getFormattedOriginalPriceAttribute(): string
    {
        return '$' . number_format($this->original_price, 2);
    }

    public function getFormattedDiscountAttribute(): string
    {
        return '$' . number_format($this->discount_given, 2);
    }

    public function getFormattedTotalAmountAttribute(): string
    {
        return '$' . number_format($this->total_amount_after_discount, 2);
    }

    public function getFormattedInstallmentAmountAttribute(): string
    {
        return '$' . number_format($this->installment_amount, 2);
    }

    public function completeSession(): void
    {
        $this->increment('sessions_completed');
        $this->decrement('sessions_remaining');
        $this->update(['last_session_date' => now()]);

        if ($this->isCompleted()) {
            $this->update(['status' => 'completed']);
        }
    }

    public function calculateEndDate(): Carbon
    {
        return $this->start_date->addDays($this->therapyPackage->validity_days);
    }
}
