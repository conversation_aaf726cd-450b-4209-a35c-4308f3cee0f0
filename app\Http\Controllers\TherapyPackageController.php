<?php

namespace App\Http\Controllers;

use App\Models\TherapyPackage;
use App\Http\Requests\TherapyPackageRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TherapyPackageController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('role:admin|branch_head');
    }

    public function index(Request $request)
    {
        $packages = TherapyPackage::with(['creator:id,name'])
            ->when($request->search, function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->type, function ($query, $type) {
                $query->where('package_type', $type);
            })
            ->when($request->status !== null, function ($query) use ($request) {
                $query->where('is_active', $request->status === 'active');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('TherapyPackages/Index', [
            'packages' => $packages,
            'filters' => $request->only(['search', 'type', 'status']),
            'packageTypes' => [
                'individual' => 'Individual Therapy',
                'group' => 'Group Therapy',
                'family' => 'Family Therapy',
                'couples' => 'Couples Therapy',
            ],
        ]);
    }

    public function show(TherapyPackage $therapyPackage)
    {
        $therapyPackage->load([
            'creator:id,name',
            'patientPackages' => function ($query) {
                $query->with(['patient:id,name,email', 'assignedDoctor:id,name'])
                    ->latest();
            }
        ]);

        return Inertia::render('TherapyPackages/Show', [
            'package' => $therapyPackage,
            'stats' => [
                'total_assigned' => $therapyPackage->patientPackages()->count(),
                'active_assignments' => $therapyPackage->activePatientPackages()->count(),
                'completed_assignments' => $therapyPackage->patientPackages()->where('status', 'completed')->count(),
                'total_revenue' => $therapyPackage->patientPackages()->sum('amount_paid'),
            ],
        ]);
    }

    public function create()
    {
        return Inertia::render('TherapyPackages/Create', [
            'packageTypes' => [
                'individual' => 'Individual Therapy',
                'group' => 'Group Therapy',
                'family' => 'Family Therapy',
                'couples' => 'Couples Therapy',
            ],
            'serviceOptions' => [
                'consultation' => 'Initial Consultation',
                'assessment' => 'Psychological Assessment',
                'therapy_sessions' => 'Therapy Sessions',
                'progress_reports' => 'Progress Reports',
                'homework_assignments' => 'Homework Assignments',
                'family_sessions' => 'Family Sessions',
            ],
        ]);
    }

    public function store(TherapyPackageRequest $request)
    {
        $package = TherapyPackage::create([
            ...$request->validated(),
            'created_by' => auth()->id(),
        ]);

        return redirect()->route('therapy-packages.show', $package)
            ->with('success', 'Therapy package created successfully.');
    }

    public function edit(TherapyPackage $therapyPackage)
    {
        return Inertia::render('TherapyPackages/Edit', [
            'package' => $therapyPackage,
            'packageTypes' => [
                'individual' => 'Individual Therapy',
                'group' => 'Group Therapy',
                'family' => 'Family Therapy',
                'couples' => 'Couples Therapy',
            ],
            'serviceOptions' => [
                'consultation' => 'Initial Consultation',
                'assessment' => 'Psychological Assessment',
                'therapy_sessions' => 'Therapy Sessions',
                'progress_reports' => 'Progress Reports',
                'homework_assignments' => 'Homework Assignments',
                'family_sessions' => 'Family Sessions',
            ],
        ]);
    }

    public function update(TherapyPackageRequest $request, TherapyPackage $therapyPackage)
    {
        $therapyPackage->update($request->validated());

        return redirect()->route('therapy-packages.show', $therapyPackage)
            ->with('success', 'Therapy package updated successfully.');
    }

    public function destroy(TherapyPackage $therapyPackage)
    {
        // Check if package has active assignments
        if ($therapyPackage->activePatientPackages()->exists()) {
            return back()->withErrors([
                'error' => 'Cannot delete package with active patient assignments.'
            ]);
        }

        $therapyPackage->delete();

        return redirect()->route('therapy-packages.index')
            ->with('success', 'Therapy package deleted successfully.');
    }

    public function toggle(TherapyPackage $therapyPackage)
    {
        $therapyPackage->update([
            'is_active' => !$therapyPackage->is_active
        ]);

        $status = $therapyPackage->is_active ? 'activated' : 'deactivated';
        
        return back()->with('success', "Therapy package {$status} successfully.");
    }
}
