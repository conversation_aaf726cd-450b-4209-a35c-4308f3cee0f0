<?php

/**
 * Code Quality Checker for Therapy Application
 * 
 * This script performs basic code quality checks
 */

echo "🔍 Running Code Quality Checks...\n\n";

$errors = [];
$warnings = [];

// Check 1: Ensure all controllers have proper authorization
echo "1. Checking controller authorization...\n";
$controllerPath = __DIR__ . '/../app/Http/Controllers';
$controllers = glob($controllerPath . '/*.php');
$controllers = array_merge($controllers, glob($controllerPath . '/*/*.php'));

foreach ($controllers as $controller) {
    $content = file_get_contents($controller);
    $filename = basename($controller);
    
    // Skip auth controllers
    if (strpos($filename, 'Auth') !== false) {
        continue;
    }
    
    // Check for auth middleware or manual auth checks
    if (!preg_match('/middleware.*auth|auth\(\)->user\(\)|hasRole|hasAnyRole/', $content)) {
        $warnings[] = "Controller {$filename} may be missing authentication checks";
    }
}

// Check 2: Ensure all models have proper fillable/guarded
echo "2. Checking model mass assignment protection...\n";
$modelPath = __DIR__ . '/../app/Models';
$models = glob($modelPath . '/*.php');

foreach ($models as $model) {
    $content = file_get_contents($model);
    $filename = basename($model);
    
    if (!preg_match('/\$fillable\s*=|\$guarded\s*=/', $content)) {
        $errors[] = "Model {$filename} is missing mass assignment protection (\$fillable or \$guarded)";
    }
}

// Check 3: Ensure all migrations have proper indexes
echo "3. Checking migration indexes...\n";
$migrationPath = __DIR__ . '/../database/migrations';
$migrations = glob($migrationPath . '/*.php');

foreach ($migrations as $migration) {
    $content = file_get_contents($migration);
    $filename = basename($migration);
    
    // Check for foreign keys without indexes
    if (preg_match('/foreignId|foreign\(/', $content) && !preg_match('/index\(/', $content)) {
        $warnings[] = "Migration {$filename} has foreign keys but may be missing indexes";
    }
}

// Check 4: Ensure environment variables are properly configured
echo "4. Checking environment configuration...\n";
$envPath = __DIR__ . '/../.env';
if (file_exists($envPath)) {
    $envContent = file_get_contents($envPath);
    
    $requiredVars = [
        'APP_KEY',
        'DB_CONNECTION',
        'DB_DATABASE',
        'CACHE_STORE',
        'SESSION_DRIVER',
        'QUEUE_CONNECTION'
    ];
    
    foreach ($requiredVars as $var) {
        if (!preg_match("/^{$var}=.+$/m", $envContent)) {
            $errors[] = "Environment variable {$var} is not set or empty";
        }
    }
    
    // Check for debug mode in production
    if (preg_match('/APP_DEBUG=true/', $envContent) && preg_match('/APP_ENV=production/', $envContent)) {
        $errors[] = "Debug mode is enabled in production environment";
    }
}

// Check 5: Ensure proper validation in form requests
echo "5. Checking form request validation...\n";
$requestPath = __DIR__ . '/../app/Http/Requests';
if (is_dir($requestPath)) {
    $requests = glob($requestPath . '/*.php');
    
    foreach ($requests as $request) {
        $content = file_get_contents($request);
        $filename = basename($request);
        
        if (!preg_match('/public function rules\(\)/', $content)) {
            $errors[] = "Form request {$filename} is missing rules() method";
        }
        
        if (!preg_match('/public function authorize\(\)/', $content)) {
            $warnings[] = "Form request {$filename} is missing authorize() method";
        }
    }
}

// Display results
echo "\n📊 Code Quality Report:\n";
echo "========================\n\n";

if (empty($errors) && empty($warnings)) {
    echo "✅ All checks passed! Your code looks good.\n";
} else {
    if (!empty($errors)) {
        echo "❌ ERRORS (" . count($errors) . "):\n";
        foreach ($errors as $error) {
            echo "   • {$error}\n";
        }
        echo "\n";
    }
    
    if (!empty($warnings)) {
        echo "⚠️  WARNINGS (" . count($warnings) . "):\n";
        foreach ($warnings as $warning) {
            echo "   • {$warning}\n";
        }
        echo "\n";
    }
}

echo "🏁 Code quality check completed.\n";

// Exit with error code if there are errors
exit(empty($errors) ? 0 : 1);
