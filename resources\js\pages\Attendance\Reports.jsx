import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
    BarChart3,
    Download,
    Calendar,
    User,
    Clock,
    TrendingUp,
    TrendingDown
} from 'lucide-react';

export default function AttendanceReports({ auth, reportData, filters }) {
    const [selectedFilters, setSelectedFilters] = useState({
        date_from: filters.date_from || '',
        date_to: filters.date_to || '',
        doctor_id: filters.doctor_id || '',
        patient_id: filters.patient_id || '',
        status: filters.status || '',
        report_type: filters.report_type || 'summary'
    });

    const handleFilterChange = (key, value) => {
        setSelectedFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = () => {
        router.get('/attendance/reports', selectedFilters);
    };

    const exportReport = (format) => {
        const params = new URLSearchParams(selectedFilters);
        params.append('export', format);
        window.open(`/attendance/reports/export?${params.toString()}`);
    };

    const StatCard = ({ title, value, change, icon: Icon, color = 'blue' }) => (
        <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        <Icon className={`h-6 w-6 text-${color}-600`} />
                    </div>
                    <div className="ml-5 w-0 flex-1">
                        <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
                            <dd className="flex items-baseline">
                                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                                {change !== undefined && (
                                    <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                                        change >= 0 ? 'text-green-600' : 'text-red-600'
                                    }`}>
                                        {change >= 0 ? (
                                            <TrendingUp className="self-center flex-shrink-0 h-4 w-4" />
                                        ) : (
                                            <TrendingDown className="self-center flex-shrink-0 h-4 w-4" />
                                        )}
                                        <span className="sr-only">{change >= 0 ? 'Increased' : 'Decreased'} by</span>
                                        {Math.abs(change)}%
                                    </div>
                                )}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    );

    const AttendanceChart = ({ data }) => {
        const maxValue = Math.max(...Object.values(data));
        
        return (
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Attendance Trends</h3>
                <div className="space-y-4">
                    {Object.entries(data).map(([status, count]) => {
                        const percentage = maxValue > 0 ? (count / maxValue) * 100 : 0;
                        const colors = {
                            present: 'bg-green-500',
                            absent: 'bg-red-500',
                            late: 'bg-yellow-500',
                            excused: 'bg-blue-500'
                        };
                        
                        return (
                            <div key={status} className="flex items-center">
                                <div className="w-20 text-sm font-medium text-gray-700 capitalize">
                                    {status}
                                </div>
                                <div className="flex-1 mx-4">
                                    <div className="bg-gray-200 rounded-full h-4">
                                        <div 
                                            className={`h-4 rounded-full ${colors[status] || 'bg-gray-500'}`}
                                            style={{ width: `${percentage}%` }}
                                        />
                                    </div>
                                </div>
                                <div className="w-12 text-sm font-medium text-gray-900 text-right">
                                    {count}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    const PatientAttendanceTable = ({ patients }) => (
        <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Patient Attendance Summary</h3>
            </div>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Patient
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total Sessions
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Present
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Absent
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Late
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Attendance Rate
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {patients.map((patient) => (
                            <tr key={patient.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                        <User className="h-5 w-5 text-gray-400 mr-2" />
                                        <div className="text-sm font-medium text-gray-900">
                                            {patient.name}
                                        </div>
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {patient.total_sessions}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                    {patient.present_count}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                    {patient.absent_count}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                    {patient.late_count}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                        <div className="text-sm font-medium text-gray-900">
                                            {patient.attendance_rate}%
                                        </div>
                                        <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                            <div 
                                                className={`h-2 rounded-full ${
                                                    patient.attendance_rate >= 80 ? 'bg-green-500' :
                                                    patient.attendance_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                                                }`}
                                                style={{ width: `${patient.attendance_rate}%` }}
                                            />
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );

    return (
        <AppLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Attendance Reports
                    </h2>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => router.get('/attendance/dashboard')}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            ← Back to Dashboard
                        </button>
                        <button
                            onClick={() => exportReport('pdf')}
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                        >
                            <Download className="w-4 h-4 mr-2" />
                            Export PDF
                        </button>
                        <button
                            onClick={() => exportReport('excel')}
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                        >
                            <Download className="w-4 h-4 mr-2" />
                            Export Excel
                        </button>
                    </div>
                </div>
            }
        >
            <Head title="Attendance Reports" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    {/* Filters */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Report Filters</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    From Date
                                </label>
                                <input
                                    type="date"
                                    value={selectedFilters.date_from}
                                    onChange={(e) => handleFilterChange('date_from', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    To Date
                                </label>
                                <input
                                    type="date"
                                    value={selectedFilters.date_to}
                                    onChange={(e) => handleFilterChange('date_to', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Doctor
                                </label>
                                <select
                                    value={selectedFilters.doctor_id}
                                    onChange={(e) => handleFilterChange('doctor_id', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                >
                                    <option value="">All Doctors</option>
                                    {reportData.doctors?.map(doctor => (
                                        <option key={doctor.id} value={doctor.id}>
                                            Dr. {doctor.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Patient
                                </label>
                                <select
                                    value={selectedFilters.patient_id}
                                    onChange={(e) => handleFilterChange('patient_id', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                >
                                    <option value="">All Patients</option>
                                    {reportData.patients?.map(patient => (
                                        <option key={patient.id} value={patient.id}>
                                            {patient.name}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Status
                                </label>
                                <select
                                    value={selectedFilters.status}
                                    onChange={(e) => handleFilterChange('status', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
                                >
                                    <option value="">All Statuses</option>
                                    <option value="present">Present</option>
                                    <option value="absent">Absent</option>
                                    <option value="late">Late</option>
                                    <option value="excused">Excused</option>
                                </select>
                            </div>
                            <div className="flex items-end">
                                <button
                                    onClick={applyFilters}
                                    className="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                                >
                                    Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard
                            title="Total Sessions"
                            value={reportData.summary?.total_sessions || 0}
                            change={reportData.summary?.sessions_change}
                            icon={Calendar}
                            color="blue"
                        />
                        <StatCard
                            title="Present"
                            value={reportData.summary?.present_count || 0}
                            change={reportData.summary?.present_change}
                            icon={User}
                            color="green"
                        />
                        <StatCard
                            title="Absent"
                            value={reportData.summary?.absent_count || 0}
                            change={reportData.summary?.absent_change}
                            icon={User}
                            color="red"
                        />
                        <StatCard
                            title="Attendance Rate"
                            value={`${reportData.summary?.attendance_rate || 0}%`}
                            change={reportData.summary?.rate_change}
                            icon={BarChart3}
                            color="indigo"
                        />
                    </div>

                    {/* Charts and Tables */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {reportData.attendance_trends && (
                            <AttendanceChart data={reportData.attendance_trends} />
                        )}
                        
                        <div className="bg-white shadow rounded-lg p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Monthly Comparison</h3>
                            <div className="space-y-3">
                                {reportData.monthly_data?.map((month) => (
                                    <div key={month.month} className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-700">
                                            {month.month}
                                        </span>
                                        <div className="flex items-center space-x-4">
                                            <span className="text-sm text-gray-600">
                                                {month.sessions} sessions
                                            </span>
                                            <span className={`text-sm font-medium ${
                                                month.rate >= 80 ? 'text-green-600' :
                                                month.rate >= 60 ? 'text-yellow-600' : 'text-red-600'
                                            }`}>
                                                {month.rate}%
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Patient Details Table */}
                    {reportData.patient_details && (
                        <PatientAttendanceTable patients={reportData.patient_details} />
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
