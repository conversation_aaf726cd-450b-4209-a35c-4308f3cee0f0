<?php

namespace App\Http\Controllers\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Requests\AppointmentUpdateRequest;
use App\Services\AppointmentService;
use Inertia\Inertia;

class AppointmentController extends Controller
{
    public function __construct(
        private AppointmentService $appointmentService
    ) {
        // Middleware is handled in routes
    }

    public function index()
    {
        $appointments = $this->appointmentService->getAppointmentsByDoctor(auth()->id());
        
        return Inertia::render('Doctor/Appointments/Index', [
            'appointments' => $appointments,
            'stats' => [
                'total' => $appointments->count(),
                'today' => $appointments->filter(fn($a) => $a->appointment_date->isToday())->count(),
                'upcoming' => $appointments->filter(fn($a) => $a->appointment_date->isFuture())->count(),
                'completed' => $appointments->where('status', 'completed')->count(),
            ]
        ]);
    }

    public function show($id)
    {
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || $appointment->doctor_id !== auth()->id()) {
            return redirect()->route('doctor.appointments.index')
                ->withErrors(['error' => 'Appointment not found']);
        }

        return Inertia::render('Doctor/Appointments/Show', [
            'appointment' => $appointment->load(['patient.patientProfile', 'medicalRecords'])
        ]);
    }

    public function update(AppointmentUpdateRequest $request, $id)
    {
        try {
            $appointment = $this->appointmentService->getAppointmentById($id);
            
            if (!$appointment || $appointment->doctor_id !== auth()->id()) {
                return back()->withErrors(['error' => 'Appointment not found']);
            }

            $this->appointmentService->updateAppointment($id, $request->validated());
            
            return back()->with('success', 'Appointment updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function complete(AppointmentUpdateRequest $request, $id)
    {
        try {
            $appointment = $this->appointmentService->getAppointmentById($id);
            
            if (!$appointment || $appointment->doctor_id !== auth()->id()) {
                return back()->withErrors(['error' => 'Appointment not found']);
            }

            $this->appointmentService->completeAppointment($id, $request->validated());
            
            return redirect()->route('doctor.appointments.index')
                ->with('success', 'Appointment completed successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function today()
    {
        $appointments = $this->appointmentService->getAppointmentsByDoctor(auth()->id())
            ->filter(fn($a) => $a->appointment_date->isToday());
            
        return Inertia::render('Doctor/Appointments/Today', [
            'appointments' => $appointments
        ]);
    }
}