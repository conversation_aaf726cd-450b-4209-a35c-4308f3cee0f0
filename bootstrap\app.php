<?php

use App\Http\Middleware\HandleAppearance;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\EnsureUserHasRole;
use App\Http\Middleware\RedirectBasedOnRole;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: ['appearance', 'sidebar_state']);

        $middleware->web(append: [
            HandleAppearance::class,
            HandleInertiaRequests::class,
            AddLinkHeadersForPreloadedAssets::class,
            RedirectBasedOnRole::class,
        ]);

        $middleware->alias([
            'role' => EnsureUserHasRole::class,
        ]);

        // Add rate limiting for authentication routes
        $middleware->throttle('auth', '5,1');
        $middleware->throttle('api', '60,1');
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Handle therapy app specific exceptions
        $exceptions->render(function (\App\Exceptions\TherapyAppException $e, $request) {
            $handler = new \App\Http\Handlers\TherapyAppExceptionHandler();
            return $handler->handle($e, $request);
        });

        $exceptions->render(function (\Illuminate\Database\Eloquent\ModelNotFoundException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Resource not found'], 404);
            }
            return redirect()->back()->withErrors(['error' => 'The requested resource was not found']);
        });

        $exceptions->render(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthenticated'], 401);
            }
            return redirect()->route('login');
        });

        $exceptions->render(function (\Illuminate\Auth\Access\AuthorizationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            return redirect()->back()->withErrors(['error' => 'You are not authorized to perform this action']);
        });

        // Handle validation exceptions
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Validation failed',
                    'errors' => $e->errors(),
                    'message' => $e->getMessage()
                ], 422);
            }
            return redirect()->back()->withErrors($e->errors())->withInput();
        });

        // Handle database exceptions
        $exceptions->render(function (\Illuminate\Database\QueryException $e, $request) {
            \Illuminate\Support\Facades\Log::error('Database query error', [
                'message' => $e->getMessage(),
                'sql' => $e->getSql(),
                'bindings' => $e->getBindings(),
                'user_id' => auth()->id(),
                'url' => $request->fullUrl()
            ]);

            if ($request->expectsJson()) {
                return response()->json(['error' => 'Database error occurred'], 500);
            }
            return redirect()->back()->withErrors(['error' => 'A database error occurred. Please try again.']);
        });
    })->create();
