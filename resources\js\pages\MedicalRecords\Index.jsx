import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { FileText, Plus, Eye, Edit, Trash2, User, Calendar } from 'lucide-react';

export default function MedicalRecordsIndex({ auth, records, userRole, canCreate, stats }) {
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const getRecordTypeColor = (type) => {
        switch (type) {
            case 'consultation': return 'bg-blue-100 text-blue-800';
            case 'test_result': return 'bg-green-100 text-green-800';
            case 'prescription': return 'bg-purple-100 text-purple-800';
            case 'diagnosis': return 'bg-red-100 text-red-800';
            case 'treatment_plan': return 'bg-yellow-100 text-yellow-800';
            case 'follow_up': return 'bg-indigo-100 text-indigo-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const handleDelete = (id) => {
        if (confirm('Are you sure you want to delete this medical record?')) {
            router.delete(route('medical-records.destroy', id));
        }
    };

    return (
        <AppLayout>
            <Head title="Medical Records" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex justify-between items-center">
                        <div>
                            <h1 className="text-2xl font-bold">Medical Records</h1>
                            <p className="text-gray-600">
                                {userRole === 'patient' && 'Your medical history and records'}
                                {userRole === 'doctor' && 'Patient records you\'ve created'}
                                {userRole === 'branch_head' && 'Branch medical records overview'}
                                {userRole === 'admin' && 'System-wide medical records'}
                            </p>
                        </div>
                        
                        {canCreate && (
                            <Link href={route('medical-records.create')}>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    New Record
                                </Button>
                            </Link>
                        )}
                    </div>

                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Records</CardTitle>
                                <FileText className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.total}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Recent (30 days)</CardTitle>
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.recent}</div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Record Types</CardTitle>
                                <FileText className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{Object.keys(stats.by_type).length}</div>
                                <p className="text-xs text-muted-foreground">Different types</p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Records List */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Medical Records</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {records.length > 0 ? (
                                <div className="space-y-4">
                                    {records.map((record) => (
                                        <div key={record.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <FileText className="h-4 w-4 text-muted-foreground" />
                                                        <h3 className="font-medium">{record.title}</h3>
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRecordTypeColor(record.record_type)}`}>
                                                            {record.record_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                        </span>
                                                    </div>
                                                    
                                                    <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                                                        {record.description}
                                                    </p>
                                                    
                                                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
                                                        <span className="flex items-center gap-1">
                                                            <Calendar className="h-3 w-3" />
                                                            {formatDate(record.record_date)}
                                                        </span>
                                                        
                                                        {(userRole === 'admin' || userRole === 'branch_head' || userRole === 'doctor') && record.patient && (
                                                            <span className="flex items-center gap-1">
                                                                <User className="h-3 w-3" />
                                                                Patient: {record.patient.name}
                                                            </span>
                                                        )}
                                                        
                                                        {(userRole === 'admin' || userRole === 'branch_head' || userRole === 'patient') && record.doctor && (
                                                            <span className="flex items-center gap-1">
                                                                <User className="h-3 w-3" />
                                                                Dr. {record.doctor.name}
                                                            </span>
                                                        )}
                                                    </div>

                                                    {record.findings && (
                                                        <div className="text-sm">
                                                            <span className="font-medium text-gray-700">Findings:</span>
                                                            <p className="text-gray-600 line-clamp-1">{record.findings}</p>
                                                        </div>
                                                    )}

                                                    {record.recommendations && (
                                                        <div className="text-sm mt-1">
                                                            <span className="font-medium text-gray-700">Recommendations:</span>
                                                            <p className="text-gray-600 line-clamp-1">{record.recommendations}</p>
                                                        </div>
                                                    )}
                                                </div>
                                                
                                                <div className="flex items-center gap-2 ml-4">
                                                    <Link href={route('medical-records.show', record.id)}>
                                                        <Button variant="outline" size="sm">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    
                                                    {(userRole === 'admin' || (userRole === 'doctor' && record.doctor_id === auth.user.id)) && (
                                                        <>
                                                            <Link href={route('medical-records.edit', record.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            
                                                            <Button 
                                                                variant="outline" 
                                                                size="sm"
                                                                onClick={() => handleDelete(record.id)}
                                                                className="text-red-600 hover:text-red-700"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <h3 className="text-lg font-medium mb-2">No medical records found</h3>
                                    <p className="text-muted-foreground mb-4">
                                        {userRole === 'patient' 
                                            ? 'Your medical records will appear here as they are created by your healthcare providers.'
                                            : 'No medical records have been created yet.'
                                        }
                                    </p>
                                    {canCreate && (
                                        <Link href={route('medical-records.create')}>
                                            <Button>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Create First Record
                                            </Button>
                                        </Link>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}