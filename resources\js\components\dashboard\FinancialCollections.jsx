import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Filter, Download, Calendar, RefreshCw, TrendingUp } from 'lucide-react';
import axios from 'axios';
import { useToast } from '@/contexts/ToastContext';
import { showLoading, closeLoading, showError, showSuccess } from '@/utils/sweetAlert';

export default function FinancialCollections({ initialData, availableFilters }) {
    const [data, setData] = useState(initialData?.financial_collections || { data: [], grand_total: {} });
    const [filters, setFilters] = useState({
        year: new Date().getFullYear(),
        month: '',
        payment_mode: 'all',
        date: '',
    });
    const [loading, setLoading] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const toast = useToast();

    const fetchData = async () => {
        setLoading(true);
        const loadingToast = toast.loading('Updating Financial Data...', 'Calculating collections and balances');

        try {
            const response = await axios.get('/dashboard/package-analytics', {
                params: filters
            });
            setData(response.data.financial_collections);
            toast.removeToast(loadingToast);
            toast.success('Financial Data Updated', 'Collections report has been refreshed successfully');
        } catch (error) {
            console.error('Error fetching financial collections data:', error);
            toast.removeToast(loadingToast);
            toast.error('Failed to Load Financial Data', 'Unable to fetch collections data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = async () => {
        await fetchData();
        setShowFilters(false);
    };

    const resetFilters = () => {
        const defaultFilters = {
            year: new Date().getFullYear(),
            month: '',
            payment_mode: 'all',
            date: '',
        };
        setFilters(defaultFilters);
        toast.info('Filters Reset', 'Financial filters have been reset to default values');
    };

    const handleExport = async () => {
        try {
            showLoading('Generating Financial Report', 'Preparing your collections and payments report...');

            // Simulate export process
            await new Promise(resolve => setTimeout(resolve, 2500));

            closeLoading();
            showSuccess('Financial Report Ready!', 'Your collections report has been downloaded successfully.');
        } catch (error) {
            closeLoading();
            showError('Export Failed', 'Unable to generate the financial report. Please try again.');
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(amount || 0);
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="text-lg font-semibold text-center">
                            TOTAL COLLECTIONS DETAILS - {filters.year}-{String(filters.year + 1).slice(-2)}
                        </CardTitle>
                        <CardDescription className="mt-2">
                            Financial overview of package collections and payments
                        </CardDescription>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowFilters(!showFilters)}
                            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                        >
                            <Filter className="h-4 w-4 mr-2" />
                            Filters
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={fetchData}
                            disabled={loading}
                            className="hover:bg-green-50 hover:border-green-300 transition-colors"
                        >
                            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleExport}
                            className="hover:bg-purple-50 hover:border-purple-300 transition-colors"
                        >
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {showFilters && (
                    <div className="mt-4 p-4 border rounded-lg bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div>
                                <Label htmlFor="month">Month</Label>
                                <Select value={filters.month} onValueChange={(value) => handleFilterChange('month', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All</SelectItem>
                                        {Object.entries(availableFilters?.months || {}).map(([value, label]) => (
                                            <SelectItem key={value} value={value}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="payment_mode">Mode of Payment</Label>
                                <Select value={filters.payment_mode} onValueChange={(value) => handleFilterChange('payment_mode', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableFilters?.payment_modes?.map(mode => (
                                            <SelectItem key={mode.value} value={mode.value}>{mode.label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="date">Date</Label>
                                <Input
                                    type="date"
                                    value={filters.date}
                                    onChange={(e) => handleFilterChange('date', e.target.value)}
                                />
                            </div>

                            <div>
                                <Label htmlFor="year">Year</Label>
                                <Select value={filters.year.toString()} onValueChange={(value) => handleFilterChange('year', parseInt(value))}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select year" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableFilters?.years?.map(year => (
                                            <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex items-end gap-2">
                                <Button onClick={applyFilters} className="flex-1">
                                    Apply
                                </Button>
                                <Button variant="outline" onClick={resetFilters}>
                                    Reset
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </CardHeader>

            <CardContent>
                {loading ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="mt-4 text-gray-600 animate-pulse">Calculating financial collections...</p>
                            <div className="mt-2 flex justify-center space-x-1">
                                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300 text-sm">
                            <thead>
                                <tr className="bg-green-700 text-white">
                                    <th className="border border-gray-300 px-2 py-2 text-left">COURSE TYPE</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">LAST YEAR PENDING FEE-23-24 PACKAGE COMPLETED</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">LAST YEAR PENDING FEE-23-24 WITH PACKAGE DUE</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">FEE COLLECTIONS - 24-25</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">DISCOUNT AMOUNT</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">AMOUNT WRITTEN OFF</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">ACTUAL AMOUNT RECEIVED</th>
                                    <th className="border border-gray-300 px-2 py-2 text-center">BALANCE FEE RECOVERABLE</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.data?.map((item, index) => (
                                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                        <td className="border border-gray-300 px-2 py-2 font-medium">{item.course_type}</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">0</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">0</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(item.fee_collections).replace('₹', '')}</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(item.discount_amount).replace('₹', '')}</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(item.written_off_amount).replace('₹', '')}</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(item.actual_amount_received).replace('₹', '')}</td>
                                        <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(item.balance_recoverable).replace('₹', '')}</td>
                                    </tr>
                                ))}
                                
                                {/* Grand Total Row */}
                                <tr className="bg-green-700 text-white font-bold">
                                    <td className="border border-gray-300 px-2 py-2">Grand Total</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">0</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">0</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(data.grand_total?.fee_collections).replace('₹', '')}</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(data.grand_total?.discount_amount).replace('₹', '')}</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(data.grand_total?.written_off_amount).replace('₹', '')}</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(data.grand_total?.actual_amount_received).replace('₹', '')}</td>
                                    <td className="border border-gray-300 px-2 py-2 text-center">{formatCurrency(data.grand_total?.balance_recoverable).replace('₹', '')}</td>
                                </tr>
                            </tbody>
                        </table>

                        {(!data.data || data.data.length === 0) && (
                            <div className="text-center py-8 text-gray-500">
                                No financial data found for the selected filters.
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
