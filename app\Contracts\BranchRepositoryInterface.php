<?php

namespace App\Contracts;

use App\Models\Branch;
use Illuminate\Pagination\LengthAwarePaginator;

interface BranchRepositoryInterface
{
    public function all();
    public function paginate(int $perPage = 15): LengthAwarePaginator;
    public function find(int $id): ?Branch;
    public function create(array $data): Branch;
    public function update(int $id, array $data): Branch;
    public function delete(int $id): bool;
    public function getActive();
    public function getBranchesWithStats();
    public function findByBranchHead(int $branchHeadId): ?Branch;
}