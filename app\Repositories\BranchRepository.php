<?php

namespace App\Repositories;

use App\Contracts\BranchRepositoryInterface;
use App\Models\Branch;
use Illuminate\Pagination\LengthAwarePaginator;

class BranchRepository implements BranchRepositoryInterface
{
    public function all()
    {
        return Branch::with(['branchHead', 'doctorProfiles', 'patientProfiles'])->get();
    }

    public function paginate(int $perPage = 15): LengthAwarePaginator
    {
        return Branch::with(['branchHead', 'doctorProfiles', 'patientProfiles'])
            ->latest()
            ->paginate($perPage);
    }

    public function find(int $id): ?Branch
    {
        return Branch::with(['branchHead', 'doctorProfiles', 'patientProfiles'])->find($id);
    }

    public function create(array $data): Branch
    {
        return Branch::create($data);
    }

    public function update(int $id, array $data): Branch
    {
        $branch = Branch::findOrFail($id);
        $branch->update($data);
        return $branch->fresh();
    }

    public function delete(int $id): bool
    {
        return Branch::destroy($id) > 0;
    }

    public function getActive()
    {
        return Branch::where('is_active', true)->get();
    }

    public function getBranchesWithStats()
    {
        return Branch::withCount(['doctorProfiles', 'patientProfiles', 'appointments'])
            ->with('branchHead')
            ->get();
    }

    public function findByBranchHead(int $branchHeadId): ?Branch
    {
        return Branch::where('branch_head_id', $branchHeadId)->first();
    }
}