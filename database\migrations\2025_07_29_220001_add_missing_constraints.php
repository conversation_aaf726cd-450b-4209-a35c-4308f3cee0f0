<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add comprehensive check constraints for data integrity
        Schema::table('appointments', function (Blueprint $table) {
            $table->check('duration_minutes > 0 AND duration_minutes <= 480', 'duration_check');
            $table->check('fee >= 0', 'fee_check');
            $table->check('appointment_date > created_at', 'appointment_future_check');
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->check('experience_years >= 0 AND experience_years <= 60', 'experience_check');
            $table->check('consultation_fee >= 0', 'consultation_fee_check');
            $table->check('start_time < end_time', 'working_hours_check');
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->check('date_of_birth < CURRENT_DATE', 'birth_date_check');
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->check('LENGTH(phone) >= 10', 'phone_length_check');
        });

        // Add soft deletes to important tables
        Schema::table('appointments', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Add additional unique constraints
        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->unique(['user_id', 'branch_id'], 'doctor_branch_unique');
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->unique(['user_id', 'branch_id'], 'patient_branch_unique');
        });

        // Add missing foreign key constraints with proper cascade rules
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropCheckConstraint('duration_check');
            $table->dropCheckConstraint('fee_check');
            $table->dropCheckConstraint('appointment_future_check');
            $table->dropSoftDeletes();
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->dropCheckConstraint('experience_check');
            $table->dropCheckConstraint('consultation_fee_check');
            $table->dropCheckConstraint('working_hours_check');
            $table->dropUnique('doctor_branch_unique');
            $table->dropSoftDeletes();
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->dropCheckConstraint('birth_date_check');
            $table->dropUnique('patient_branch_unique');
            $table->dropSoftDeletes();
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->dropCheckConstraint('phone_length_check');
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
    }
};
