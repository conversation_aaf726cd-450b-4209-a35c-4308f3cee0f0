<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Add check constraints for data integrity
        Schema::table('appointments', function (Blueprint $table) {
            $table->check('duration_minutes > 0 AND duration_minutes <= 480', 'duration_check');
            $table->check('fee >= 0', 'fee_check');
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->check('experience_years >= 0 AND experience_years <= 60', 'experience_check');
            $table->check('consultation_fee >= 0', 'consultation_fee_check');
        });

        // Add soft deletes to important tables
        Schema::table('appointments', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropCheckConstraint('duration_check');
            $table->dropCheckConstraint('fee_check');
            $table->dropSoftDeletes();
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->dropCheckConstraint('experience_check');
            $table->dropCheckConstraint('consultation_fee_check');
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
