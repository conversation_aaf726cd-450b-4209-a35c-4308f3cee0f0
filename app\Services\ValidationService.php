<?php

namespace App\Services;

use App\Models\User;
use App\Models\DoctorProfile;
use App\Models\PatientProfile;
use App\Models\Appointment;
use App\Models\Branch;
use App\Exceptions\BusinessRuleViolationException;
use App\Exceptions\BranchAccessDeniedException;
use App\Exceptions\UserProfileMismatchException;
use Carbon\Carbon;

class ValidationService
{
    /**
     * Validate appointment business rules
     */
    public function validateAppointment(array $data): void
    {
        $this->validateAppointmentTiming($data);
        $this->validateAppointmentParticipants($data);
        $this->validateAppointmentBranch($data);
        $this->validateAppointmentDuration($data);
    }

    /**
     * Validate user profile consistency
     */
    public function validateUserProfile(User $user, string $role, array $profileData = []): void
    {
        $this->validateRoleProfileConsistency($user, $role);
        $this->validateProfileBranchConsistency($user, $role, $profileData);
        $this->validateProfileRequiredFields($role, $profileData);
    }

    /**
     * Validate doctor working hours and availability
     */
    public function validateDoctorAvailability(DoctorProfile $doctor, Carbon $appointmentTime, int $duration): void
    {
        $this->validateDoctorIsActive($doctor);
        $this->validateDoctorWorkingDay($doctor, $appointmentTime);
        $this->validateDoctorWorkingHours($doctor, $appointmentTime, $duration);
    }

    /**
     * Validate branch access and consistency
     */
    public function validateBranchAccess(int $branchId, ?User $user = null): void
    {
        $user = $user ?? auth()->user();
        
        if (!$user) {
            throw new BranchAccessDeniedException('Authentication required');
        }

        // Admin can access any branch
        if ($user->hasRole('admin')) {
            return;
        }

        $userBranchId = $this->getUserBranchId($user);
        
        if ($branchId !== $userBranchId) {
            throw new BranchAccessDeniedException(
                'Access denied to branch',
                403,
                null,
                [
                    'user_id' => $user->id,
                    'requested_branch_id' => $branchId,
                    'user_branch_id' => $userBranchId
                ]
            );
        }
    }

    private function validateAppointmentTiming(array $data): void
    {
        $appointmentDate = Carbon::parse($data['appointment_date']);
        
        // Must be in the future
        if ($appointmentDate->isPast()) {
            throw new BusinessRuleViolationException('Appointments must be scheduled in the future');
        }

        // Must be within business hours (8 AM to 8 PM)
        $hour = $appointmentDate->hour;
        if ($hour < 8 || $hour >= 20) {
            throw new BusinessRuleViolationException('Appointments can only be scheduled between 8:00 AM and 8:00 PM');
        }

        // No appointments on Sundays
        if ($appointmentDate->dayOfWeek === Carbon::SUNDAY) {
            throw new BusinessRuleViolationException('Appointments cannot be scheduled on Sundays');
        }

        // Must be at least 30 minutes from now
        if ($appointmentDate->diffInMinutes(now()) < 30) {
            throw new BusinessRuleViolationException('Appointments must be scheduled at least 30 minutes in advance');
        }

        // Cannot be more than 6 months in advance
        if ($appointmentDate->diffInMonths(now()) > 6) {
            throw new BusinessRuleViolationException('Appointments cannot be scheduled more than 6 months in advance');
        }
    }

    private function validateAppointmentParticipants(array $data): void
    {
        $doctorId = $data['doctor_id'];
        $patientId = $data['patient_id'] ?? auth()->id();

        // Prevent self-appointments
        if ($doctorId === $patientId) {
            throw new BusinessRuleViolationException('Doctor cannot book appointment with themselves');
        }

        // Validate doctor exists and is active
        $doctor = User::with('doctorProfile')->find($doctorId);
        if (!$doctor || !$doctor->doctorProfile || !$doctor->doctorProfile->is_active) {
            throw new BusinessRuleViolationException('Selected doctor is not available');
        }

        // Validate patient exists and is active
        $patient = User::with('patientProfile')->find($patientId);
        if (!$patient || !$patient->patientProfile || !$patient->patientProfile->is_active) {
            throw new BusinessRuleViolationException('Patient profile is not active');
        }
    }

    private function validateAppointmentBranch(array $data): void
    {
        $doctorId = $data['doctor_id'];
        $patientId = $data['patient_id'] ?? auth()->id();
        $branchId = $data['branch_id'];

        $doctor = User::with('doctorProfile')->find($doctorId);
        $patient = User::with('patientProfile')->find($patientId);

        // Validate branch exists and is active
        $branch = Branch::find($branchId);
        if (!$branch || !$branch->is_active) {
            throw new BusinessRuleViolationException('Selected branch is not available');
        }

        // Doctor and patient must belong to the same branch
        if ($doctor->doctorProfile->branch_id !== $patient->patientProfile->branch_id) {
            throw new BranchAccessDeniedException('Doctor and patient must belong to the same branch');
        }

        // Appointment branch must match doctor/patient branch
        if ($branchId !== $doctor->doctorProfile->branch_id) {
            throw new BusinessRuleViolationException('Appointment branch must match doctor and patient branch');
        }
    }

    private function validateAppointmentDuration(array $data): void
    {
        $duration = $data['duration_minutes'] ?? 60;

        if ($duration < 15) {
            throw new BusinessRuleViolationException('Appointment duration must be at least 15 minutes');
        }

        if ($duration > 480) {
            throw new BusinessRuleViolationException('Appointment duration cannot exceed 8 hours');
        }

        // Duration must be in 15-minute increments
        if ($duration % 15 !== 0) {
            throw new BusinessRuleViolationException('Appointment duration must be in 15-minute increments');
        }
    }

    private function validateRoleProfileConsistency(User $user, string $role): void
    {
        switch ($role) {
            case 'doctor':
                if ($user->patientProfile && !$user->doctorProfile) {
                    throw new UserProfileMismatchException('User has patient profile but being assigned doctor role');
                }
                break;
            case 'patient':
                if ($user->doctorProfile && !$user->patientProfile) {
                    throw new UserProfileMismatchException('User has doctor profile but being assigned patient role');
                }
                break;
        }
    }

    private function validateProfileBranchConsistency(User $user, string $role, array $profileData): void
    {
        if (!isset($profileData['branch_id'])) {
            return;
        }

        $branchId = $profileData['branch_id'];

        // Validate branch exists
        $branch = Branch::find($branchId);
        if (!$branch || !$branch->is_active) {
            throw new BusinessRuleViolationException('Invalid or inactive branch');
        }

        // Validate user has access to this branch
        $this->validateBranchAccess($branchId, $user);
    }

    private function validateProfileRequiredFields(string $role, array $profileData): void
    {
        switch ($role) {
            case 'doctor':
                $required = ['branch_id', 'specialization'];
                break;
            case 'patient':
                $required = ['branch_id'];
                break;
            default:
                return;
        }

        foreach ($required as $field) {
            if (!isset($profileData[$field]) || empty($profileData[$field])) {
                throw new BusinessRuleViolationException("Field '{$field}' is required for {$role} profile");
            }
        }
    }

    private function validateDoctorIsActive(DoctorProfile $doctor): void
    {
        if (!$doctor->is_active) {
            throw new BusinessRuleViolationException('Doctor is not currently active');
        }

        if (!$doctor->user->is_active) {
            throw new BusinessRuleViolationException('Doctor user account is not active');
        }
    }

    private function validateDoctorWorkingDay(DoctorProfile $doctor, Carbon $appointmentTime): void
    {
        $dayOfWeek = strtolower($appointmentTime->format('l'));
        
        if (!in_array($dayOfWeek, $doctor->available_days)) {
            throw new BusinessRuleViolationException("Doctor is not available on {$dayOfWeek}s");
        }
    }

    private function validateDoctorWorkingHours(DoctorProfile $doctor, Carbon $appointmentTime, int $duration): void
    {
        $appointmentStart = $appointmentTime->format('H:i');
        $appointmentEnd = $appointmentTime->copy()->addMinutes($duration)->format('H:i');
        
        $doctorStart = $doctor->start_time->format('H:i');
        $doctorEnd = $doctor->end_time->format('H:i');

        if ($appointmentStart < $doctorStart || $appointmentEnd > $doctorEnd) {
            throw new BusinessRuleViolationException(
                "Appointment time ({$appointmentStart} - {$appointmentEnd}) is outside doctor's working hours ({$doctorStart} - {$doctorEnd})"
            );
        }
    }

    private function getUserBranchId(?User $user): ?int
    {
        if (!$user) {
            return null;
        }

        if ($user->hasRole('branch_head')) {
            return $user->managedBranch?->id;
        }

        if ($user->hasRole('doctor')) {
            return $user->doctorProfile?->branch_id;
        }

        if ($user->hasRole('patient')) {
            return $user->patientProfile?->branch_id;
        }

        return null;
    }

    public function validatePackageAssignment(array $data): void
    {
        // Validate required fields
        if (empty($data['patient_id'])) {
            throw new ValidationException('Patient ID is required');
        }

        if (empty($data['therapy_package_id'])) {
            throw new ValidationException('Therapy package ID is required');
        }

        if (empty($data['assigned_doctor_id'])) {
            throw new ValidationException('Assigned doctor ID is required');
        }

        // Validate financial data
        if (isset($data['original_price']) && $data['original_price'] < 0) {
            throw new ValidationException('Original price cannot be negative');
        }

        if (isset($data['discount_given']) && $data['discount_given'] < 0) {
            throw new ValidationException('Discount cannot be negative');
        }

        if (isset($data['number_of_installments']) && $data['number_of_installments'] < 1) {
            throw new ValidationException('Number of installments must be at least 1');
        }

        // Validate payment type
        if (isset($data['payment_type']) && !in_array($data['payment_type'], ['full_payment', 'installments', 'insurance'])) {
            throw new ValidationException('Invalid payment type');
        }
    }

    public function validatePackageSession(array $data): void
    {
        // Validate required fields
        if (empty($data['patient_package_id'])) {
            throw new ValidationException('Patient package ID is required');
        }

        if (empty($data['session_date'])) {
            throw new ValidationException('Session date is required');
        }

        // Validate session date
        $sessionDate = Carbon::parse($data['session_date']);
        if ($sessionDate->isPast()) {
            throw new ValidationException('Session date cannot be in the past');
        }

        // Validate duration
        if (isset($data['duration_minutes']) && ($data['duration_minutes'] < 15 || $data['duration_minutes'] > 480)) {
            throw new ValidationException('Session duration must be between 15 and 480 minutes');
        }
    }
}
