<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

class AppointmentRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'doctor_id' => ['required', 'exists:users,id'],
            'appointment_date' => ['required', 'date', 'after:now'],
            'duration_minutes' => ['integer', 'min:15', 'max:240'],
            'symptoms' => ['nullable', 'string'],
            'notes' => ['nullable', 'string'],
            'fee' => ['required', 'numeric', 'min:0'],
        ];
    }

    public function messages(): array
    {
        return [
            'doctor_id.required' => 'Please select a doctor',
            'appointment_date.required' => 'Appointment date and time is required',
            'appointment_date.after' => 'Appointment must be scheduled for a future date and time',
            'fee.required' => 'Consultation fee is required',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $appointmentDate = Carbon::parse($this->appointment_date);
            
            // Check if appointment is during business hours (8 AM to 8 PM)
            if ($appointmentDate->hour < 8 || $appointmentDate->hour >= 20) {
                $validator->errors()->add('appointment_date', 'Appointments can only be scheduled between 8:00 AM and 8:00 PM');
            }
            
            // Check if appointment is not on Sunday (assuming clinic is closed)
            if ($appointmentDate->dayOfWeek === Carbon::SUNDAY) {
                $validator->errors()->add('appointment_date', 'Appointments cannot be scheduled on Sundays');
            }
        });
    }
}