import Modal from '@/Components/ui/Modal';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { X, Calendar, Clock, DollarSign, Users } from 'lucide-react';

const PackageViewModal = ({ isOpen, onClose, packageData }) => {
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const footerContent = (
        <Button onClick={onClose} variant="outline">
            <X className="h-4 w-4 mr-2" />
            Close
        </Button>
    );

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={`Package Details: ${packageData?.name || ''}`}
            size="xl"
            showFooter={true}
            footerContent={footerContent}
        >
            {packageData && (
                <div className="space-y-6">
                    {/* Header Section */}
                    <div className="flex items-start justify-between">
                        <div>
                            <h3 className="text-2xl font-bold text-gray-900">{packageData.name}</h3>
                            <div className="mt-2">
                                <Badge variant={packageData.is_active ? 'success' : 'secondary'}>
                                    {packageData.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                            </div>
                        </div>
                        <div className="text-right">
                            <div className="text-3xl font-bold text-blue-600">
                                {formatCurrency(packageData.price)}
                            </div>
                            <div className="text-sm text-gray-500">Package Price</div>
                        </div>
                    </div>

                    {/* Description */}
                    {packageData.description && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                            <p className="text-gray-700">{packageData.description}</p>
                        </div>
                    )}

                    {/* Package Details Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className="bg-blue-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <Users className="h-8 w-8 text-blue-600" />
                                <div className="ml-3">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {packageData.total_sessions}
                                    </div>
                                    <div className="text-sm text-gray-600">Total Sessions</div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-green-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <Clock className="h-8 w-8 text-green-600" />
                                <div className="ml-3">
                                    <div className="text-2xl font-bold text-green-600">
                                        {packageData.duration_months}
                                    </div>
                                    <div className="text-sm text-gray-600">Duration (Months)</div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-purple-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <DollarSign className="h-8 w-8 text-purple-600" />
                                <div className="ml-3">
                                    <div className="text-2xl font-bold text-purple-600">
                                        {formatCurrency(packageData.price / packageData.total_sessions)}
                                    </div>
                                    <div className="text-sm text-gray-600">Per Session</div>
                                </div>
                            </div>
                        </div>

                        <div className="bg-orange-50 p-4 rounded-lg">
                            <div className="flex items-center">
                                <Calendar className="h-8 w-8 text-orange-600" />
                                <div className="ml-3">
                                    <div className="text-2xl font-bold text-orange-600">
                                        {Math.round(packageData.duration_months * 30 / packageData.total_sessions)}
                                    </div>
                                    <div className="text-sm text-gray-600">Days/Session</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Additional Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <h4 className="text-lg font-medium text-gray-900">Package Information</h4>
                            <div className="space-y-3">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Package ID:</span>
                                    <span className="font-medium">#{packageData.id}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Status:</span>
                                    <Badge variant={packageData.is_active ? 'success' : 'secondary'}>
                                        {packageData.is_active ? 'Active' : 'Inactive'}
                                    </Badge>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Created Date:</span>
                                    <span className="font-medium">{formatDate(packageData.created_at)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Last Updated:</span>
                                    <span className="font-medium">{formatDate(packageData.updated_at)}</span>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-4">
                            <h4 className="text-lg font-medium text-gray-900">Pricing Breakdown</h4>
                            <div className="space-y-3">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Total Package Price:</span>
                                    <span className="font-medium">{formatCurrency(packageData.price)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Price per Session:</span>
                                    <span className="font-medium">{formatCurrency(packageData.price / packageData.total_sessions)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">Price per Month:</span>
                                    <span className="font-medium">{formatCurrency(packageData.price / packageData.duration_months)}</span>
                                </div>
                                <div className="flex justify-between border-t pt-2">
                                    <span className="text-gray-600 font-medium">Sessions per Month:</span>
                                    <span className="font-medium">{Math.round(packageData.total_sessions / packageData.duration_months * 10) / 10}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Usage Statistics (if available) */}
                    {packageData.usage_stats && (
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {packageData.usage_stats.active_patients || 0}
                                    </div>
                                    <div className="text-sm text-gray-600">Active Patients</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                        {packageData.usage_stats.completed_packages || 0}
                                    </div>
                                    <div className="text-sm text-gray-600">Completed Packages</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-purple-600">
                                        {formatCurrency(packageData.usage_stats.total_revenue || 0)}
                                    </div>
                                    <div className="text-sm text-gray-600">Total Revenue</div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            )}
        </Modal>
    );
};

export default PackageViewModal;
