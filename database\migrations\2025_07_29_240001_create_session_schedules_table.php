<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('session_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_package_id')->constrained()->onDelete('cascade');
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('doctor_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('scheduled_by')->constrained('users')->onDelete('cascade');
            
            // Session details
            $table->integer('session_number'); // 1, 2, 3, etc.
            $table->date('scheduled_date');
            $table->time('scheduled_time');
            $table->integer('duration_minutes')->default(60);
            
            // Status and management
            $table->enum('status', ['scheduled', 'completed', 'cancelled', 'no_show', 'rescheduled'])->default('scheduled');
            $table->enum('session_type', ['individual', 'group', 'assessment', 'follow_up'])->default('individual');
            $table->string('location')->nullable(); // Room number or location
            
            // Rescheduling tracking
            $table->foreignId('rescheduled_from')->nullable()->constrained('session_schedules')->onDelete('set null');
            $table->timestamp('rescheduled_at')->nullable();
            $table->foreignId('rescheduled_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('reschedule_reason')->nullable();
            
            // Cancellation tracking
            $table->timestamp('cancelled_at')->nullable();
            $table->foreignId('cancelled_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('cancellation_reason')->nullable();
            
            // Session notes and details
            $table->text('session_notes')->nullable();
            $table->text('preparation_notes')->nullable(); // Notes for preparation
            $table->json('session_goals')->nullable(); // Goals for this session
            
            // Completion tracking
            $table->timestamp('completed_at')->nullable();
            $table->foreignId('completed_by')->nullable()->constrained('users')->onDelete('set null');
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['patient_id', 'scheduled_date']);
            $table->index(['doctor_id', 'scheduled_date']);
            $table->index(['scheduled_date', 'status']);
            $table->index(['patient_package_id', 'session_number']);
            $table->index(['status', 'scheduled_date']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('session_schedules');
    }
};
