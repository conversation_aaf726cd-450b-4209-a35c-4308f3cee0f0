import StatsCard from '@/Components/ui/StatsCard';
import { Users, Calendar, Clock, UserCheck } from 'lucide-react';

export default function BranchHeadStats({ stats }) {
    return (
        <>
            <StatsCard 
                title="Total Doctors"
                icon={UserCheck}
                value={stats.total_doctors}
                subtitle="Active in your branch"
            />
            
            <StatsCard 
                title="Total Patients"
                icon={Users}
                value={stats.total_patients}
                subtitle="Registered patients"
            />
            
            <StatsCard 
                title="Today's Appointments"
                icon={Calendar}
                value={stats.todays_appointments}
                subtitle="Scheduled for today"
            />
            
            <StatsCard 
                title="Pending Appointments"
                icon={Clock}
                value={stats.pending_appointments}
                subtitle="Awaiting confirmation"
            />
        </>
    );
}