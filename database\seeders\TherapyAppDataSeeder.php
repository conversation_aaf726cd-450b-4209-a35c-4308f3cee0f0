<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Branch;
use App\Models\DoctorProfile;
use App\Models\PatientProfile;
use App\Models\Appointment;
use Illuminate\Support\Facades\Hash;

class TherapyAppDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create branches
        $branch1 = Branch::create([
            'name' => 'Downtown Therapy Center',
            'address' => '123 Main Street, Downtown, City 12345',
            'phone' => '******-0101',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        $branch2 = Branch::create([
            'name' => 'Northside Wellness Clinic',
            'address' => '456 Oak Avenue, Northside, City 67890',
            'phone' => '******-0102',
            'email' => '<EMAIL>',
            'is_active' => true,
        ]);

        // Create branch heads
        $branchHead1 = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0201',
            'is_active' => true,
        ]);
        $branchHead1->assignRole('branch_head');

        $branchHead2 = User::create([
            'name' => 'Michael <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0202',
            'is_active' => true,
        ]);
        $branchHead2->assignRole('branch_head');

        // Update branches with branch heads
        $branch1->update(['branch_head_id' => $branchHead1->id]);
        $branch2->update(['branch_head_id' => $branchHead2->id]);

        // Create doctors
        $doctor1 = User::create([
            'name' => 'Dr. Emily Rodriguez',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0301',
            'is_active' => true,
        ]);
        $doctor1->assignRole('doctor');

        DoctorProfile::create([
            'user_id' => $doctor1->id,
            'branch_id' => $branch1->id,
            'specialization' => 'Clinical Psychology',
            'license_number' => 'PSY-12345',
            'qualifications' => 'Ph.D. in Clinical Psychology, Licensed Clinical Psychologist',
            'experience_years' => 8,
            'phone' => '******-0301',
            'bio' => 'Specialized in anxiety, depression, and trauma therapy with 8+ years experience.',
            'consultation_fee' => 150.00,
            'available_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            'start_time' => '09:00',
            'end_time' => '17:00',
            'is_active' => true,
        ]);

        $doctor2 = User::create([
            'name' => 'Dr. James Wilson',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0302',
            'is_active' => true,
        ]);
        $doctor2->assignRole('doctor');

        DoctorProfile::create([
            'user_id' => $doctor2->id,
            'branch_id' => $branch1->id,
            'specialization' => 'Cognitive Behavioral Therapy',
            'license_number' => 'CBT-67890',
            'qualifications' => 'M.A. in Counseling Psychology, CBT Certified',
            'experience_years' => 12,
            'phone' => '******-0302',
            'bio' => 'Expert in CBT techniques for mood disorders and behavioral issues.',
            'consultation_fee' => 175.00,
            'available_days' => ['monday', 'wednesday', 'friday'],
            'start_time' => '10:00',
            'end_time' => '18:00',
            'is_active' => true,
        ]);

        $doctor3 = User::create([
            'name' => 'Dr. Lisa Chen',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0303',
            'is_active' => true,
        ]);
        $doctor3->assignRole('doctor');

        DoctorProfile::create([
            'user_id' => $doctor3->id,
            'branch_id' => $branch2->id,
            'specialization' => 'Family Therapy',
            'license_number' => 'FAM-54321',
            'qualifications' => 'M.S.W., Licensed Marriage and Family Therapist',
            'experience_years' => 6,
            'phone' => '******-0303',
            'bio' => 'Specializes in family dynamics, couples therapy, and relationship counseling.',
            'consultation_fee' => 140.00,
            'available_days' => ['tuesday', 'thursday', 'saturday'],
            'start_time' => '08:00',
            'end_time' => '16:00',
            'is_active' => true,
        ]);

        // Create patients
        $patient1 = User::create([
            'name' => 'John Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0401',
            'is_active' => true,
        ]);
        $patient1->assignRole('patient');

        PatientProfile::create([
            'user_id' => $patient1->id,
            'branch_id' => $branch1->id,
            'date_of_birth' => '1985-05-15',
            'gender' => 'male',
            'phone' => '******-0401',
            'address' => '789 Pine Street, Apartment 4B, City 12345',
            'emergency_contact_name' => 'Jane Smith',
            'emergency_contact_phone' => '******-0402',
            'medical_history' => 'History of anxiety and mild depression',
            'current_medications' => 'Sertraline 50mg daily',
            'allergies' => 'None known',
            'insurance_provider' => 'BlueCross BlueShield',
            'insurance_number' => 'BC123456789',
            'is_active' => true,
        ]);

        $patient2 = User::create([
            'name' => 'Maria Garcia',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0403',
            'is_active' => true,
        ]);
        $patient2->assignRole('patient');

        PatientProfile::create([
            'user_id' => $patient2->id,
            'branch_id' => $branch1->id,
            'date_of_birth' => '1992-03-22',
            'gender' => 'female',
            'phone' => '******-0403',
            'address' => '321 Elm Drive, City 12345',
            'emergency_contact_name' => 'Carlos Garcia',
            'emergency_contact_phone' => '******-0404',
            'medical_history' => 'PTSD following car accident',
            'current_medications' => 'None',
            'allergies' => 'Penicillin',
            'insurance_provider' => 'Aetna',
            'insurance_number' => 'AET987654321',
            'is_active' => true,
        ]);

        $patient3 = User::create([
            'name' => 'Robert Brown',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '******-0405',
            'is_active' => true,
        ]);
        $patient3->assignRole('patient');

        PatientProfile::create([
            'user_id' => $patient3->id,
            'branch_id' => $branch2->id,
            'date_of_birth' => '1978-11-08',
            'gender' => 'male',
            'phone' => '******-0405',
            'address' => '654 Cedar Lane, City 67890',
            'emergency_contact_name' => 'Susan Brown',
            'emergency_contact_phone' => '******-0406',
            'medical_history' => 'Bipolar disorder, stable on medication',
            'current_medications' => 'Lithium 300mg twice daily, Risperidone 2mg daily',
            'allergies' => 'Latex',
            'insurance_provider' => 'United Healthcare',
            'insurance_number' => 'UH456789123',
            'is_active' => true,
        ]);

        // Create sample appointments
        Appointment::create([
            'patient_id' => $patient1->id,
            'doctor_id' => $doctor1->id,
            'branch_id' => $branch1->id,
            'appointment_date' => now()->addDays(1)->setTime(10, 0),
            'duration_minutes' => 60,
            'status' => 'scheduled',
            'symptoms' => 'Increased anxiety, difficulty sleeping',
            'notes' => 'Follow-up session for anxiety management',
            'fee' => 150.00,
            'payment_status' => 'pending',
        ]);

        Appointment::create([
            'patient_id' => $patient2->id,
            'doctor_id' => $doctor2->id,
            'branch_id' => $branch1->id,
            'appointment_date' => now()->addDays(2)->setTime(14, 0),
            'duration_minutes' => 90,
            'status' => 'confirmed',
            'symptoms' => 'Flashbacks, nightmares related to trauma',
            'notes' => 'CBT session focused on trauma processing',
            'fee' => 175.00,
            'payment_status' => 'paid',
        ]);

        Appointment::create([
            'patient_id' => $patient3->id,
            'doctor_id' => $doctor3->id,
            'branch_id' => $branch2->id,
            'appointment_date' => now()->subDays(1)->setTime(11, 0),
            'duration_minutes' => 60,
            'status' => 'completed',
            'symptoms' => 'Mood swings, medication adjustment needed',
            'notes' => 'Regular monitoring session',
            'diagnosis' => 'Bipolar I disorder, currently stable',
            'treatment_plan' => 'Continue current medications, weekly therapy sessions',
            'prescription' => 'Continue Lithium 300mg BID, Risperidone 2mg daily',
            'fee' => 140.00,
            'payment_status' => 'paid',
        ]);
    }
}