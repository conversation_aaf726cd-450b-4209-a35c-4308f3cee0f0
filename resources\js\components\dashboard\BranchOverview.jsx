import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/Components/ui/card';
import { Building2, Users, UserCheck, Calendar } from 'lucide-react';

export default function BranchOverview({ branchStats = [] }) {
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    Branch Overview
                </CardTitle>
            </CardHeader>
            <CardContent>
                {branchStats.length > 0 ? (
                    <div className="space-y-4">
                        {branchStats.slice(0, 5).map((branch, index) => (
                            <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                                <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-medium">{branch.name}</h4>
                                    <span className="text-xs text-muted-foreground">
                                        {branch.city || 'Branch'}
                                    </span>
                                </div>
                                
                                <div className="grid grid-cols-3 gap-4 text-sm">
                                    <div className="flex items-center gap-1">
                                        <UserCheck className="h-3 w-3 text-muted-foreground" />
                                        <span>{branch.doctors || 0} Doctors</span>
                                    </div>
                                    
                                    <div className="flex items-center gap-1">
                                        <Users className="h-3 w-3 text-muted-foreground" />
                                        <span>{branch.patients || 0} Patients</span>
                                    </div>
                                    
                                    <div className="flex items-center gap-1">
                                        <Calendar className="h-3 w-3 text-muted-foreground" />
                                        <span>{branch.appointments || 0} Appointments</span>
                                    </div>
                                </div>
                            </div>
                        ))}
                        
                        {branchStats.length > 5 && (
                            <div className="text-center py-2">
                                <span className="text-sm text-muted-foreground">
                                    +{branchStats.length - 5} more branches
                                </span>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="text-center py-6">
                        <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No branches found</p>
                        <p className="text-sm text-muted-foreground">
                            Create your first branch to get started
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}