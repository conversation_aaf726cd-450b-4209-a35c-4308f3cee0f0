import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { ArrowLeft, User, Mail, Phone, MapPin, Calendar, Shield, Building2, Edit, Trash2 } from 'lucide-react';

export default function UserShow({ user }) {
    const { auth } = usePage().props;
    const currentUser = auth?.user;
    const userRole = currentUser?.roles?.[0]?.name;

    // Check if current user can edit/delete this user
    const canEdit = userRole === 'admin' || (userRole === 'branch_head' && user.branch_id === currentUser.branch_id);
    const canDelete = userRole === 'admin';

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const getRoleColor = (role) => {
        const colors = {
            admin: 'bg-red-100 text-red-800',
            branch_head: 'bg-blue-100 text-blue-800',
            doctor: 'bg-green-100 text-green-800',
            patient: 'bg-purple-100 text-purple-800'
        };
        return colors[role] || 'bg-gray-100 text-gray-800';
    };

    return (
        <AppLayout>
            <Head title={`User: ${user.name}`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Link href={route('users.index')}>
                                <Button variant="outline" size="sm">
                                    <ArrowLeft className="h-4 w-4" />
                                </Button>
                            </Link>
                            <div>
                                <h1 className="text-2xl font-bold">{user.name}</h1>
                                <p className="text-gray-600">User Details</p>
                            </div>
                        </div>

                        {/* Actions */}
                        <div className="flex gap-2">
                            {canEdit && (
                                <Link href={route('users.edit', user.id)}>
                                    <Button variant="outline" size="sm">
                                        <Edit className="h-4 w-4 mr-2" />
                                        Edit
                                    </Button>
                                </Link>
                            )}
                            {canDelete && (
                                <Button variant="destructive" size="sm">
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                </Button>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Basic Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-500">Full Name</label>
                                    <p className="text-lg">{user.name}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Email Address</label>
                                    <p className="flex items-center gap-2">
                                        <Mail className="h-4 w-4 text-gray-400" />
                                        {user.email}
                                    </p>
                                </div>

                                {user.phone && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Phone Number</label>
                                        <p className="flex items-center gap-2">
                                            <Phone className="h-4 w-4 text-gray-400" />
                                            {user.phone}
                                        </p>
                                    </div>
                                )}

                                {user.address && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Address</label>
                                        <p className="flex items-center gap-2">
                                            <MapPin className="h-4 w-4 text-gray-400" />
                                            {user.address}
                                        </p>
                                    </div>
                                )}

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Email Verified</label>
                                    <p>
                                        <Badge variant={user.email_verified_at ? "default" : "secondary"}>
                                            {user.email_verified_at ? "Verified" : "Not Verified"}
                                        </Badge>
                                    </p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Role & Access Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Shield className="h-5 w-5" />
                                    Role & Access
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-500">User Role</label>
                                    <div className="mt-1">
                                        {user.roles && user.roles.length > 0 ? (
                                            user.roles.map((role) => (
                                                <Badge 
                                                    key={role.name} 
                                                    className={getRoleColor(role.name)}
                                                >
                                                    {role.name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                </Badge>
                                            ))
                                        ) : (
                                            <Badge variant="secondary">No Role Assigned</Badge>
                                        )}
                                    </div>
                                </div>

                                {user.branch && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Branch</label>
                                        <p className="flex items-center gap-2">
                                            <Building2 className="h-4 w-4 text-gray-400" />
                                            <Link 
                                                href={route('branches.show', user.branch.id)}
                                                className="text-blue-600 hover:underline"
                                            >
                                                {user.branch.name}
                                            </Link>
                                        </p>
                                        <p className="text-sm text-gray-500 mt-1">
                                            {user.branch.city}, {user.branch.state}
                                        </p>
                                    </div>
                                )}

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Account Status</label>
                                    <p>
                                        <Badge variant={user.is_active ? "default" : "destructive"}>
                                            {user.is_active ? "Active" : "Inactive"}
                                        </Badge>
                                    </p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Member Since</label>
                                    <p className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-gray-400" />
                                        {formatDate(user.created_at)}
                                    </p>
                                </div>

                                {user.updated_at !== user.created_at && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Last Updated</label>
                                        <p className="flex items-center gap-2">
                                            <Calendar className="h-4 w-4 text-gray-400" />
                                            {formatDate(user.updated_at)}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Additional Information for specific roles */}
                    {user.roles?.some(role => role.name === 'doctor') && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Doctor Information</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {user.specialization && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Specialization</label>
                                            <p>{user.specialization}</p>
                                        </div>
                                    )}
                                    {user.license_number && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">License Number</label>
                                            <p>{user.license_number}</p>
                                        </div>
                                    )}
                                    {user.experience_years && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Years of Experience</label>
                                            <p>{user.experience_years} years</p>
                                        </div>
                                    )}
                                    {user.consultation_fee && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Consultation Fee</label>
                                            <p>${user.consultation_fee}</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {user.roles?.some(role => role.name === 'patient') && (
                        <Card>
                            <CardHeader>
                                <CardTitle>Patient Information</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {user.date_of_birth && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                                            <p>{formatDate(user.date_of_birth)}</p>
                                        </div>
                                    )}
                                    {user.gender && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Gender</label>
                                            <p className="capitalize">{user.gender}</p>
                                        </div>
                                    )}
                                    {user.emergency_contact && (
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Emergency Contact</label>
                                            <p>{user.emergency_contact}</p>
                                        </div>
                                    )}
                                    {user.medical_history && (
                                        <div className="md:col-span-2">
                                            <label className="text-sm font-medium text-gray-500">Medical History</label>
                                            <p className="text-sm bg-gray-50 p-3 rounded-md mt-1">{user.medical_history}</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}