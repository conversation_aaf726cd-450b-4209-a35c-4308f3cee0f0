<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\BranchService;
use App\Services\UserService;
use App\Services\AppointmentService;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function __construct(
        private BranchService $branchService,
        private UserService $userService,
        private AppointmentService $appointmentService
    ) {
        // Middleware is handled in routes
    }

    public function index()
    {
        $branches = $this->branchService->getAllBranches();
        $todaysAppointments = $this->appointmentService->getTodaysAppointments();
        $upcomingAppointments = $this->appointmentService->getUpcomingAppointments()->take(10);

        $stats = [
            'total_branches' => $branches->count(),
            'active_branches' => $branches->where('is_active', true)->count(),
            'total_doctors' => $branches->sum('doctor_profiles_count'),
            'total_patients' => $branches->sum('patient_profiles_count'),
            'todays_appointments' => $todaysAppointments->count(),
            'completed_today' => $todaysAppointments->where('status', 'completed')->count(),
            'pending_appointments' => $todaysAppointments->whereIn('status', ['scheduled', 'confirmed'])->count(),
        ];

        $recentActivity = [
            'branches' => $branches->sortByDesc('created_at')->take(5),
            'appointments' => $todaysAppointments->sortByDesc('created_at')->take(10),
        ];

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentActivity' => $recentActivity,
            'upcomingAppointments' => $upcomingAppointments,
            'branchStats' => $branches->map(function ($branch) {
                return [
                    'name' => $branch->name,
                    'doctors' => $branch->doctor_profiles_count,
                    'patients' => $branch->patient_profiles_count,
                    'appointments' => $branch->appointments_count,
                ];
            })
        ]);
    }
}