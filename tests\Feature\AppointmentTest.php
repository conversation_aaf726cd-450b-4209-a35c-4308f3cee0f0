<?php

use App\Models\User;
use App\Models\Branch;
use App\Models\Appointment;
use App\Models\DoctorProfile;
use App\Models\PatientProfile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Role;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create roles
    Role::create(['name' => 'admin']);
    Role::create(['name' => 'doctor']);
    Role::create(['name' => 'patient']);
    Role::create(['name' => 'branch_head']);
});

test('patient can create appointment', function () {
    $branch = Branch::factory()->create();
    
    $doctor = User::factory()->create();
    $doctor->assignRole('doctor');
    DoctorProfile::factory()->create([
        'user_id' => $doctor->id,
        'branch_id' => $branch->id,
    ]);

    $patient = User::factory()->create();
    $patient->assignRole('patient');
    PatientProfile::factory()->create([
        'user_id' => $patient->id,
        'branch_id' => $branch->id,
    ]);

    $response = $this->actingAs($patient)->post('/appointments', [
        'doctor_id' => $doctor->id,
        'appointment_date' => now()->addDay()->format('Y-m-d H:i:s'),
        'duration_minutes' => 60,
        'symptoms' => 'Test symptoms',
        'fee' => 100.00,
    ]);

    $response->assertRedirect();
    $this->assertDatabaseHas('appointments', [
        'patient_id' => $patient->id,
        'doctor_id' => $doctor->id,
        'branch_id' => $branch->id,
    ]);
});

test('appointment cannot be created in the past', function () {
    $branch = Branch::factory()->create();
    
    $doctor = User::factory()->create();
    $doctor->assignRole('doctor');
    DoctorProfile::factory()->create([
        'user_id' => $doctor->id,
        'branch_id' => $branch->id,
    ]);

    $patient = User::factory()->create();
    $patient->assignRole('patient');
    PatientProfile::factory()->create([
        'user_id' => $patient->id,
        'branch_id' => $branch->id,
    ]);

    $response = $this->actingAs($patient)->post('/appointments', [
        'doctor_id' => $doctor->id,
        'appointment_date' => now()->subDay()->format('Y-m-d H:i:s'),
        'duration_minutes' => 60,
        'symptoms' => 'Test symptoms',
        'fee' => 100.00,
    ]);

    $response->assertSessionHasErrors(['appointment_date']);
});

test('only authorized users can view appointments', function () {
    $appointment = Appointment::factory()->create();
    
    $unauthorizedUser = User::factory()->create();
    $unauthorizedUser->assignRole('patient');

    $response = $this->actingAs($unauthorizedUser)->get("/appointments/{$appointment->id}");
    
    $response->assertStatus(403);
});
