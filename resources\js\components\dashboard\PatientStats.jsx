import StatsCard from '@/Components/ui/StatsCard';
import { Calendar, Clock, CheckCircle, X } from 'lucide-react';

export default function PatientStats({ stats }) {
    return (
        <>
            <StatsCard 
                title="Total Appointments"
                icon={Calendar}
                value={stats.total_appointments}
                subtitle="All time"
            />
            
            <StatsCard 
                title="Upcoming"
                icon={Clock}
                value={stats.upcoming_appointments}
                subtitle="Scheduled sessions"
            />
            
            <StatsCard 
                title="Completed"
                icon={CheckCircle}
                value={stats.completed_appointments}
                subtitle="Sessions attended"
            />
            
            <StatsCard 
                title="Cancelled"
                icon={X}
                value={stats.cancelled_appointments}
                subtitle="Cancelled sessions"
            />
        </>
    );
}