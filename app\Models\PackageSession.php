<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PackageSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_package_id',
        'appointment_id',
        'session_number',
        'session_date',
        'session_time',
        'duration_minutes',
        'status',
        'session_notes',
        'homework_assigned',
        'progress_notes',
        'session_fee',
        'conducted_by',
        'completed_at',
    ];

    protected $casts = [
        'session_date' => 'date',
        'session_time' => 'datetime:H:i',
        'session_fee' => 'decimal:2',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Relationships
    public function patientPackage(): BelongsTo
    {
        return $this->belongsTo(PatientPackage::class);
    }

    public function appointment(): BelongsTo
    {
        return $this->belongsTo(Appointment::class);
    }

    public function conductedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'conducted_by');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('status', 'scheduled')
            ->where('session_date', '>=', now()->toDateString());
    }

    public function scopeForDoctor($query, $doctorId)
    {
        return $query->where('conducted_by', $doctorId);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('session_date', $date);
    }

    // Accessors
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'scheduled' => 'Scheduled',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'no_show' => 'No Show',
            'rescheduled' => 'Rescheduled',
            default => ucfirst($this->status),
        };
    }

    public function getFormattedSessionFeeAttribute(): string
    {
        return $this->session_fee ? '$' . number_format($this->session_fee, 2) : 'N/A';
    }

    public function getSessionDateTimeAttribute(): string
    {
        return $this->session_date->format('M d, Y') . ' at ' . $this->session_time->format('H:i');
    }

    // Methods
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    public function canBeCompleted(): bool
    {
        return $this->isScheduled() && $this->session_date <= now()->toDateString();
    }

    public function markAsCompleted($conductedBy = null, $notes = null): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'conducted_by' => $conductedBy ?? auth()->id(),
            'session_notes' => $notes ?? $this->session_notes,
        ]);

        // Update the patient package
        $this->patientPackage->completeSession();
    }

    public function markAsNoShow(): void
    {
        $this->update(['status' => 'no_show']);
    }

    public function reschedule($newDate, $newTime): void
    {
        $this->update([
            'session_date' => $newDate,
            'session_time' => $newTime,
            'status' => 'scheduled',
        ]);
    }

    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }
}
