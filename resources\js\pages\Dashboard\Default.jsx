import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { AlertCircle, User } from 'lucide-react';

export default function DefaultDashboard({ auth }) {
    return (
        <AuthenticatedLayout
            user={auth.user}
            header={<h2 className="font-semibold text-xl text-gray-800 leading-tight">Dashboard</h2>}
        >
            <Head title="Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <User className="mr-2 h-5 w-5" />
                                Welcome, {auth.user.name}
                            </CardTitle>
                            <CardDescription>
                                Your account is set up, but no specific role has been assigned yet.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-start space-x-4 p-4 border border-orange-200 bg-orange-50 rounded-lg">
                                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                                <div className="flex-1">
                                    <h3 className="text-sm font-medium text-orange-800">
                                        Account Setup Required
                                    </h3>
                                    <p className="mt-1 text-sm text-orange-700">
                                        Your account doesn't have a specific role assigned yet. Please contact your administrator to assign you the appropriate role (Admin, Branch Head, Doctor, or Patient).
                                    </p>
                                    <div className="mt-4">
                                        <Button 
                                            variant="outline" 
                                            size="sm"
                                            onClick={() => window.location.href = '/profile'}
                                        >
                                            View Profile
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Account Information</CardTitle>
                            <CardDescription>Your current account details</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Name:</span>
                                    <span className="text-sm font-medium">{auth.user.name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Email:</span>
                                    <span className="text-sm font-medium">{auth.user.email}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Status:</span>
                                    <span className="text-sm font-medium">
                                        {auth.user.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Roles:</span>
                                    <span className="text-sm font-medium">
                                        {auth.user.roles?.length > 0 
                                            ? auth.user.roles.map(role => role.name).join(', ')
                                            : 'No roles assigned'
                                        }
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}