<?php

namespace App\Contracts;

use App\Models\Appointment;
use Illuminate\Pagination\LengthAwarePaginator;

interface AppointmentRepositoryInterface
{
    public function all();
    public function paginate(int $perPage = 15): LengthAwarePaginator;
    public function find(int $id): ?Appointment;
    public function create(array $data): Appointment;
    public function update(int $id, array $data): Appointment;
    public function delete(int $id): bool;
    public function getByDoctor(int $doctorId);
    public function getByPatient(int $patientId);
    public function getByBranch(int $branchId);
    public function getTodaysAppointments();
    public function getUpcomingAppointments();
    public function getByStatus(string $status);
    public function getByDateRange(\Carbon\Carbon $start, \Carbon\Carbon $end);
    public function checkConflict(int $doctorId, \Carbon\Carbon $dateTime, int $duration);
    public function checkConflictExcluding(int $doctorId, \Carbon\Carbon $dateTime, int $duration, int $excludeAppointmentId);
    public function getForUser($user = null);
}