import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { ArrowLeft, Users, Save, Stethoscope, Heart, UserCheck } from 'lucide-react';
import { useState } from 'react';

export default function UserCreate({ role = 'patient', branches, canChooseRole = false }) {
    const [selectedRole, setSelectedRole] = useState(role);
    
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        email: '',
        password: '',
        phone: '',
        is_active: true,
        role: selectedRole,
        branch_id: '',
        
        // Doctor-specific fields
        specialization: '',
        license_number: '',
        qualifications: '',
        experience_years: '',
        doctor_phone: '',
        bio: '',
        consultation_fee: '',
        available_days: [],
        start_time: '09:00',
        end_time: '17:00',
        
        // Patient-specific fields
        date_of_birth: '',
        gender: '',
        patient_phone: '',
        address: '',
        emergency_contact_name: '',
        emergency_contact_phone: '',
        medical_history: '',
        current_medications: '',
        allergies: '',
        insurance_provider: '',
        insurance_number: ''
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('users.store'));
    };

    const handleRoleChange = (newRole) => {
        setSelectedRole(newRole);
        setData('role', newRole);
    };

    const handleAvailableDaysChange = (day) => {
        const currentDays = data.available_days || [];
        const updatedDays = currentDays.includes(day)
            ? currentDays.filter(d => d !== day)
            : [...currentDays, day];
        setData('available_days', updatedDays);
    };

    const getRoleIcon = (role) => {
        switch (role) {
            case 'doctor': return <Stethoscope className="h-5 w-5" />;
            case 'patient': return <Heart className="h-5 w-5" />;
            case 'branch_head': return <UserCheck className="h-5 w-5" />;
            default: return <Users className="h-5 w-5" />;
        }
    };

    const getRoleTitle = (role) => {
        switch (role) {
            case 'doctor': return 'Doctor';
            case 'patient': return 'Patient';
            case 'branch_head': return 'Branch Head';
            default: return 'User';
        }
    };

    const weekDays = [
        { value: 'monday', label: 'Monday' },
        { value: 'tuesday', label: 'Tuesday' },
        { value: 'wednesday', label: 'Wednesday' },
        { value: 'thursday', label: 'Thursday' },
        { value: 'friday', label: 'Friday' },
        { value: 'saturday', label: 'Saturday' },
        { value: 'sunday', label: 'Sunday' }
    ];

    return (
        <AppLayout>
            <Head title={`Create ${getRoleTitle(selectedRole)}`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex items-center gap-4">
                        <Link href={route('users.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div className="flex items-center gap-2">
                            {getRoleIcon(selectedRole)}
                            <div>
                                <h1 className="text-2xl font-bold">Create New {getRoleTitle(selectedRole)}</h1>
                                <p className="text-gray-600">Add a new user to the system</p>
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit}>
                        {/* Role Selection */}
                        {canChooseRole && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>User Role</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        {['doctor', 'patient', 'branch_head'].map((roleOption) => (
                                            <div
                                                key={roleOption}
                                                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                                                    selectedRole === roleOption
                                                        ? 'border-blue-500 bg-blue-50'
                                                        : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                                onClick={() => handleRoleChange(roleOption)}
                                            >
                                                <div className="flex items-center gap-2 mb-2">
                                                    {getRoleIcon(roleOption)}
                                                    <h3 className="font-medium">{getRoleTitle(roleOption)}</h3>
                                                </div>
                                                <p className="text-sm text-gray-600">
                                                    {roleOption === 'doctor' && 'Healthcare provider with specialized skills'}
                                                    {roleOption === 'patient' && 'Person receiving medical care'}
                                                    {roleOption === 'branch_head' && 'Manager of a therapy branch'}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Basic Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="name">Full Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="John Doe"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors.email ? 'border-red-500' : ''}
                                        />
                                        {errors.email && <p className="text-sm text-red-600 mt-1">{errors.email}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="password">Password *</Label>
                                        <Input
                                            id="password"
                                            type="password"
                                            value={data.password}
                                            onChange={(e) => setData('password', e.target.value)}
                                            placeholder="••••••••"
                                            className={errors.password ? 'border-red-500' : ''}
                                        />
                                        {errors.password && <p className="text-sm text-red-600 mt-1">{errors.password}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="phone">Phone Number</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+****************"
                                            className={errors.phone ? 'border-red-500' : ''}
                                        />
                                        {errors.phone && <p className="text-sm text-red-600 mt-1">{errors.phone}</p>}
                                    </div>

                                    <div>
                                        <Label>Branch *</Label>
                                        <Select
                                            value={data.branch_id}
                                            onValueChange={(value) => setData('branch_id', value)}
                                        >
                                            <SelectTrigger className={errors.branch_id ? 'border-red-500' : ''}>
                                                <SelectValue placeholder="Select a branch..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {branches?.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id.toString()}>
                                                        {branch.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.branch_id && <p className="text-sm text-red-600 mt-1">{errors.branch_id}</p>}
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="is_active"
                                            checked={data.is_active}
                                            onChange={(e) => setData('is_active', e.target.checked)}
                                        />
                                        <Label htmlFor="is_active">Active User</Label>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Role-specific Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>{getRoleTitle(selectedRole)} Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    
                                    {/* Doctor-specific fields */}
                                    {selectedRole === 'doctor' && (
                                        <>
                                            <div>
                                                <Label htmlFor="specialization">Specialization *</Label>
                                                <Input
                                                    id="specialization"
                                                    value={data.specialization}
                                                    onChange={(e) => setData('specialization', e.target.value)}
                                                    placeholder="Clinical Psychology"
                                                    className={errors.specialization ? 'border-red-500' : ''}
                                                />
                                                {errors.specialization && <p className="text-sm text-red-600 mt-1">{errors.specialization}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="license_number">License Number *</Label>
                                                <Input
                                                    id="license_number"
                                                    value={data.license_number}
                                                    onChange={(e) => setData('license_number', e.target.value)}
                                                    placeholder="LIC123456789"
                                                    className={errors.license_number ? 'border-red-500' : ''}
                                                />
                                                {errors.license_number && <p className="text-sm text-red-600 mt-1">{errors.license_number}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="qualifications">Qualifications *</Label>
                                                <Textarea
                                                    id="qualifications"
                                                    value={data.qualifications}
                                                    onChange={(e) => setData('qualifications', e.target.value)}
                                                    placeholder="PhD in Clinical Psychology, Board Certified..."
                                                    className={errors.qualifications ? 'border-red-500' : ''}
                                                />
                                                {errors.qualifications && <p className="text-sm text-red-600 mt-1">{errors.qualifications}</p>}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="experience_years">Experience (Years) *</Label>
                                                    <Input
                                                        id="experience_years"
                                                        type="number"
                                                        min="0"
                                                        max="50"
                                                        value={data.experience_years}
                                                        onChange={(e) => setData('experience_years', e.target.value)}
                                                        className={errors.experience_years ? 'border-red-500' : ''}
                                                    />
                                                    {errors.experience_years && <p className="text-sm text-red-600 mt-1">{errors.experience_years}</p>}
                                                </div>

                                                <div>
                                                    <Label htmlFor="consultation_fee">Consultation Fee *</Label>
                                                    <Input
                                                        id="consultation_fee"
                                                        type="number"
                                                        min="0"
                                                        step="0.01"
                                                        value={data.consultation_fee}
                                                        onChange={(e) => setData('consultation_fee', e.target.value)}
                                                        placeholder="150.00"
                                                        className={errors.consultation_fee ? 'border-red-500' : ''}
                                                    />
                                                    {errors.consultation_fee && <p className="text-sm text-red-600 mt-1">{errors.consultation_fee}</p>}
                                                </div>
                                            </div>

                                            <div>
                                                <Label>Available Days *</Label>
                                                <div className="grid grid-cols-4 gap-2 mt-2">
                                                    {weekDays.map((day) => (
                                                        <label key={day.value} className="flex items-center space-x-2">
                                                            <input
                                                                type="checkbox"
                                                                checked={data.available_days?.includes(day.value) || false}
                                                                onChange={() => handleAvailableDaysChange(day.value)}
                                                            />
                                                            <span className="text-sm">{day.label}</span>
                                                        </label>
                                                    ))}
                                                </div>
                                                {errors.available_days && <p className="text-sm text-red-600 mt-1">{errors.available_days}</p>}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="start_time">Start Time *</Label>
                                                    <Input
                                                        id="start_time"
                                                        type="time"
                                                        value={data.start_time}
                                                        onChange={(e) => setData('start_time', e.target.value)}
                                                        className={errors.start_time ? 'border-red-500' : ''}
                                                    />
                                                    {errors.start_time && <p className="text-sm text-red-600 mt-1">{errors.start_time}</p>}
                                                </div>

                                                <div>
                                                    <Label htmlFor="end_time">End Time *</Label>
                                                    <Input
                                                        id="end_time"
                                                        type="time"
                                                        value={data.end_time}
                                                        onChange={(e) => setData('end_time', e.target.value)}
                                                        className={errors.end_time ? 'border-red-500' : ''}
                                                    />
                                                    {errors.end_time && <p className="text-sm text-red-600 mt-1">{errors.end_time}</p>}
                                                </div>
                                            </div>

                                            <div>
                                                <Label htmlFor="bio">Bio</Label>
                                                <Textarea
                                                    id="bio"
                                                    value={data.bio}
                                                    onChange={(e) => setData('bio', e.target.value)}
                                                    placeholder="Brief professional bio..."
                                                    className={errors.bio ? 'border-red-500' : ''}
                                                />
                                                {errors.bio && <p className="text-sm text-red-600 mt-1">{errors.bio}</p>}
                                            </div>
                                        </>
                                    )}

                                    {/* Patient-specific fields */}
                                    {selectedRole === 'patient' && (
                                        <>
                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="date_of_birth">Date of Birth *</Label>
                                                    <Input
                                                        id="date_of_birth"
                                                        type="date"
                                                        value={data.date_of_birth}
                                                        onChange={(e) => setData('date_of_birth', e.target.value)}
                                                        className={errors.date_of_birth ? 'border-red-500' : ''}
                                                    />
                                                    {errors.date_of_birth && <p className="text-sm text-red-600 mt-1">{errors.date_of_birth}</p>}
                                                </div>

                                                <div>
                                                    <Label>Gender *</Label>
                                                    <Select
                                                        value={data.gender}
                                                        onValueChange={(value) => setData('gender', value)}
                                                    >
                                                        <SelectTrigger className={errors.gender ? 'border-red-500' : ''}>
                                                            <SelectValue placeholder="Select gender..." />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem value="male">Male</SelectItem>
                                                            <SelectItem value="female">Female</SelectItem>
                                                            <SelectItem value="other">Other</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    {errors.gender && <p className="text-sm text-red-600 mt-1">{errors.gender}</p>}
                                                </div>
                                            </div>

                                            <div>
                                                <Label htmlFor="address">Address *</Label>
                                                <Textarea
                                                    id="address"
                                                    value={data.address}
                                                    onChange={(e) => setData('address', e.target.value)}
                                                    placeholder="Full address..."
                                                    className={errors.address ? 'border-red-500' : ''}
                                                />
                                                {errors.address && <p className="text-sm text-red-600 mt-1">{errors.address}</p>}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="emergency_contact_name">Emergency Contact Name *</Label>
                                                    <Input
                                                        id="emergency_contact_name"
                                                        value={data.emergency_contact_name}
                                                        onChange={(e) => setData('emergency_contact_name', e.target.value)}
                                                        placeholder="Contact person name"
                                                        className={errors.emergency_contact_name ? 'border-red-500' : ''}
                                                    />
                                                    {errors.emergency_contact_name && <p className="text-sm text-red-600 mt-1">{errors.emergency_contact_name}</p>}
                                                </div>

                                                <div>
                                                    <Label htmlFor="emergency_contact_phone">Emergency Contact Phone *</Label>
                                                    <Input
                                                        id="emergency_contact_phone"
                                                        value={data.emergency_contact_phone}
                                                        onChange={(e) => setData('emergency_contact_phone', e.target.value)}
                                                        placeholder="+****************"
                                                        className={errors.emergency_contact_phone ? 'border-red-500' : ''}
                                                    />
                                                    {errors.emergency_contact_phone && <p className="text-sm text-red-600 mt-1">{errors.emergency_contact_phone}</p>}
                                                </div>
                                            </div>

                                            <div>
                                                <Label htmlFor="medical_history">Medical History</Label>
                                                <Textarea
                                                    id="medical_history"
                                                    value={data.medical_history}
                                                    onChange={(e) => setData('medical_history', e.target.value)}
                                                    placeholder="Previous medical conditions, surgeries, etc."
                                                    className={errors.medical_history ? 'border-red-500' : ''}
                                                />
                                                {errors.medical_history && <p className="text-sm text-red-600 mt-1">{errors.medical_history}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="current_medications">Current Medications</Label>
                                                <Textarea
                                                    id="current_medications"
                                                    value={data.current_medications}
                                                    onChange={(e) => setData('current_medications', e.target.value)}
                                                    placeholder="List of current medications..."
                                                    className={errors.current_medications ? 'border-red-500' : ''}
                                                />
                                                {errors.current_medications && <p className="text-sm text-red-600 mt-1">{errors.current_medications}</p>}
                                            </div>

                                            <div>
                                                <Label htmlFor="allergies">Allergies</Label>
                                                <Textarea
                                                    id="allergies"
                                                    value={data.allergies}
                                                    onChange={(e) => setData('allergies', e.target.value)}
                                                    placeholder="Known allergies and reactions..."
                                                    className={errors.allergies ? 'border-red-500' : ''}
                                                />
                                                {errors.allergies && <p className="text-sm text-red-600 mt-1">{errors.allergies}</p>}
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <div>
                                                    <Label htmlFor="insurance_provider">Insurance Provider</Label>
                                                    <Input
                                                        id="insurance_provider"
                                                        value={data.insurance_provider}
                                                        onChange={(e) => setData('insurance_provider', e.target.value)}
                                                        placeholder="Insurance company name"
                                                        className={errors.insurance_provider ? 'border-red-500' : ''}
                                                    />
                                                    {errors.insurance_provider && <p className="text-sm text-red-600 mt-1">{errors.insurance_provider}</p>}
                                                </div>

                                                <div>
                                                    <Label htmlFor="insurance_number">Insurance Number</Label>
                                                    <Input
                                                        id="insurance_number"
                                                        value={data.insurance_number}
                                                        onChange={(e) => setData('insurance_number', e.target.value)}
                                                        placeholder="Policy number"
                                                        className={errors.insurance_number ? 'border-red-500' : ''}
                                                    />
                                                    {errors.insurance_number && <p className="text-sm text-red-600 mt-1">{errors.insurance_number}</p>}
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {/* Branch Head has no additional fields beyond basic info */}
                                    {selectedRole === 'branch_head' && (
                                        <div className="text-center py-8">
                                            <UserCheck className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                                            <p className="text-muted-foreground">
                                                Branch heads only require basic information. Additional branch assignment will be done separately.
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end gap-4 mt-6">
                            <Link href={route('users.index')}>
                                <Button variant="outline">Cancel</Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Creating...' : `Create ${getRoleTitle(selectedRole)}`}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}