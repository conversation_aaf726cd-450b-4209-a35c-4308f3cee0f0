<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TherapyPackageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasAnyRole(['admin', 'branch_head']);
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:1000'],
            'details' => ['nullable', 'string', 'max:5000'],
            'total_sessions' => ['required', 'integer', 'min:1', 'max:100'],
            'price' => ['required', 'numeric', 'min:0', 'max:50000'],
            'session_duration_minutes' => ['required', 'integer', 'min:15', 'max:480'],
            'package_type' => ['required', 'string', 'in:individual,group,family,couples'],
            'objectives' => ['nullable', 'string', 'max:2000'],
            'target_audience' => ['nullable', 'string', 'max:1000'],
            'included_services' => ['nullable', 'array'],
            'included_services.*' => ['string', 'in:consultation,assessment,therapy_sessions,progress_reports,homework_assignments,family_sessions'],
            'validity_days' => ['required', 'integer', 'min:30', 'max:1095'], // 30 days to 3 years
            'is_active' => ['boolean'],
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'Package title is required',
            'title.max' => 'Package title cannot exceed 255 characters',
            'description.required' => 'Package description is required',
            'description.max' => 'Package description cannot exceed 1000 characters',
            'total_sessions.required' => 'Total number of sessions is required',
            'total_sessions.min' => 'Package must have at least 1 session',
            'total_sessions.max' => 'Package cannot have more than 100 sessions',
            'price.required' => 'Package price is required',
            'price.min' => 'Package price cannot be negative',
            'price.max' => 'Package price cannot exceed $50,000',
            'session_duration_minutes.required' => 'Session duration is required',
            'session_duration_minutes.min' => 'Session duration must be at least 15 minutes',
            'session_duration_minutes.max' => 'Session duration cannot exceed 8 hours',
            'package_type.required' => 'Package type is required',
            'package_type.in' => 'Invalid package type selected',
            'validity_days.required' => 'Package validity period is required',
            'validity_days.min' => 'Package validity must be at least 30 days',
            'validity_days.max' => 'Package validity cannot exceed 3 years',
            'included_services.*.in' => 'Invalid service selected',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Validate price per session is reasonable
            if ($this->total_sessions && $this->price) {
                $pricePerSession = $this->price / $this->total_sessions;
                if ($pricePerSession < 10) {
                    $validator->errors()->add('price', 'Price per session seems too low (less than $10)');
                }
                if ($pricePerSession > 1000) {
                    $validator->errors()->add('price', 'Price per session seems too high (more than $1000)');
                }
            }

            // Validate session duration for group therapy
            if ($this->package_type === 'group' && $this->session_duration_minutes < 60) {
                $validator->errors()->add('session_duration_minutes', 'Group therapy sessions should be at least 60 minutes');
            }

            // Validate total sessions for different package types
            if ($this->package_type === 'individual' && $this->total_sessions > 50) {
                $validator->errors()->add('total_sessions', 'Individual therapy packages typically have 50 sessions or fewer');
            }

            if ($this->package_type === 'group' && $this->total_sessions > 20) {
                $validator->errors()->add('total_sessions', 'Group therapy packages typically have 20 sessions or fewer');
            }
        });
    }
}
