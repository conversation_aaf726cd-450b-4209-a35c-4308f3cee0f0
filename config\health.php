<?php

use <PERSON>tie\Health\Checks\Checks\CacheCheck;
use Spatie\Health\Checks\Checks\DatabaseCheck;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
use Spatie\Health\Checks\Checks\OptimizedAppCheck;
use Spatie\Health\Checks\Checks\QueueCheck;
use Spatie\Health\Checks\Checks\ScheduleCheck;
use Spatie\Health\Checks\Checks\UsedDiskSpaceCheck;

return [
    /*
    |--------------------------------------------------------------------------
    | Health Checks
    |--------------------------------------------------------------------------
    |
    | Here you can configure which health checks should be run to determine
    | if your application is healthy. These checks will be run when you
    | visit the health check endpoint.
    |
    */

    'checks' => [
        CacheCheck::new(),
        DatabaseCheck::new(),
        DebugModeCheck::new()
            ->if(app()->environment('production')),
        EnvironmentCheck::new()
            ->expectEnvironment('production')
            ->if(app()->environment('production')),
        OptimizedAppCheck::new()
            ->if(app()->environment('production')),
        QueueCheck::new()
            ->onQueue('default'),
        ScheduleCheck::new()
            ->heartbeatMaxAgeInMinutes(2),
        UsedDiskSpaceCheck::new()
            ->warnWhenUsedSpaceIsAbovePercentage(70)
            ->failWhenUsedSpaceIsAbovePercentage(90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Here you can configure how you want to be notified when the health
    | of your application changes.
    |
    */

    'notifications' => [
        'enabled' => env('HEALTH_NOTIFICATIONS_ENABLED', true),

        'notifications' => [
            // Add your notification channels here
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Result Store
    |--------------------------------------------------------------------------
    |
    | Here you can configure where the results of the health checks will
    | be stored. By default, they are stored in the cache.
    |
    */

    'result_stores' => [
        Spatie\Health\ResultStores\CacheHealthResultStore::class => [
            'store' => null,
        ],
    ],
];
