<?php

namespace App\Http\Controllers;

use App\Http\Requests\BranchRequest;
use App\Services\BranchService;
use App\Services\UserService;
use Inertia\Inertia;

class BranchController extends Controller
{
    public function __construct(
        private BranchService $branchService,
        private UserService $userService
    ) {}

    public function index()
    {
        $user = auth()->user();
        
        // Admin sees all branches, Branch Head sees only their branch
        if ($user->hasRole('admin')) {
            $branches = $this->branchService->getAllBranches();
            $canManageAll = true;
        } else {
            $branches = collect([$user->managedBranch])->filter();
            $canManageAll = false;
        }
        
        return Inertia::render('Branches/Index', [
            'branches' => $branches,
            'canManageAll' => $canManageAll,
            'stats' => [
                'total_branches' => $branches->count(),
                'active_branches' => $branches->where('is_active', true)->count(),
                'total_doctors' => $branches->sum('doctor_profiles_count'),
                'total_patients' => $branches->sum('patient_profiles_count'),
            ]
        ]);
    }

    public function create()
    {
        // Only admins can create branches
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $allBranchHeads = $this->userService->getUsersByRole('branch_head');
        $availableBranchHeads = $allBranchHeads->filter(function ($user) {
            return !$user->managedBranch;
        });

        return Inertia::render('Branches/Create', [
            'availableBranchHeads' => $availableBranchHeads->values()
        ]);
    }

    public function store(BranchRequest $request)
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        try {
            $branch = $this->branchService->createBranch($request->validated());
            
            return redirect()->route('branches.index')
                ->with('success', 'Branch created successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $user = auth()->user();
        $branch = $this->branchService->getBranchById($id);
        
        if (!$branch) {
            return redirect()->route('branches.index')
                ->withErrors(['error' => 'Branch not found']);
        }

        // Branch heads can only see their own branch
        if ($user->hasRole('branch_head') && $user->managedBranch->id !== $branch->id) {
            abort(403, 'Unauthorized');
        }

        $doctors = $this->userService->getDoctorsByBranch($id);
        $patients = $this->userService->getPatientsByBranch($id);

        return Inertia::render('Branches/Show', [
            'branch' => $branch,
            'doctors' => $doctors,
            'patients' => $patients,
            'canEdit' => $user->hasRole('admin'),
            'stats' => [
                'total_doctors' => $doctors->count(),
                'total_patients' => $patients->count(),
                'active_doctors' => $doctors->where('doctorProfile.is_active', true)->count(),
                'active_patients' => $patients->where('patientProfile.is_active', true)->count(),
            ]
        ]);
    }

    public function edit($id)
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $branch = $this->branchService->getBranchById($id);
        
        if (!$branch) {
            return redirect()->route('branches.index')
                ->withErrors(['error' => 'Branch not found']);
        }

        $allBranchHeads = $this->userService->getUsersByRole('branch_head');
        $availableBranchHeads = $allBranchHeads->filter(function ($user) use ($branch) {
            return !$user->managedBranch || $user->id === $branch->branch_head_id;
        });

        return Inertia::render('Branches/Edit', [
            'branch' => $branch,
            'availableBranchHeads' => $availableBranchHeads->values()
        ]);
    }

    public function update(BranchRequest $request, $id)
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        try {
            $branch = $this->branchService->updateBranch($id, $request->validated());
            
            return redirect()->route('branches.index')
                ->with('success', 'Branch updated successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        try {
            $this->branchService->deleteBranch($id);
            
            return redirect()->route('branches.index')
                ->with('success', 'Branch deleted successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function stats($id)
    {
        $user = auth()->user();
        $branch = $this->branchService->getBranchById($id);
        
        if (!$branch) {
            return response()->json(['error' => 'Branch not found'], 404);
        }

        // Branch heads can only see their own branch stats
        if ($user->hasRole('branch_head') && $user->managedBranch->id !== $branch->id) {
            abort(403, 'Unauthorized');
        }

        $doctors = $this->userService->getDoctorsByBranch($id);
        $patients = $this->userService->getPatientsByBranch($id);

        return response()->json([
            'doctors' => $doctors->count(),
            'patients' => $patients->count(),
            'active_doctors' => $doctors->where('doctorProfile.is_active', true)->count(),
            'active_patients' => $patients->where('patientProfile.is_active', true)->count(),
        ]);
    }

    public function getStats()
    {
        $user = auth()->user();
        
        if ($user->hasRole('admin')) {
            $branches = $this->branchService->getAllBranches();
        } else {
            $branches = collect([$user->managedBranch])->filter();
        }

        return response()->json([
            'total_branches' => $branches->count(),
            'active_branches' => $branches->where('is_active', true)->count(),
            'total_doctors' => $branches->sum('doctor_profiles_count'),
            'total_patients' => $branches->sum('patient_profiles_count'),
        ]);
    }
}