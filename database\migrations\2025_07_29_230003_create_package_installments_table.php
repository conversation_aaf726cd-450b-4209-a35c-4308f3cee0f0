<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('package_installments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_package_id')->constrained()->onDelete('cascade');
            $table->integer('installment_number'); // 1, 2, 3, etc.
            $table->date('installment_date'); // Date when installment was created/scheduled
            $table->decimal('installment_amount', 10, 2);
            $table->date('due_date');
            $table->date('paid_date')->nullable();
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->enum('status', ['pending', 'paid', 'overdue', 'partial'])->default('pending');
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'cheque', 'online'])->nullable();
            $table->string('transaction_reference')->nullable();
            $table->text('payment_notes')->nullable();
            $table->foreignId('received_by')->nullable()->constrained('users')->onDelete('set null'); // Who received the payment
            $table->timestamps();

            $table->unique(['patient_package_id', 'installment_number']);
            $table->index(['patient_package_id', 'status']);
            $table->index(['due_date', 'status']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('package_installments');
    }
};
