<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MedicalRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'patient_id',
        'doctor_id',
        'appointment_id',
        'record_type',
        'title',
        'description',
        'findings',
        'recommendations',
        'attachments',
        'record_date',
    ];

    protected function casts(): array
    {
        return [
            'attachments' => 'array',
            'record_date' => 'date',
        ];
    }

    // Relationships
    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function appointment()
    {
        return $this->belongsTo(Appointment::class);
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('record_type', $type);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('record_date', '>=', now()->subDays($days));
    }

    // Helper methods
    public function hasAttachments()
    {
        return !empty($this->attachments);
    }

    public function getAttachmentCountAttribute()
    {
        return count($this->attachments ?? []);
    }
}