import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { ArrowLeft, User, Save, Shield } from 'lucide-react';

export default function UserEdit({ user, roles = [], branches = [] }) {
    const { auth } = usePage().props;
    const currentUser = auth?.user;
    const userRole = currentUser?.roles?.[0]?.name;

    const { data, setData, put, processing, errors } = useForm({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        role: user.roles?.[0]?.name || 'none',
        branch_id: user.branch_id?.toString() || 'none',
        is_active: user.is_active || true,
        
        // Doctor specific fields
        specialization: user.specialization || '',
        license_number: user.license_number || '',
        experience_years: user.experience_years || '',
        consultation_fee: user.consultation_fee || '',
        
        // Patient specific fields
        date_of_birth: user.date_of_birth || '',
        gender: user.gender || 'none',
        emergency_contact: user.emergency_contact || '',
        medical_history: user.medical_history || '',
        
        // Password fields (optional for editing)
        password: '',
        password_confirmation: ''
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        
        put(route('users.update', user.id), {
            transform: (data) => {
                const transformedData = {
                    ...data,
                    role: data.role === 'none' ? null : data.role,
                    branch_id: data.branch_id === 'none' ? null : data.branch_id,
                    gender: data.gender === 'none' ? null : data.gender
                };
                
                // Remove empty password fields
                if (!transformedData.password) {
                    delete transformedData.password;
                    delete transformedData.password_confirmation;
                }
                
                return transformedData;
            }
        });
    };

    const selectedRole = data.role;

    return (
        <AppLayout>
            <Head title={`Edit User: ${user.name}`} />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8 space-y-6">
                    
                    {/* Header */}
                    <div className="flex items-center gap-4">
                        <Link href={route('users.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Edit User: {user.name}</h1>
                            <p className="text-gray-600">Update user information and role</p>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit}>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            
                            {/* Basic Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Basic Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="name">Full Name *</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="John Doe"
                                            className={errors.name ? 'border-red-500' : ''}
                                        />
                                        {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="email">Email Address *</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                            className={errors.email ? 'border-red-500' : ''}
                                        />
                                        {errors.email && <p className="text-sm text-red-600 mt-1">{errors.email}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="phone">Phone Number</Label>
                                        <Input
                                            id="phone"
                                            value={data.phone}
                                            onChange={(e) => setData('phone', e.target.value)}
                                            placeholder="+****************"
                                            className={errors.phone ? 'border-red-500' : ''}
                                        />
                                        {errors.phone && <p className="text-sm text-red-600 mt-1">{errors.phone}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="address">Address</Label>
                                        <Textarea
                                            id="address"
                                            value={data.address}
                                            onChange={(e) => setData('address', e.target.value)}
                                            placeholder="123 Main Street, City, State"
                                            className={errors.address ? 'border-red-500' : ''}
                                        />
                                        {errors.address && <p className="text-sm text-red-600 mt-1">{errors.address}</p>}
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="is_active"
                                            checked={data.is_active}
                                            onChange={(e) => setData('is_active', e.target.checked)}
                                        />
                                        <Label htmlFor="is_active">Active User</Label>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Role & Access */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Shield className="h-5 w-5" />
                                        Role & Access
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label>User Role *</Label>
                                        <Select
                                            value={data.role}
                                            onValueChange={(value) => setData('role', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select user role..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">No Role</SelectItem>
                                                {roles.map((role) => (
                                                    <SelectItem key={role.name} value={role.name}>
                                                        {role.name.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.role && <p className="text-sm text-red-600 mt-1">{errors.role}</p>}
                                    </div>

                                    <div>
                                        <Label>Branch Assignment</Label>
                                        <Select
                                            value={data.branch_id}
                                            onValueChange={(value) => setData('branch_id', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select branch..." />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">No Branch</SelectItem>
                                                {branches.map((branch) => (
                                                    <SelectItem key={branch.id} value={branch.id.toString()}>
                                                        {branch.name} - {branch.city}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.branch_id && <p className="text-sm text-red-600 mt-1">{errors.branch_id}</p>}
                                    </div>

                                    {/* Password Update Section */}
                                    <div className="border-t pt-4">
                                        <h4 className="text-sm font-medium mb-3">Update Password (Optional)</h4>
                                        <div className="space-y-3">
                                            <div>
                                                <Label htmlFor="password">New Password</Label>
                                                <Input
                                                    id="password"
                                                    type="password"
                                                    value={data.password}
                                                    onChange={(e) => setData('password', e.target.value)}
                                                    placeholder="Leave blank to keep current password"
                                                    className={errors.password ? 'border-red-500' : ''}
                                                />
                                                {errors.password && <p className="text-sm text-red-600 mt-1">{errors.password}</p>}
                                            </div>
                                            <div>
                                                <Label htmlFor="password_confirmation">Confirm New Password</Label>
                                                <Input
                                                    id="password_confirmation"
                                                    type="password"
                                                    value={data.password_confirmation}
                                                    onChange={(e) => setData('password_confirmation', e.target.value)}
                                                    placeholder="Confirm new password"
                                                    className={errors.password_confirmation ? 'border-red-500' : ''}
                                                />
                                                {errors.password_confirmation && <p className="text-sm text-red-600 mt-1">{errors.password_confirmation}</p>}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Role-specific fields */}
                        {selectedRole === 'doctor' && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Doctor Information</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="specialization">Specialization</Label>
                                            <Input
                                                id="specialization"
                                                value={data.specialization}
                                                onChange={(e) => setData('specialization', e.target.value)}
                                                placeholder="e.g., Psychiatry, Therapy"
                                                className={errors.specialization ? 'border-red-500' : ''}
                                            />
                                            {errors.specialization && <p className="text-sm text-red-600 mt-1">{errors.specialization}</p>}
                                        </div>
                                        <div>
                                            <Label htmlFor="license_number">License Number</Label>
                                            <Input
                                                id="license_number"
                                                value={data.license_number}
                                                onChange={(e) => setData('license_number', e.target.value)}
                                                placeholder="Medical license number"
                                                className={errors.license_number ? 'border-red-500' : ''}
                                            />
                                            {errors.license_number && <p className="text-sm text-red-600 mt-1">{errors.license_number}</p>}
                                        </div>
                                        <div>
                                            <Label htmlFor="experience_years">Years of Experience</Label>
                                            <Input
                                                id="experience_years"
                                                type="number"
                                                value={data.experience_years}
                                                onChange={(e) => setData('experience_years', e.target.value)}
                                                placeholder="5"
                                                className={errors.experience_years ? 'border-red-500' : ''}
                                            />
                                            {errors.experience_years && <p className="text-sm text-red-600 mt-1">{errors.experience_years}</p>}
                                        </div>
                                        <div>
                                            <Label htmlFor="consultation_fee">Consultation Fee ($)</Label>
                                            <Input
                                                id="consultation_fee"
                                                type="number"
                                                step="0.01"
                                                value={data.consultation_fee}
                                                onChange={(e) => setData('consultation_fee', e.target.value)}
                                                placeholder="100.00"
                                                className={errors.consultation_fee ? 'border-red-500' : ''}
                                            />
                                            {errors.consultation_fee && <p className="text-sm text-red-600 mt-1">{errors.consultation_fee}</p>}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {selectedRole === 'patient' && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Patient Information</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="date_of_birth">Date of Birth</Label>
                                            <Input
                                                id="date_of_birth"
                                                type="date"
                                                value={data.date_of_birth}
                                                onChange={(e) => setData('date_of_birth', e.target.value)}
                                                className={errors.date_of_birth ? 'border-red-500' : ''}
                                            />
                                            {errors.date_of_birth && <p className="text-sm text-red-600 mt-1">{errors.date_of_birth}</p>}
                                        </div>
                                        <div>
                                            <Label>Gender</Label>
                                            <Select
                                                value={data.gender}
                                                onValueChange={(value) => setData('gender', value)}
                                            >
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select gender..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="none">Prefer not to say</SelectItem>
                                                    <SelectItem value="male">Male</SelectItem>
                                                    <SelectItem value="female">Female</SelectItem>
                                                    <SelectItem value="other">Other</SelectItem>
                                                </SelectContent>
                                            </Select>
                                            {errors.gender && <p className="text-sm text-red-600 mt-1">{errors.gender}</p>}
                                        </div>
                                        <div>
                                            <Label htmlFor="emergency_contact">Emergency Contact</Label>
                                            <Input
                                                id="emergency_contact"
                                                value={data.emergency_contact}
                                                onChange={(e) => setData('emergency_contact', e.target.value)}
                                                placeholder="Emergency contact name and phone"
                                                className={errors.emergency_contact ? 'border-red-500' : ''}
                                            />
                                            {errors.emergency_contact && <p className="text-sm text-red-600 mt-1">{errors.emergency_contact}</p>}
                                        </div>
                                        <div className="md:col-span-2">
                                            <Label htmlFor="medical_history">Medical History</Label>
                                            <Textarea
                                                id="medical_history"
                                                value={data.medical_history}
                                                onChange={(e) => setData('medical_history', e.target.value)}
                                                placeholder="Brief medical history and relevant conditions"
                                                className={errors.medical_history ? 'border-red-500' : ''}
                                            />
                                            {errors.medical_history && <p className="text-sm text-red-600 mt-1">{errors.medical_history}</p>}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Actions */}
                        <div className="flex justify-end gap-4 mt-6">
                            <Link href={route('users.show', user.id)}>
                                <Button variant="outline">Cancel</Button>
                            </Link>
                            <Button type="submit" disabled={processing}>
                                <Save className="mr-2 h-4 w-4" />
                                {processing ? 'Updating...' : 'Update User'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}