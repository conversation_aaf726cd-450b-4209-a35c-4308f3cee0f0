<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('therapy_packages', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->text('details')->nullable();
            $table->integer('total_sessions');
            $table->decimal('price', 10, 2);
            $table->integer('session_duration_minutes')->default(60);
            $table->enum('package_type', ['individual', 'group', 'family', 'couples'])->default('individual');
            $table->text('objectives')->nullable();
            $table->text('target_audience')->nullable();
            $table->json('included_services')->nullable(); // ['consultation', 'assessment', 'therapy_sessions', 'progress_reports']
            $table->integer('validity_days')->default(365); // Package validity in days
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['is_active', 'package_type']);
            $table->index('total_sessions');
            $table->index('price');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('therapy_packages');
    }
};
