<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('patient_packages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('patient_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('therapy_package_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_doctor_id')->nullable()->constrained('users')->onDelete('set null');
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('sessions_completed')->default(0);
            $table->integer('sessions_remaining');
            $table->enum('status', ['active', 'completed', 'cancelled', 'expired', 'on_hold'])->default('active');
            // Financial details
            $table->decimal('original_price', 10, 2); // Original package price
            $table->decimal('discount_given', 10, 2)->default(0); // Discount amount
            $table->decimal('total_amount_after_discount', 10, 2); // Final amount after discount
            $table->integer('number_of_installments')->default(1); // Payment installments
            $table->enum('payment_type', ['cash', 'card', 'bank_transfer', 'cheque', 'online', 'installments'])->default('cash');
            $table->decimal('amount_paid', 10, 2)->default(0);
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->text('payment_notes')->nullable(); // Payment-specific notes
            $table->text('notes')->nullable(); // General notes
            $table->timestamp('last_session_date')->nullable();
            $table->timestamps();
            
            $table->index(['patient_id', 'status']);
            $table->index(['therapy_package_id', 'status']);
            $table->index(['assigned_doctor_id', 'status']);
            $table->index(['start_date', 'end_date']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('patient_packages');
    }
};
