import Swal from 'sweetalert2';

// Custom SweetAlert2 configuration with therapy app theme
const swalConfig = {
    customClass: {
        popup: 'rounded-lg shadow-2xl',
        header: 'border-b border-gray-200 pb-4',
        title: 'text-xl font-semibold text-gray-800',
        content: 'text-gray-600',
        confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 mr-2',
        cancelButton: 'bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200',
        denyButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 mr-2'
    },
    buttonsStyling: false,
    showClass: {
        popup: 'animate__animated animate__fadeInDown animate__faster'
    },
    hideClass: {
        popup: 'animate__animated animate__fadeOutUp animate__faster'
    }
};

// Success Alert
export const showSuccess = (title, text = '', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'success',
        title,
        text,
        confirmButtonText: 'Great!',
        timer: 3000,
        timerProgressBar: true,
        ...options
    });
};

// Error Alert
export const showError = (title, text = '', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'error',
        title,
        text,
        confirmButtonText: 'Understood',
        ...options
    });
};

// Warning Alert
export const showWarning = (title, text = '', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'warning',
        title,
        text,
        confirmButtonText: 'OK',
        ...options
    });
};

// Info Alert
export const showInfo = (title, text = '', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'info',
        title,
        text,
        confirmButtonText: 'Got it',
        ...options
    });
};

// Confirmation Dialog
export const showConfirmation = (title, text = '', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'question',
        title,
        text,
        showCancelButton: true,
        confirmButtonText: 'Yes, proceed',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        ...options
    });
};

// Delete Confirmation
export const showDeleteConfirmation = (itemName = 'this item', options = {}) => {
    return Swal.fire({
        ...swalConfig,
        icon: 'warning',
        title: 'Are you sure?',
        text: `You won't be able to recover ${itemName} after deletion!`,
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        customClass: {
            ...swalConfig.customClass,
            confirmButton: 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 mr-2'
        },
        ...options
    }).then((result) => result.isConfirmed);
};

// Loading Alert
export const showLoading = (title = 'Processing...', text = 'Please wait') => {
    return Swal.fire({
        ...swalConfig,
        title,
        text,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
};

// Close Loading
export const closeLoading = () => {
    Swal.close();
};

// Custom Form Dialog
export const showFormDialog = (title, html, options = {}) => {
    return Swal.fire({
        ...swalConfig,
        title,
        html,
        showCancelButton: true,
        confirmButtonText: 'Submit',
        cancelButtonText: 'Cancel',
        reverseButtons: true,
        preConfirm: () => {
            // Custom validation can be added here
            return true;
        },
        ...options
    });
};

// Progress Dialog
export const showProgress = (title, steps = [], currentStep = 0) => {
    const progressHtml = `
        <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Step ${currentStep + 1} of ${steps.length}</span>
                <span>${Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                     style="width: ${((currentStep + 1) / steps.length) * 100}%"></div>
            </div>
            <div class="mt-3 text-center">
                <p class="font-medium">${steps[currentStep]}</p>
            </div>
        </div>
    `;

    return Swal.fire({
        ...swalConfig,
        title,
        html: progressHtml,
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
};

// Update Progress
export const updateProgress = (steps, currentStep) => {
    const progressHtml = `
        <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Step ${currentStep + 1} of ${steps.length}</span>
                <span>${Math.round(((currentStep + 1) / steps.length) * 100)}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                     style="width: ${((currentStep + 1) / steps.length) * 100}%"></div>
            </div>
            <div class="mt-3 text-center">
                <p class="font-medium">${steps[currentStep]}</p>
            </div>
        </div>
    `;
    
    Swal.update({
        html: progressHtml
    });
};

// Mixed Alert (for complex scenarios)
export const showMixed = (config) => {
    return Swal.fire({
        ...swalConfig,
        ...config
    });
};

// Toast-like notification using SweetAlert2
export const showToast = (title, icon = 'success', position = 'top-end') => {
    return Swal.fire({
        toast: true,
        position,
        icon,
        title,
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
            popup: 'rounded-lg shadow-lg',
            title: 'text-sm font-medium'
        },
        didOpen: (toast) => {
            toast.addEventListener('mouseenter', Swal.stopTimer);
            toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
    });
};

export default {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirmation,
    showDeleteConfirmation,
    showLoading,
    closeLoading,
    showFormDialog,
    showProgress,
    updateProgress,
    showMixed,
    showToast
};
