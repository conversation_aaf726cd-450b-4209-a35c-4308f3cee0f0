import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Calendar, Clock, User, CheckCircle } from 'lucide-react';

export default function AppointmentsList({ 
    userRole, 
    upcomingAppointments = [], 
    todaysAppointments = [], 
    pastAppointments = [],
    isDoctor,
    isPatient 
}) {
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    };

    const formatTime = (dateString) => {
        return new Date(dateString).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'confirmed': return 'text-green-600 bg-green-50';
            case 'scheduled': return 'text-blue-600 bg-blue-50';
            case 'completed': return 'text-gray-600 bg-gray-50';
            case 'cancelled': return 'text-red-600 bg-red-50';
            default: return 'text-gray-600 bg-gray-50';
        }
    };

    const renderAppointment = (appointment, index) => (
        <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
            <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                    <p className="text-sm font-medium">
                        {isDoctor() && appointment.patient?.name}
                        {isPatient() && `Dr. ${appointment.doctor?.name}`}
                        {!isDoctor() && !isPatient() && 'Appointment'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                        {formatDate(appointment.appointment_date)} at {formatTime(appointment.appointment_date)}
                    </p>
                </div>
            </div>
            <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(appointment.status)}`}>
                    {appointment.status}
                </span>
            </div>
        </div>
    );

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Appointments
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Today's Appointments */}
                {todaysAppointments.length > 0 && (
                    <div>
                        <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Today's Schedule
                        </h4>
                        <div className="space-y-2">
                            {todaysAppointments.slice(0, 3).map(renderAppointment)}
                        </div>
                    </div>
                )}

                {/* Upcoming Appointments */}
                {upcomingAppointments.length > 0 && (
                    <div>
                        <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            Upcoming
                        </h4>
                        <div className="space-y-2">
                            {upcomingAppointments.slice(0, 3).map(renderAppointment)}
                        </div>
                    </div>
                )}

                {/* Past Appointments for Patients */}
                {isPatient() && pastAppointments.length > 0 && (
                    <div>
                        <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                            <CheckCircle className="h-4 w-4" />
                            Recent Sessions
                        </h4>
                        <div className="space-y-2">
                            {pastAppointments.slice(0, 3).map(renderAppointment)}
                        </div>
                    </div>
                )}

                {/* Empty State */}
                {todaysAppointments.length === 0 && upcomingAppointments.length === 0 && pastAppointments.length === 0 && (
                    <div className="text-center py-6">
                        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                        <p className="text-muted-foreground">No appointments found</p>
                        <p className="text-sm text-muted-foreground">
                            {isPatient() ? 'Book your first appointment to get started' : 'No appointments scheduled'}
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}