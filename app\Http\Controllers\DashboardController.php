<?php

namespace App\Http\Controllers;

use App\Services\BranchService;
use App\Services\UserService;
use App\Services\AppointmentService;
use App\Services\PackageAnalyticsService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    public function __construct(
        private BranchService $branchService,
        private UserService $userService,
        private AppointmentService $appointmentService,
        private PackageAnalyticsService $packageAnalyticsService
    ) {}

    public function index()
    {
        $user = auth()->user();
        
        // Get role-specific data
        $dashboardData = $this->getDashboardDataForRole($user);
        
        // Use single unified dashboard component
        return Inertia::render('Dashboard', [
            'user' => $user->load(['roles', 'doctorProfile', 'patientProfile']),
            ...$dashboardData
        ]);
    }

    public function getData()
    {
        $user = auth()->user();
        return response()->json($this->getDashboardDataForRole($user));
    }

    public function getPackageAnalytics(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasRole('admin')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $filters = $request->only(['year', 'month', 'course_type', 'payment_mode', 'date']);

        $packageTracker = $this->packageAnalyticsService->getPackageTrackerData($filters);
        $financialCollections = $this->packageAnalyticsService->getFinancialCollectionsData($filters);

        return response()->json([
            'package_tracker' => $packageTracker,
            'financial_collections' => $financialCollections,
        ]);
    }

    private function getDashboardDataForRole($user)
    {
        if ($user->hasRole('admin')) {
            return $this->getAdminDashboardData();
        } elseif ($user->hasRole('branch_head')) {
            return $this->getBranchHeadDashboardData($user);
        } elseif ($user->hasRole('doctor')) {
            return $this->getDoctorDashboardData($user);
        } elseif ($user->hasRole('patient')) {
            return $this->getPatientDashboardData($user);
        }
        
        return [];
    }

    private function getDashboardComponent($user)
    {
        if ($user->hasRole('admin')) {
            return 'Dashboard/Admin';
        } elseif ($user->hasRole('branch_head')) {
            return 'Dashboard/BranchHead';
        } elseif ($user->hasRole('doctor')) {
            return 'Dashboard/Doctor';
        } elseif ($user->hasRole('patient')) {
            return 'Dashboard/Patient';
        }
        
        return 'Dashboard/Default';
    }

    private function getAdminDashboardData()
    {
        $branches = $this->branchService->getAllBranches();
        $todaysAppointments = $this->appointmentService->getTodaysAppointments();
        $upcomingAppointments = $this->appointmentService->getUpcomingAppointments()->take(10);

        $stats = [
            'total_branches' => $branches->count(),
            'active_branches' => $branches->where('is_active', true)->count(),
            'total_doctors' => $branches->sum('doctor_profiles_count') ?: 0,
            'total_patients' => $branches->sum('patient_profiles_count') ?: 0,
            'todays_appointments' => $todaysAppointments->count(),
            'completed_today' => $todaysAppointments->where('status', 'completed')->count(),
            'pending_appointments' => $todaysAppointments->whereIn('status', ['scheduled', 'confirmed'])->count(),
        ];

        $recentActivity = [
            'branches' => $branches->sortByDesc('created_at')->take(5),
            'appointments' => $todaysAppointments->sortByDesc('created_at')->take(10),
        ];

        // Get package analytics data
        $packageAnalytics = $this->packageAnalyticsService->getDashboardSummary();
        $availableFilters = $this->packageAnalyticsService->getAvailableFilters();

        return [
            'stats' => $stats,
            'recentActivity' => $recentActivity,
            'upcomingAppointments' => $upcomingAppointments->toArray(),
            'branchStats' => $branches->map(function ($branch) {
                return [
                    'name' => $branch->name,
                    'city' => $branch->city,
                    'doctors' => $branch->doctor_profiles_count ?: 0,
                    'patients' => $branch->patient_profiles_count ?: 0,
                    'appointments' => $branch->appointments_count ?: 0,
                ];
            })->toArray(),
            'packageAnalytics' => $packageAnalytics,
            'availableFilters' => $availableFilters,
        ];
    }

    private function getBranchHeadDashboardData($user)
    {
        $branch = $user->managedBranch;
        if (!$branch) {
            return [
                'error' => 'No branch assigned to this branch head',
                'stats' => [],
                'branch' => null,
                'doctors' => [],
                'patients' => []
            ];
        }

        $branchAppointments = $this->appointmentService->getAppointmentsByBranch($branch->id);
        $branchDoctors = $this->userService->getDoctorsByBranch($branch->id);
        $branchPatients = $this->userService->getPatientsByBranch($branch->id);

        return [
            'branch' => $branch->load(['doctorProfiles', 'patientProfiles']),
            'stats' => [
                'total_doctors' => $branchDoctors->count(),
                'total_patients' => $branchPatients->count(),
                'todays_appointments' => $branchAppointments->filter(fn($a) => $a->appointment_date->isToday())->count(),
                'pending_appointments' => $branchAppointments->whereIn('status', ['scheduled', 'confirmed'])->count(),
            ],
            'recentAppointments' => $branchAppointments->sortByDesc('created_at')->take(10),
            'doctors' => $branchDoctors->toArray(),
            'patients' => $branchPatients->take(10)->toArray(),
        ];
    }

    private function getDoctorDashboardData($user)
    {
        $appointments = $this->appointmentService->getAppointmentsByDoctor($user->id);
        $todaysAppointments = $appointments->filter(fn($a) => $a->appointment_date->isToday());
        $upcomingAppointments = $appointments->filter(fn($a) => $a->appointment_date->isFuture())->take(10);

        return [
            'doctorProfile' => $user->doctorProfile ? $user->doctorProfile->load('branch') : null,
            'stats' => [
                'total_appointments' => $appointments->count(),
                'todays_appointments' => $todaysAppointments->count(),
                'upcoming_appointments' => $upcomingAppointments->count(),
                'completed_appointments' => $appointments->where('status', 'completed')->count(),
            ],
            'todaysAppointments' => $todaysAppointments->sortBy('appointment_date')->values()->toArray(),
            'upcomingAppointments' => $upcomingAppointments->sortBy('appointment_date')->values()->toArray(),
            'recentPatients' => $appointments->map(fn($a) => $a->patient)->unique('id')->take(5)->values()->toArray(),
        ];
    }

    private function getPatientDashboardData($user)
    {
        $appointments = $this->appointmentService->getAppointmentsByPatient($user->id);
        $upcomingAppointments = $appointments->filter(fn($a) => $a->appointment_date->isFuture())->take(5);
        $pastAppointments = $appointments->filter(fn($a) => $a->appointment_date->isPast())->sortByDesc('appointment_date')->take(5);

        return [
            'patientProfile' => $user->patientProfile ? $user->patientProfile->load('branch') : null,
            'stats' => [
                'total_appointments' => $appointments->count(),
                'upcoming_appointments' => $upcomingAppointments->count(),
                'completed_appointments' => $appointments->where('status', 'completed')->count(),
                'cancelled_appointments' => $appointments->where('status', 'cancelled')->count(),
            ],
            'upcomingAppointments' => $upcomingAppointments->sortBy('appointment_date')->values()->toArray(),
            'pastAppointments' => $pastAppointments->values()->toArray(),
            'recentDoctors' => $appointments->map(fn($a) => $a->doctor)->unique('id')->take(3)->values()->toArray(),
        ];
    }
}