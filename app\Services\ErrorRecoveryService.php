<?php

namespace App\Services;

use App\Models\Appointment;
use App\Models\User;
use App\Models\DoctorProfile;
use App\Contracts\AppointmentRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ErrorRecoveryService
{
    public function __construct(
        private AppointmentRepositoryInterface $appointmentRepository
    ) {}

    /**
     * Suggest alternative appointment times when there's a conflict
     */
    public function suggestAlternativeAppointmentTimes(array $appointmentData, int $maxSuggestions = 5): array
    {
        if (!isset($appointmentData['doctor_id'], $appointmentData['appointment_date'], $appointmentData['duration_minutes'])) {
            return [];
        }

        $doctorId = $appointmentData['doctor_id'];
        $requestedDate = Carbon::parse($appointmentData['appointment_date']);
        $duration = $appointmentData['duration_minutes'];

        $suggestions = [];

        // Get doctor's working hours
        $doctor = User::with('doctorProfile')->find($doctorId);
        if (!$doctor || !$doctor->doctorProfile) {
            return [];
        }

        $doctorProfile = $doctor->doctorProfile;
        $workingDays = $doctorProfile->available_days ? explode(',', $doctorProfile->available_days) : ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $startTime = Carbon::parse($doctorProfile->start_time ?? '09:00');
        $endTime = Carbon::parse($doctorProfile->end_time ?? '17:00');

        // Try to find alternatives on the same day first
        $sameDayAlternatives = $this->findAlternativesOnDate($doctorId, $requestedDate, $duration, $startTime, $endTime);
        $suggestions = array_merge($suggestions, $sameDayAlternatives);

        // If not enough suggestions, try next few working days
        $currentDate = $requestedDate->copy();
        $daysChecked = 0;
        $maxDaysToCheck = 14;

        while (count($suggestions) < $maxSuggestions && $daysChecked < $maxDaysToCheck) {
            $currentDate->addDay();
            $daysChecked++;

            $dayName = strtolower($currentDate->format('l'));
            if (!in_array($dayName, $workingDays)) {
                continue;
            }

            $dayAlternatives = $this->findAlternativesOnDate($doctorId, $currentDate, $duration, $startTime, $endTime);
            $suggestions = array_merge($suggestions, $dayAlternatives);

            if (count($suggestions) >= $maxSuggestions) {
                break;
            }
        }

        return array_slice($suggestions, 0, $maxSuggestions);
    }

    /**
     * Find available time slots on a specific date
     */
    private function findAlternativesOnDate(int $doctorId, Carbon $date, int $duration, Carbon $startTime, Carbon $endTime): array
    {
        $alternatives = [];
        
        // Get existing appointments for this doctor on this date
        $existingAppointments = $this->appointmentRepository->getDoctorAppointmentsForDate($doctorId, $date->toDateString());
        
        // Create time slots (15-minute intervals)
        $currentSlot = $date->copy()->setTime($startTime->hour, $startTime->minute);
        $dayEnd = $date->copy()->setTime($endTime->hour, $endTime->minute);

        while ($currentSlot->copy()->addMinutes($duration)->lte($dayEnd)) {
            $slotEnd = $currentSlot->copy()->addMinutes($duration);
            
            // Check if this slot conflicts with existing appointments
            $hasConflict = false;
            foreach ($existingAppointments as $appointment) {
                $appointmentStart = Carbon::parse($appointment->appointment_date);
                $appointmentEnd = $appointmentStart->copy()->addMinutes($appointment->duration_minutes);
                
                if ($this->timeSlotsOverlap($currentSlot, $slotEnd, $appointmentStart, $appointmentEnd)) {
                    $hasConflict = true;
                    break;
                }
            }

            if (!$hasConflict && $currentSlot->isFuture()) {
                $alternatives[] = [
                    'date' => $currentSlot->toDateString(),
                    'time' => $currentSlot->format('H:i'),
                    'datetime' => $currentSlot->toDateTimeString(),
                    'formatted_time' => $currentSlot->format('g:i A'),
                    'formatted_date' => $currentSlot->format('M j, Y'),
                    'day_name' => $currentSlot->format('l'),
                ];
            }

            $currentSlot->addMinutes(15); // Move to next 15-minute slot
        }

        return $alternatives;
    }

    /**
     * Check if two time slots overlap
     */
    private function timeSlotsOverlap(Carbon $start1, Carbon $end1, Carbon $start2, Carbon $end2): bool
    {
        return $start1->lt($end2) && $end1->gt($start2);
    }

    /**
     * Suggest alternative doctors for an appointment
     */
    public function suggestAlternativeDoctors(array $appointmentData, int $maxSuggestions = 3): array
    {
        if (!isset($appointmentData['appointment_date'], $appointmentData['duration_minutes'], $appointmentData['branch_id'])) {
            return [];
        }

        $requestedDateTime = Carbon::parse($appointmentData['appointment_date']);
        $duration = $appointmentData['duration_minutes'];
        $branchId = $appointmentData['branch_id'];
        $originalDoctorId = $appointmentData['doctor_id'] ?? null;

        // Get all active doctors in the same branch
        $availableDoctors = DoctorProfile::with('user')
            ->where('branch_id', $branchId)
            ->where('is_active', true)
            ->when($originalDoctorId, function ($query) use ($originalDoctorId) {
                return $query->where('user_id', '!=', $originalDoctorId);
            })
            ->get();

        $suggestions = [];

        foreach ($availableDoctors as $doctorProfile) {
            // Check if doctor is available at the requested time
            if ($this->isDoctorAvailableAtTime($doctorProfile->user_id, $requestedDateTime, $duration)) {
                $suggestions[] = [
                    'doctor_id' => $doctorProfile->user_id,
                    'doctor_name' => $doctorProfile->user->name,
                    'specialization' => $doctorProfile->specialization,
                    'consultation_fee' => $doctorProfile->consultation_fee,
                    'formatted_fee' => '$' . number_format($doctorProfile->consultation_fee, 2),
                ];

                if (count($suggestions) >= $maxSuggestions) {
                    break;
                }
            }
        }

        return $suggestions;
    }

    /**
     * Check if a doctor is available at a specific time
     */
    private function isDoctorAvailableAtTime(int $doctorId, Carbon $dateTime, int $duration): bool
    {
        $doctor = User::with('doctorProfile')->find($doctorId);
        if (!$doctor || !$doctor->doctorProfile) {
            return false;
        }

        // Check working days
        $dayName = strtolower($dateTime->format('l'));
        $workingDays = $doctor->doctorProfile->available_days ? explode(',', $doctor->doctorProfile->available_days) : ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        
        if (!in_array($dayName, $workingDays)) {
            return false;
        }

        // Check working hours
        $startTime = Carbon::parse($doctor->doctorProfile->start_time ?? '09:00');
        $endTime = Carbon::parse($doctor->doctorProfile->end_time ?? '17:00');
        $appointmentTime = Carbon::parse($dateTime->format('H:i'));
        $appointmentEndTime = $appointmentTime->copy()->addMinutes($duration);

        if ($appointmentTime->lt($startTime) || $appointmentEndTime->gt($endTime)) {
            return false;
        }

        // Check for conflicts with existing appointments
        $existingAppointments = $this->appointmentRepository->getDoctorAppointmentsForDate($doctorId, $dateTime->toDateString());
        
        foreach ($existingAppointments as $appointment) {
            $appointmentStart = Carbon::parse($appointment->appointment_date);
            $appointmentEnd = $appointmentStart->copy()->addMinutes($appointment->duration_minutes);
            $requestedEnd = $dateTime->copy()->addMinutes($duration);
            
            if ($this->timeSlotsOverlap($dateTime, $requestedEnd, $appointmentStart, $appointmentEnd)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Recover from data integrity issues by suggesting corrections
     */
    public function suggestDataIntegrityFixes(array $data, string $entityType): array
    {
        $suggestions = [];

        switch ($entityType) {
            case 'user':
                $suggestions = $this->suggestUserDataFixes($data);
                break;
            case 'appointment':
                $suggestions = $this->suggestAppointmentDataFixes($data);
                break;
            case 'patient_package':
                $suggestions = $this->suggestPackageDataFixes($data);
                break;
        }

        return $suggestions;
    }

    /**
     * Suggest fixes for user data integrity issues
     */
    private function suggestUserDataFixes(array $data): array
    {
        $suggestions = [];

        // Check for missing required fields
        if (empty($data['email'])) {
            $suggestions[] = 'Email address is required';
        } elseif (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $suggestions[] = 'Please provide a valid email address';
        }

        if (empty($data['name'])) {
            $suggestions[] = 'Full name is required';
        }

        if (isset($data['role']) && !in_array($data['role'], ['admin', 'doctor', 'patient', 'branch_head'])) {
            $suggestions[] = 'Please select a valid role';
        }

        return $suggestions;
    }

    /**
     * Suggest fixes for appointment data integrity issues
     */
    private function suggestAppointmentDataFixes(array $data): array
    {
        $suggestions = [];

        if (isset($data['appointment_date'])) {
            $appointmentDate = Carbon::parse($data['appointment_date']);
            if ($appointmentDate->isPast()) {
                $suggestions[] = 'Appointment date must be in the future';
            }
        }

        if (isset($data['duration_minutes']) && ($data['duration_minutes'] < 15 || $data['duration_minutes'] > 480)) {
            $suggestions[] = 'Appointment duration must be between 15 and 480 minutes';
        }

        if (isset($data['duration_minutes']) && $data['duration_minutes'] % 15 !== 0) {
            $suggestions[] = 'Appointment duration must be in 15-minute increments';
        }

        return $suggestions;
    }

    /**
     * Suggest fixes for package data integrity issues
     */
    private function suggestPackageDataFixes(array $data): array
    {
        $suggestions = [];

        if (isset($data['discount_given'], $data['original_price']) && $data['discount_given'] > $data['original_price']) {
            $suggestions[] = 'Discount cannot be greater than the original price';
        }

        if (isset($data['number_of_installments']) && $data['number_of_installments'] < 1) {
            $suggestions[] = 'Number of installments must be at least 1';
        }

        if (isset($data['amount_paid'], $data['total_amount_after_discount']) && $data['amount_paid'] > $data['total_amount_after_discount']) {
            $suggestions[] = 'Amount paid cannot exceed the total amount';
        }

        return $suggestions;
    }

    /**
     * Attempt automatic data correction for common issues
     */
    public function attemptDataCorrection(array $data, string $entityType): array
    {
        switch ($entityType) {
            case 'appointment':
                return $this->correctAppointmentData($data);
            case 'user':
                return $this->correctUserData($data);
            default:
                return $data;
        }
    }

    /**
     * Correct common appointment data issues
     */
    private function correctAppointmentData(array $data): array
    {
        // Round duration to nearest 15-minute increment
        if (isset($data['duration_minutes']) && $data['duration_minutes'] % 15 !== 0) {
            $data['duration_minutes'] = round($data['duration_minutes'] / 15) * 15;
        }

        // Ensure minimum duration
        if (isset($data['duration_minutes']) && $data['duration_minutes'] < 15) {
            $data['duration_minutes'] = 15;
        }

        // Ensure maximum duration
        if (isset($data['duration_minutes']) && $data['duration_minutes'] > 480) {
            $data['duration_minutes'] = 480;
        }

        return $data;
    }

    /**
     * Correct common user data issues
     */
    private function correctUserData(array $data): array
    {
        // Trim whitespace from name and email
        if (isset($data['name'])) {
            $data['name'] = trim($data['name']);
        }

        if (isset($data['email'])) {
            $data['email'] = trim(strtolower($data['email']));
        }

        // Format phone number
        if (isset($data['phone'])) {
            $data['phone'] = preg_replace('/[^0-9+]/', '', $data['phone']);
        }

        return $data;
    }
}
