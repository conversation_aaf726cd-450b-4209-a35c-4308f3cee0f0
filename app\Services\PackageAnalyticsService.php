<?php

namespace App\Services;

use App\Models\PatientPackage;
use App\Models\PackageInstallment;
use App\Models\TherapyPackage;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PackageAnalyticsService
{
    public function getPackageTrackerData(array $filters = []): array
    {
        $year = $filters['year'] ?? now()->year;
        $month = $filters['month'] ?? null;
        $courseType = $filters['course_type'] ?? null;

        $query = PatientPackage::with([
            'patient:id,name,email',
            'patient.patientProfile:id,user_id,registration_number',
            'therapyPackage:id,title,package_type,total_sessions',
            'packageSessions'
        ])
        ->whereYear('created_at', $year);

        if ($month) {
            $query->whereMonth('created_at', $month);
        }

        if ($courseType) {
            $query->whereHas('therapyPackage', function ($q) use ($courseType) {
                $q->where('package_type', $courseType);
            });
        }

        $packages = $query->get();

        $trackerData = $packages->map(function ($package) {
            $completedSessions = $package->packageSessions->where('status', 'completed')->count();
            $totalSessions = $package->therapyPackage->total_sessions ?? 0;
            $pendingSessions = max(0, $totalSessions - $completedSessions);

            return [
                'name' => $package->patient->name,
                'registration_no' => $package->patient->patientProfile->registration_number ?? 'N/A',
                'course_type' => ucfirst($package->therapyPackage->package_type ?? 'N/A'),
                'total_sessions' => $totalSessions,
                'sessions_completed' => $completedSessions,
                'pending_sessions' => $pendingSessions,
                'package_id' => $package->id,
                'patient_id' => $package->patient_id,
            ];
        });

        $grandTotal = [
            'total_sessions' => $trackerData->sum('total_sessions'),
            'sessions_completed' => $trackerData->sum('sessions_completed'),
            'pending_sessions' => $trackerData->sum('pending_sessions'),
        ];

        return [
            'data' => $trackerData->toArray(),
            'grand_total' => $grandTotal,
            'filters' => [
                'year' => $year,
                'month' => $month,
                'course_type' => $courseType,
            ]
        ];
    }

    public function getFinancialCollectionsData(array $filters = []): array
    {
        $year = $filters['year'] ?? now()->year;
        $month = $filters['month'] ?? null;
        $paymentMode = $filters['payment_mode'] ?? null;
        $date = $filters['date'] ?? null;

        // Get current year data
        $currentYearQuery = PatientPackage::with(['therapyPackage', 'installments'])
            ->whereYear('created_at', $year);

        // Get last year data for pending fees
        $lastYearQuery = PatientPackage::with(['therapyPackage', 'installments'])
            ->whereYear('created_at', $year - 1);

        if ($month) {
            $currentYearQuery->whereMonth('created_at', $month);
        }

        if ($date) {
            $currentYearQuery->whereDate('created_at', $date);
        }

        $currentYearPackages = $currentYearQuery->get();
        $lastYearPackages = $lastYearQuery->get();

        // Calculate collections by course type
        $courseTypes = ['consultation', 'counselling'];
        $collectionsData = [];

        foreach ($courseTypes as $courseType) {
            $currentYearCoursePackages = $currentYearPackages->filter(function ($package) use ($courseType) {
                return $package->therapyPackage && 
                       strtolower($package->therapyPackage->package_type) === $courseType;
            });

            $lastYearCoursePackages = $lastYearPackages->filter(function ($package) use ($courseType) {
                return $package->therapyPackage && 
                       strtolower($package->therapyPackage->package_type) === $courseType;
            });

            // Calculate last year pending fees
            $lastYearPendingFees = $this->calculatePendingFees($lastYearCoursePackages, $year - 1);
            
            // Calculate current year pending fees
            $currentYearPendingFees = $this->calculatePendingFees($currentYearCoursePackages, $year);

            // Calculate fee collections
            $feeCollections = $this->calculateFeeCollections($currentYearCoursePackages, $paymentMode);

            // Calculate discount amounts
            $discountAmount = $currentYearCoursePackages->sum('discount_given');

            // Calculate written off amounts (assuming packages with status 'cancelled' or 'terminated')
            $writtenOffAmount = $currentYearCoursePackages
                ->whereIn('status', ['cancelled', 'terminated'])
                ->sum('total_amount_after_discount');

            // Calculate actual amount received
            $actualAmountReceived = $currentYearCoursePackages->sum('amount_paid');

            // Calculate balance recoverable
            $totalDue = $currentYearCoursePackages->sum('total_amount_after_discount');
            $balanceRecoverable = $totalDue - $actualAmountReceived - $writtenOffAmount;

            $collectionsData[] = [
                'course_type' => ucfirst($courseType),
                'last_year_pending_fees' => $lastYearPendingFees,
                'current_year_pending_fees' => $currentYearPendingFees,
                'fee_collections' => $feeCollections,
                'discount_amount' => $discountAmount,
                'written_off_amount' => $writtenOffAmount,
                'actual_amount_received' => $actualAmountReceived,
                'balance_recoverable' => $balanceRecoverable,
            ];
        }

        // Calculate grand totals
        $grandTotal = [
            'last_year_pending_fees' => collect($collectionsData)->sum('last_year_pending_fees'),
            'current_year_pending_fees' => collect($collectionsData)->sum('current_year_pending_fees'),
            'fee_collections' => collect($collectionsData)->sum('fee_collections'),
            'discount_amount' => collect($collectionsData)->sum('discount_amount'),
            'written_off_amount' => collect($collectionsData)->sum('written_off_amount'),
            'actual_amount_received' => collect($collectionsData)->sum('actual_amount_received'),
            'balance_recoverable' => collect($collectionsData)->sum('balance_recoverable'),
        ];

        return [
            'data' => $collectionsData,
            'grand_total' => $grandTotal,
            'filters' => [
                'year' => $year,
                'month' => $month,
                'payment_mode' => $paymentMode,
                'date' => $date,
            ]
        ];
    }

    private function calculatePendingFees($packages, $year): float
    {
        return $packages->sum(function ($package) use ($year) {
            $totalDue = $package->total_amount_after_discount ?? 0;
            $amountPaid = $package->amount_paid ?? 0;
            
            // Only consider as pending if the package is still active or if it's from a previous year
            if ($package->status === 'active' || $package->created_at->year < now()->year) {
                return max(0, $totalDue - $amountPaid);
            }
            
            return 0;
        });
    }

    private function calculateFeeCollections($packages, $paymentMode = null): float
    {
        if (!$paymentMode || $paymentMode === 'all') {
            return $packages->sum('amount_paid');
        }

        // Filter by payment mode through installments
        return $packages->sum(function ($package) use ($paymentMode) {
            return $package->installments
                ->where('status', 'paid')
                ->when($paymentMode !== 'all', function ($collection) use ($paymentMode) {
                    return $collection->where('payment_method', $paymentMode);
                })
                ->sum('amount_paid');
        });
    }

    public function getAvailableFilters(): array
    {
        $currentYear = now()->year;
        $years = range($currentYear - 5, $currentYear + 1);
        
        $months = [
            1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
            5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
            9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
        ];

        $courseTypes = TherapyPackage::distinct('package_type')
            ->whereNotNull('package_type')
            ->pluck('package_type')
            ->map(function ($type) {
                return ['value' => $type, 'label' => ucfirst($type)];
            })
            ->toArray();

        $paymentModes = [
            ['value' => 'all', 'label' => 'All'],
            ['value' => 'cash', 'label' => 'Cash'],
            ['value' => 'card', 'label' => 'Swipe'],
            ['value' => 'transfer', 'label' => 'TRF'],
            ['value' => 'cheque', 'label' => 'CHQ'],
        ];

        return [
            'years' => $years,
            'months' => $months,
            'course_types' => $courseTypes,
            'payment_modes' => $paymentModes,
        ];
    }

    public function getDashboardSummary(): array
    {
        $currentYear = now()->year;
        $currentMonth = now()->month;

        // Get package tracker summary
        $packageTracker = $this->getPackageTrackerData(['year' => $currentYear]);
        
        // Get financial collections summary
        $financialCollections = $this->getFinancialCollectionsData(['year' => $currentYear]);

        // Get monthly trends
        $monthlyTrends = $this->getMonthlyTrends($currentYear);

        return [
            'package_tracker' => $packageTracker,
            'financial_collections' => $financialCollections,
            'monthly_trends' => $monthlyTrends,
            'summary_stats' => [
                'total_active_packages' => PatientPackage::where('status', 'active')->count(),
                'total_revenue_current_year' => PatientPackage::whereYear('created_at', $currentYear)->sum('amount_paid'),
                'pending_collections' => PatientPackage::where('status', 'active')
                    ->get()
                    ->sum(function ($package) {
                        return max(0, ($package->total_amount_after_discount ?? 0) - ($package->amount_paid ?? 0));
                    }),
            ]
        ];
    }

    private function getMonthlyTrends($year): array
    {
        $trends = [];
        
        for ($month = 1; $month <= 12; $month++) {
            $monthlyPackages = PatientPackage::whereYear('created_at', $year)
                ->whereMonth('created_at', $month)
                ->get();

            $trends[] = [
                'month' => $month,
                'month_name' => Carbon::create($year, $month, 1)->format('M'),
                'packages_assigned' => $monthlyPackages->count(),
                'revenue_collected' => $monthlyPackages->sum('amount_paid'),
                'sessions_completed' => $monthlyPackages->sum('sessions_completed'),
            ];
        }

        return $trends;
    }
}
