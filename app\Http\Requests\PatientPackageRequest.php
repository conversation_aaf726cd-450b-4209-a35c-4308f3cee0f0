<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\User;
use App\Models\TherapyPackage;

class PatientPackageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->user()->hasAnyRole(['admin', 'branch_head', 'doctor']);
    }

    public function rules(): array
    {
        return [
            'patient_id' => ['required', 'exists:users,id'],
            'therapy_package_id' => ['required', 'exists:therapy_packages,id'],
            'assigned_doctor_id' => ['nullable', 'exists:users,id'],
            'start_date' => ['required', 'date', 'after_or_equal:today'],

            // Financial fields
            'discount_given' => ['required', 'numeric', 'min:0'],
            'number_of_installments' => ['required', 'integer', 'min:1', 'max:24'],
            'payment_type' => ['required', 'in:cash,card,bank_transfer,cheque,online,installments'],
            'amount_paid' => ['required', 'numeric', 'min:0'],
            'payment_status' => ['required', 'in:pending,partial,paid'],
            'payment_notes' => ['nullable', 'string', 'max:1000'],
            'notes' => ['nullable', 'string', 'max:1000'],

            // Custom installment data (when payment_type is installments)
            'installments' => ['nullable', 'array'],
            'installments.*.date' => ['required_with:installments', 'date'],
            'installments.*.amount' => ['required_with:installments', 'numeric', 'min:0'],
            'installments.*.due_date' => ['required_with:installments', 'date'],
        ];
    }

    public function messages(): array
    {
        return [
            'patient_id.required' => 'Please select a patient',
            'patient_id.exists' => 'Selected patient does not exist',
            'therapy_package_id.required' => 'Please select a therapy package',
            'therapy_package_id.exists' => 'Selected therapy package does not exist',
            'assigned_doctor_id.exists' => 'Selected doctor does not exist',
            'start_date.required' => 'Start date is required',
            'start_date.after_or_equal' => 'Start date cannot be in the past',
            'discount_given.required' => 'Discount amount is required',
            'discount_given.min' => 'Discount cannot be negative',
            'number_of_installments.required' => 'Number of installments is required',
            'number_of_installments.min' => 'Must have at least 1 installment',
            'number_of_installments.max' => 'Cannot exceed 24 installments',
            'payment_type.required' => 'Payment type is required',
            'payment_type.in' => 'Invalid payment type selected',
            'amount_paid.required' => 'Amount paid is required',
            'amount_paid.min' => 'Amount paid cannot be negative',
            'payment_status.required' => 'Payment status is required',
            'payment_status.in' => 'Invalid payment status selected',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = auth()->user();
            
            // Validate patient selection based on user role
            if ($this->patient_id) {
                $patient = User::find($this->patient_id);
                
                if (!$patient || !$patient->hasRole('patient')) {
                    $validator->errors()->add('patient_id', 'Selected user is not a patient');
                    return;
                }

                // Check if patient belongs to the same branch for doctors and branch heads
                if ($user->hasRole('doctor')) {
                    $doctorBranchId = $user->doctorProfile?->branch_id;
                    $patientBranchId = $patient->patientProfile?->branch_id;
                    
                    if ($doctorBranchId && $patientBranchId && $doctorBranchId !== $patientBranchId) {
                        $validator->errors()->add('patient_id', 'You can only assign packages to patients in your branch');
                    }
                } elseif ($user->hasRole('branch_head')) {
                    $branchHeadBranchId = $user->branchHeadProfile?->branch_id;
                    $patientBranchId = $patient->patientProfile?->branch_id;
                    
                    if ($branchHeadBranchId && $patientBranchId && $branchHeadBranchId !== $patientBranchId) {
                        $validator->errors()->add('patient_id', 'You can only assign packages to patients in your branch');
                    }
                }

                // Check if patient already has an active package
                $activePackage = $patient->activePatientPackages()->first();
                if ($activePackage) {
                    $validator->errors()->add('patient_id', 'Patient already has an active therapy package');
                }
            }

            // Validate therapy package
            if ($this->therapy_package_id) {
                $package = TherapyPackage::find($this->therapy_package_id);
                
                if (!$package || !$package->is_active) {
                    $validator->errors()->add('therapy_package_id', 'Selected therapy package is not available');
                }
            }

            // Validate assigned doctor
            if ($this->assigned_doctor_id) {
                $doctor = User::find($this->assigned_doctor_id);
                
                if (!$doctor || !$doctor->hasRole('doctor')) {
                    $validator->errors()->add('assigned_doctor_id', 'Selected user is not a doctor');
                } else {
                    // Check if doctor belongs to the same branch as patient
                    if ($this->patient_id) {
                        $patient = User::find($this->patient_id);
                        $doctorBranchId = $doctor->doctorProfile?->branch_id;
                        $patientBranchId = $patient->patientProfile?->branch_id;
                        
                        if ($doctorBranchId && $patientBranchId && $doctorBranchId !== $patientBranchId) {
                            $validator->errors()->add('assigned_doctor_id', 'Doctor and patient must be in the same branch');
                        }
                    }
                }
            }

            // Validate financial calculations
            if ($this->therapy_package_id && $this->discount_given !== null) {
                $package = TherapyPackage::find($this->therapy_package_id);

                if ($package) {
                    // Validate discount doesn't exceed package price
                    if ($this->discount_given > $package->price) {
                        $validator->errors()->add('discount_given', 'Discount cannot exceed package price');
                    }

                    // Calculate total amount after discount
                    $totalAfterDiscount = $package->price - $this->discount_given;

                    // Validate amount paid doesn't exceed total after discount
                    if ($this->amount_paid > $totalAfterDiscount) {
                        $validator->errors()->add('amount_paid', 'Amount paid cannot exceed total amount after discount');
                    }

                    // Validate payment status consistency
                    if ($this->payment_status === 'paid' && $this->amount_paid < $totalAfterDiscount) {
                        $validator->errors()->add('payment_status', 'Payment status cannot be "paid" when amount is less than total amount');
                    }

                    if ($this->payment_status === 'pending' && $this->amount_paid > 0) {
                        $validator->errors()->add('payment_status', 'Payment status should be "partial" when amount is paid but not complete');
                    }

                    // Validate installments for payment type
                    if ($this->payment_type === 'installments' && $this->number_of_installments <= 1) {
                        $validator->errors()->add('number_of_installments', 'Installment payment type requires more than 1 installment');
                    }

                    if (in_array($this->payment_type, ['cash', 'card', 'bank_transfer', 'cheque', 'online']) && $this->number_of_installments > 1) {
                        $validator->errors()->add('payment_type', 'Single payment methods should have only 1 installment');
                    }

                    // Validate reasonable installment amounts
                    if ($this->payment_type === 'installments' && $this->number_of_installments > 0) {
                        $installmentAmount = $totalAfterDiscount / $this->number_of_installments;
                        if ($installmentAmount < 50) {
                            $validator->errors()->add('number_of_installments', 'Too many installments - each installment would be less than $50');
                        }
                    }
                }
            }
        });
    }
}
