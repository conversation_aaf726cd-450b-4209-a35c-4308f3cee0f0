<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Appointments table indexes
        Schema::table('appointments', function (Blueprint $table) {
            // Index for finding appointments by doctor and date range
            $table->index(['doctor_id', 'appointment_date'], 'idx_appointments_doctor_date');
            
            // Index for finding appointments by patient and date range
            $table->index(['patient_id', 'appointment_date'], 'idx_appointments_patient_date');
            
            // Index for finding appointments by branch and date
            $table->index(['branch_id', 'appointment_date'], 'idx_appointments_branch_date');
            
            // Index for finding appointments by status
            $table->index(['status'], 'idx_appointments_status');
            
            // Index for finding appointments by date range
            $table->index(['appointment_date'], 'idx_appointments_date');
            
            // Composite index for conflict checking
            $table->index(['doctor_id', 'appointment_date', 'status'], 'idx_appointments_conflict_check');
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            // Index for email lookups (if not already exists)
            if (!$this->indexExists('users', 'users_email_unique')) {
                $table->index(['email'], 'idx_users_email');
            }
            
            // Index for active users
            $table->index(['is_active'], 'idx_users_active');
            
            // Index for soft deletes
            $table->index(['deleted_at'], 'idx_users_deleted');
        });

        // Doctor profiles indexes
        Schema::table('doctor_profiles', function (Blueprint $table) {
            // Index for finding doctors by branch
            $table->index(['branch_id'], 'idx_doctor_profiles_branch');
            
            // Index for finding active doctors
            $table->index(['is_active'], 'idx_doctor_profiles_active');
            
            // Index for finding doctors by specialization
            $table->index(['specialization'], 'idx_doctor_profiles_specialization');
            
            // Composite index for active doctors by branch
            $table->index(['branch_id', 'is_active'], 'idx_doctor_profiles_branch_active');
        });

        // Patient profiles indexes
        Schema::table('patient_profiles', function (Blueprint $table) {
            // Index for finding patients by branch
            $table->index(['branch_id'], 'idx_patient_profiles_branch');
            
            // Index for finding active patients
            $table->index(['is_active'], 'idx_patient_profiles_active');
            
            // Composite index for active patients by branch
            $table->index(['branch_id', 'is_active'], 'idx_patient_profiles_branch_active');
        });

        // Medical records indexes
        Schema::table('medical_records', function (Blueprint $table) {
            // Index for finding records by patient
            $table->index(['patient_id'], 'idx_medical_records_patient');
            
            // Index for finding records by doctor
            $table->index(['doctor_id'], 'idx_medical_records_doctor');
            
            // Index for finding records by appointment
            $table->index(['appointment_id'], 'idx_medical_records_appointment');
            
            // Index for finding records by date
            $table->index(['record_date'], 'idx_medical_records_date');
            
            // Composite index for patient records by date
            $table->index(['patient_id', 'record_date'], 'idx_medical_records_patient_date');
        });

        // Branches indexes
        Schema::table('branches', function (Blueprint $table) {
            // Index for finding active branches
            $table->index(['is_active'], 'idx_branches_active');
            
            // Index for soft deletes
            $table->index(['deleted_at'], 'idx_branches_deleted');
        });

        // Branch heads indexes
        Schema::table('branch_heads', function (Blueprint $table) {
            // Index for finding active branch heads
            $table->index(['is_active'], 'idx_branch_heads_active');
            
            // Index for finding by user
            $table->index(['user_id'], 'idx_branch_heads_user');
            
            // Index for finding by branch
            $table->index(['branch_id'], 'idx_branch_heads_branch');
            
            // Index for date range queries
            $table->index(['assigned_date'], 'idx_branch_heads_assigned_date');
            $table->index(['end_date'], 'idx_branch_heads_end_date');
        });

        // Therapy packages indexes (if table exists)
        if (Schema::hasTable('therapy_packages')) {
            Schema::table('therapy_packages', function (Blueprint $table) {
                // Index for finding packages by branch
                $table->index(['branch_id'], 'idx_therapy_packages_branch');
                
                // Index for finding active packages
                $table->index(['is_active'], 'idx_therapy_packages_active');
                
                // Index for finding packages by patient
                $table->index(['patient_id'], 'idx_therapy_packages_patient');
                
                // Index for finding packages by status
                $table->index(['status'], 'idx_therapy_packages_status');
            });
        }

        // Therapy sessions indexes (if table exists)
        if (Schema::hasTable('therapy_sessions')) {
            Schema::table('therapy_sessions', function (Blueprint $table) {
                // Index for finding sessions by package
                $table->index(['package_id'], 'idx_therapy_sessions_package');
                
                // Index for finding sessions by appointment
                $table->index(['appointment_id'], 'idx_therapy_sessions_appointment');
                
                // Index for finding sessions by status
                $table->index(['status'], 'idx_therapy_sessions_status');
                
                // Index for finding sessions by date
                $table->index(['session_date'], 'idx_therapy_sessions_date');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes in reverse order
        Schema::table('appointments', function (Blueprint $table) {
            $table->dropIndex('idx_appointments_doctor_date');
            $table->dropIndex('idx_appointments_patient_date');
            $table->dropIndex('idx_appointments_branch_date');
            $table->dropIndex('idx_appointments_status');
            $table->dropIndex('idx_appointments_date');
            $table->dropIndex('idx_appointments_conflict_check');
        });

        Schema::table('users', function (Blueprint $table) {
            if ($this->indexExists('users', 'idx_users_email')) {
                $table->dropIndex('idx_users_email');
            }
            $table->dropIndex('idx_users_active');
            $table->dropIndex('idx_users_deleted');
        });

        Schema::table('doctor_profiles', function (Blueprint $table) {
            $table->dropIndex('idx_doctor_profiles_branch');
            $table->dropIndex('idx_doctor_profiles_active');
            $table->dropIndex('idx_doctor_profiles_specialization');
            $table->dropIndex('idx_doctor_profiles_branch_active');
        });

        Schema::table('patient_profiles', function (Blueprint $table) {
            $table->dropIndex('idx_patient_profiles_branch');
            $table->dropIndex('idx_patient_profiles_active');
            $table->dropIndex('idx_patient_profiles_branch_active');
        });

        Schema::table('medical_records', function (Blueprint $table) {
            $table->dropIndex('idx_medical_records_patient');
            $table->dropIndex('idx_medical_records_doctor');
            $table->dropIndex('idx_medical_records_appointment');
            $table->dropIndex('idx_medical_records_date');
            $table->dropIndex('idx_medical_records_patient_date');
        });

        Schema::table('branches', function (Blueprint $table) {
            $table->dropIndex('idx_branches_active');
            $table->dropIndex('idx_branches_deleted');
        });

        Schema::table('branch_heads', function (Blueprint $table) {
            $table->dropIndex('idx_branch_heads_active');
            $table->dropIndex('idx_branch_heads_user');
            $table->dropIndex('idx_branch_heads_branch');
            $table->dropIndex('idx_branch_heads_assigned_date');
            $table->dropIndex('idx_branch_heads_end_date');
        });

        if (Schema::hasTable('therapy_packages')) {
            Schema::table('therapy_packages', function (Blueprint $table) {
                $table->dropIndex('idx_therapy_packages_branch');
                $table->dropIndex('idx_therapy_packages_active');
                $table->dropIndex('idx_therapy_packages_patient');
                $table->dropIndex('idx_therapy_packages_status');
            });
        }

        if (Schema::hasTable('therapy_sessions')) {
            Schema::table('therapy_sessions', function (Blueprint $table) {
                $table->dropIndex('idx_therapy_sessions_package');
                $table->dropIndex('idx_therapy_sessions_appointment');
                $table->dropIndex('idx_therapy_sessions_status');
                $table->dropIndex('idx_therapy_sessions_date');
            });
        }
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = Schema::getConnection()->getDoctrineSchemaManager()
            ->listTableIndexes($table);
        
        return array_key_exists($index, $indexes);
    }
};
