import StatsCard from '@/Components/ui/StatsCard';
import { Calendar, Clock, CheckCircle } from 'lucide-react';

export default function DoctorStats({ stats }) {
    return (
        <>
            <StatsCard 
                title="Total Appointments"
                icon={Calendar}
                value={stats.total_appointments}
                subtitle="All time"
            />
            
            <StatsCard 
                title="Today's Schedule"
                icon={Clock}
                value={stats.todays_appointments}
                subtitle="Appointments today"
            />
            
            <StatsCard 
                title="Upcoming"
                icon={Calendar}
                value={stats.upcoming_appointments}
                subtitle="Future appointments"
            />
            
            <StatsCard 
                title="Completed"
                icon={CheckCircle}
                value={stats.completed_appointments}
                subtitle="Sessions completed"
            />
        </>
    );
}