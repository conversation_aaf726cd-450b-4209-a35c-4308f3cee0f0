import { showConfirmation, showDeleteConfirmation, showWarning } from '@/utils/sweetAlert';

// Generic confirmation dialog
export const useConfirmation = () => {
    const confirm = async (title, text, options = {}) => {
        const result = await showConfirmation(title, text, options);
        return result.isConfirmed;
    };

    const confirmDelete = async (itemName = 'this item', options = {}) => {
        const result = await showDeleteConfirmation(itemName, options);
        return result.isConfirmed;
    };

    const confirmAction = async (action, itemName, options = {}) => {
        const actionTexts = {
            delete: {
                title: 'Delete Confirmation',
                text: `Are you sure you want to delete ${itemName}? This action cannot be undone.`,
                confirmButtonText: 'Yes, delete it!',
                icon: 'warning'
            },
            cancel: {
                title: 'Cancel Confirmation',
                text: `Are you sure you want to cancel ${itemName}? Any unsaved changes will be lost.`,
                confirmButtonText: 'Yes, cancel',
                icon: 'warning'
            },
            archive: {
                title: 'Archive Confirmation',
                text: `Are you sure you want to archive ${itemName}? It will be moved to archived items.`,
                confirmButtonText: 'Yes, archive',
                icon: 'question'
            },
            restore: {
                title: 'Restore Confirmation',
                text: `Are you sure you want to restore ${itemName}? It will be moved back to active items.`,
                confirmButtonText: 'Yes, restore',
                icon: 'question'
            },
            deactivate: {
                title: 'Deactivate Confirmation',
                text: `Are you sure you want to deactivate ${itemName}? It will no longer be available for use.`,
                confirmButtonText: 'Yes, deactivate',
                icon: 'warning'
            },
            activate: {
                title: 'Activate Confirmation',
                text: `Are you sure you want to activate ${itemName}? It will become available for use.`,
                confirmButtonText: 'Yes, activate',
                icon: 'question'
            },
            complete: {
                title: 'Complete Confirmation',
                text: `Are you sure you want to mark ${itemName} as complete? This action may affect related records.`,
                confirmButtonText: 'Yes, complete',
                icon: 'question'
            },
            submit: {
                title: 'Submit Confirmation',
                text: `Are you sure you want to submit ${itemName}? Please review all information before proceeding.`,
                confirmButtonText: 'Yes, submit',
                icon: 'question'
            }
        };

        const config = actionTexts[action] || {
            title: 'Confirmation Required',
            text: `Are you sure you want to proceed with this action on ${itemName}?`,
            confirmButtonText: 'Yes, proceed',
            icon: 'question'
        };

        const result = await showConfirmation(
            config.title,
            config.text,
            {
                icon: config.icon,
                confirmButtonText: config.confirmButtonText,
                ...options
            }
        );

        return result.isConfirmed;
    };

    return {
        confirm,
        confirmDelete,
        confirmAction
    };
};

// Specific confirmation dialogs for common actions
export const ConfirmationDialogs = {
    // Package related confirmations
    deletePackage: async (packageName) => {
        const { confirmDelete } = useConfirmation();
        return await confirmDelete(`package "${packageName}"`);
    },

    cancelPackage: async (packageName) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('cancel', `package "${packageName}"`);
    },

    completeSession: async (sessionNumber) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('complete', `session #${sessionNumber}`);
    },

    // User related confirmations
    deleteUser: async (userName) => {
        const { confirmDelete } = useConfirmation();
        return await confirmDelete(`user "${userName}"`);
    },

    deactivateUser: async (userName) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('deactivate', `user "${userName}"`);
    },

    // Appointment related confirmations
    cancelAppointment: async (appointmentDate) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('cancel', `appointment on ${appointmentDate}`);
    },

    // Branch related confirmations
    deleteBranch: async (branchName) => {
        const { confirmDelete } = useConfirmation();
        return await confirmDelete(`branch "${branchName}"`);
    },

    // Payment related confirmations
    processPayment: async (amount) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('submit', `payment of ₹${amount}`);
    },

    refundPayment: async (amount) => {
        const result = await showWarning(
            'Refund Confirmation',
            `Are you sure you want to process a refund of ₹${amount}? This action will require approval.`,
            {
                showCancelButton: true,
                confirmButtonText: 'Yes, process refund',
                cancelButtonText: 'Cancel'
            }
        );
        return result.isConfirmed;
    },

    // Data export confirmations
    exportData: async (dataType) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction('submit', `${dataType} export`);
    },

    // Bulk actions
    bulkDelete: async (count, itemType) => {
        const result = await showDeleteConfirmation(
            `${count} ${itemType}${count > 1 ? 's' : ''}`,
            {
                text: `You are about to delete ${count} ${itemType}${count > 1 ? 's' : ''}. This action cannot be undone and may affect related records.`
            }
        );
        return result.isConfirmed;
    },

    bulkUpdate: async (count, itemType, action) => {
        const { confirmAction } = useConfirmation();
        return await confirmAction(action, `${count} ${itemType}${count > 1 ? 's' : ''}`);
    }
};

export default ConfirmationDialogs;
