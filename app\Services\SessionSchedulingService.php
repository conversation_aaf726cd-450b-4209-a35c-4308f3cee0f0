<?php

namespace App\Services;

use App\Models\SessionSchedule;
use App\Models\PatientPackage;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SessionSchedulingService
{
    /**
     * Schedule a new session
     */
    public function scheduleSession(array $data): SessionSchedule
    {
        return DB::transaction(function () use ($data) {
            $session = SessionSchedule::create([
                'patient_package_id' => $data['patient_package_id'],
                'patient_id' => $data['patient_id'],
                'doctor_id' => $data['doctor_id'],
                'scheduled_by' => auth()->id(),
                'session_number' => $data['session_number'],
                'scheduled_date' => $data['scheduled_date'],
                'scheduled_time' => $data['scheduled_time'],
                'duration_minutes' => $data['duration_minutes'],
                'session_type' => $data['session_type'],
                'location' => $data['location'] ?? null,
                'session_notes' => $data['session_notes'] ?? null,
                'preparation_notes' => $data['preparation_notes'] ?? null,
                'session_goals' => $data['session_goals'] ?? null,
            ]);

            return $session;
        });
    }

    /**
     * Update an existing session
     */
    public function updateSession(SessionSchedule $session, array $data): SessionSchedule
    {
        if (!$session->canBeRescheduled()) {
            throw new \Exception('This session cannot be updated');
        }

        return DB::transaction(function () use ($session, $data) {
            $session->update([
                'doctor_id' => $data['doctor_id'],
                'scheduled_date' => $data['scheduled_date'],
                'scheduled_time' => $data['scheduled_time'],
                'duration_minutes' => $data['duration_minutes'],
                'session_type' => $data['session_type'],
                'location' => $data['location'] ?? $session->location,
                'session_notes' => $data['session_notes'] ?? $session->session_notes,
                'preparation_notes' => $data['preparation_notes'] ?? $session->preparation_notes,
                'session_goals' => $data['session_goals'] ?? $session->session_goals,
            ]);

            return $session;
        });
    }

    /**
     * Reschedule a session
     */
    public function rescheduleSession(SessionSchedule $session, string $newDate, string $newTime, string $reason = null): SessionSchedule
    {
        if (!$session->canBeRescheduled()) {
            throw new \Exception('This session cannot be rescheduled');
        }

        return DB::transaction(function () use ($session, $newDate, $newTime, $reason) {
            return $session->reschedule($newDate, $newTime, $reason);
        });
    }

    /**
     * Cancel a session
     */
    public function cancelSession(SessionSchedule $session, string $reason): void
    {
        if (!$session->canBeCancelled()) {
            throw new \Exception('This session cannot be cancelled');
        }

        DB::transaction(function () use ($session, $reason) {
            $session->markAsCancelled($reason);
        });
    }

    /**
     * Get available time slots for a doctor on a specific date
     */
    public function getAvailableTimeSlots(int $doctorId, string $date, int $duration = 60): array
    {
        $workingHours = [
            'start' => '09:00',
            'end' => '18:00',
            'break_start' => '12:00',
            'break_end' => '13:00',
        ];

        $slots = [];
        $currentTime = Carbon::parse($date . ' ' . $workingHours['start']);
        $endTime = Carbon::parse($date . ' ' . $workingHours['end']);
        $breakStart = Carbon::parse($date . ' ' . $workingHours['break_start']);
        $breakEnd = Carbon::parse($date . ' ' . $workingHours['break_end']);

        // Get existing appointments for the doctor on this date
        $existingAppointments = SessionSchedule::where('doctor_id', $doctorId)
            ->whereDate('scheduled_date', $date)
            ->where('status', 'scheduled')
            ->get(['scheduled_time', 'duration_minutes']);

        while ($currentTime->copy()->addMinutes($duration)->lte($endTime)) {
            $slotEnd = $currentTime->copy()->addMinutes($duration);

            // Skip lunch break
            if ($currentTime->lt($breakEnd) && $slotEnd->gt($breakStart)) {
                $currentTime = $breakEnd->copy();
                continue;
            }

            // Check for conflicts with existing appointments
            $hasConflict = false;
            foreach ($existingAppointments as $appointment) {
                $appointmentStart = Carbon::parse($date . ' ' . $appointment->scheduled_time);
                $appointmentEnd = $appointmentStart->copy()->addMinutes($appointment->duration_minutes);

                if ($currentTime->lt($appointmentEnd) && $slotEnd->gt($appointmentStart)) {
                    $hasConflict = true;
                    break;
                }
            }

            if (!$hasConflict) {
                $slots[] = [
                    'time' => $currentTime->format('H:i'),
                    'formatted_time' => $currentTime->format('g:i A'),
                    'available' => true,
                ];
            }

            $currentTime->addMinutes(30); // 30-minute intervals
        }

        return $slots;
    }

    /**
     * Bulk schedule sessions for a package
     */
    public function bulkScheduleSessions(array $data): array
    {
        $package = PatientPackage::with('therapyPackage')->findOrFail($data['patient_package_id']);
        $sessions = [];
        $currentDate = Carbon::parse($data['start_date']);
        $sessionCount = min($data['number_of_sessions'], $package->therapyPackage->total_sessions);

        return DB::transaction(function () use ($data, $package, $sessions, $currentDate, $sessionCount) {
            for ($i = 1; $i <= $sessionCount; $i++) {
                // Skip weekends if requested
                if ($data['skip_weekends'] && $currentDate->isWeekend()) {
                    $currentDate = $this->getNextWorkingDay($currentDate);
                }

                // Check if slot is available
                $availableSlots = $this->getAvailableTimeSlots(
                    $data['doctor_id'],
                    $currentDate->toDateString(),
                    $data['session_duration']
                );

                $requestedTime = $data['session_time'];
                $slotAvailable = collect($availableSlots)->contains('time', $requestedTime);

                if (!$slotAvailable) {
                    // Find the next available slot
                    $nextAvailableSlot = collect($availableSlots)->first();
                    if (!$nextAvailableSlot) {
                        // Move to next day if no slots available
                        $currentDate = $this->getNextSchedulingDate($currentDate, $data['session_frequency']);
                        continue;
                    }
                    $requestedTime = $nextAvailableSlot['time'];
                }

                $session = SessionSchedule::create([
                    'patient_package_id' => $data['patient_package_id'],
                    'patient_id' => $package->patient_id,
                    'doctor_id' => $data['doctor_id'],
                    'scheduled_by' => auth()->id(),
                    'session_number' => $i,
                    'scheduled_date' => $currentDate->toDateString(),
                    'scheduled_time' => $requestedTime,
                    'duration_minutes' => $data['session_duration'],
                    'session_type' => 'individual',
                ]);

                $sessions[] = $session;

                // Calculate next session date based on frequency
                $currentDate = $this->getNextSchedulingDate($currentDate, $data['session_frequency']);
            }

            return $sessions;
        });
    }

    /**
     * Get next scheduling date based on frequency
     */
    private function getNextSchedulingDate(Carbon $currentDate, string $frequency): Carbon
    {
        return match($frequency) {
            'daily' => $currentDate->copy()->addDay(),
            'weekly' => $currentDate->copy()->addWeek(),
            'biweekly' => $currentDate->copy()->addWeeks(2),
            'monthly' => $currentDate->copy()->addMonth(),
            default => $currentDate->copy()->addWeek(),
        };
    }

    /**
     * Get next working day (skip weekends)
     */
    private function getNextWorkingDay(Carbon $date): Carbon
    {
        $nextDay = $date->copy()->addDay();
        while ($nextDay->isWeekend()) {
            $nextDay->addDay();
        }
        return $nextDay;
    }

    /**
     * Get doctor's availability for a date range
     */
    public function getDoctorAvailability(int $doctorId, string $startDate, string $endDate): array
    {
        $availability = [];
        $currentDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        while ($currentDate->lte($endDate)) {
            $slots = $this->getAvailableTimeSlots($doctorId, $currentDate->toDateString());
            $availability[$currentDate->toDateString()] = [
                'date' => $currentDate->toDateString(),
                'day_name' => $currentDate->format('l'),
                'available_slots' => count($slots),
                'slots' => $slots,
            ];
            $currentDate->addDay();
        }

        return $availability;
    }

    /**
     * Auto-schedule sessions based on patient preferences and doctor availability
     */
    public function autoScheduleSessions(PatientPackage $package, array $preferences): array
    {
        $preferredDays = $preferences['preferred_days'] ?? ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        $preferredTimes = $preferences['preferred_times'] ?? ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'];
        $frequency = $preferences['frequency'] ?? 'weekly';
        $doctorId = $preferences['doctor_id'] ?? $package->assigned_doctor_id;

        $sessions = [];
        $currentDate = Carbon::parse($preferences['start_date'] ?? today()->addDay());
        $sessionsNeeded = $package->sessions_remaining;

        return DB::transaction(function () use ($package, $sessions, $currentDate, $sessionsNeeded, $preferredDays, $preferredTimes, $frequency, $doctorId) {
            for ($i = 1; $i <= $sessionsNeeded; $i++) {
                // Find next preferred day
                while (!in_array(strtolower($currentDate->format('l')), $preferredDays)) {
                    $currentDate->addDay();
                }

                // Get available slots for this date
                $availableSlots = $this->getAvailableTimeSlots($doctorId, $currentDate->toDateString());
                
                // Find preferred time slot
                $selectedSlot = null;
                foreach ($preferredTimes as $preferredTime) {
                    $slot = collect($availableSlots)->firstWhere('time', $preferredTime);
                    if ($slot) {
                        $selectedSlot = $slot;
                        break;
                    }
                }

                // If no preferred time available, take first available
                if (!$selectedSlot && !empty($availableSlots)) {
                    $selectedSlot = $availableSlots[0];
                }

                if ($selectedSlot) {
                    $session = SessionSchedule::create([
                        'patient_package_id' => $package->id,
                        'patient_id' => $package->patient_id,
                        'doctor_id' => $doctorId,
                        'scheduled_by' => auth()->id(),
                        'session_number' => $package->sessions_completed + $i,
                        'scheduled_date' => $currentDate->toDateString(),
                        'scheduled_time' => $selectedSlot['time'],
                        'duration_minutes' => 60,
                        'session_type' => 'individual',
                    ]);

                    $sessions[] = $session;
                }

                // Move to next scheduling date
                $currentDate = $this->getNextSchedulingDate($currentDate, $frequency);
            }

            return $sessions;
        });
    }
}
