<?php

namespace App\Services;

use App\Contracts\BranchRepositoryInterface;
use App\Contracts\UserRepositoryInterface;
use App\Models\Branch;
use App\Models\User;
use App\Services\CacheService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class BranchService
{
    public function __construct(
        private BranchRepositoryInterface $branchRepository,
        private UserRepositoryInterface $userRepository,
        private CacheService $cacheService
    ) {}

    public function getAllBranches()
    {
        return $this->branchRepository->getBranchesWithStats();
    }

    public function getBranchById(int $id)
    {
        return $this->branchRepository->find($id);
    }

    public function createBranch(array $data)
    {
        return DB::transaction(function () use ($data) {
            // Create branch head if provided
            $branchHeadId = null;
            if (isset($data['branch_head_data'])) {
                $branchHeadData = $data['branch_head_data'];
                $branchHeadData['password'] = Hash::make($branchHeadData['password']);
                $branchHeadData['is_active'] = true;
                
                $branchHead = $this->userRepository->create($branchHeadData);
                $branchHead->assignRole('branch_head');
                $branchHeadId = $branchHead->id;
            }

            // Create branch
            $branchData = collect($data)->except(['branch_head_data'])->toArray();
            $branchData['branch_head_id'] = $branchHeadId;
            
            return $this->branchRepository->create($branchData);
        });
    }

    public function updateBranch(int $id, array $data)
    {
        return DB::transaction(function () use ($id, $data) {
            $branch = $this->branchRepository->find($id);
            
            if (!$branch) {
                throw new \Exception('Branch not found');
            }

            // Handle branch head update
            if (isset($data['branch_head_id']) && $data['branch_head_id'] !== $branch->branch_head_id) {
                // Remove old branch head role
                if ($branch->branchHead) {
                    $branch->branchHead->removeRole('branch_head');
                }
                
                // Assign new branch head role
                if ($data['branch_head_id']) {
                    $newBranchHead = $this->userRepository->find($data['branch_head_id']);
                    if ($newBranchHead) {
                        $newBranchHead->assignRole('branch_head');
                    }
                }
            }

            return $this->branchRepository->update($id, $data);
        });
    }

    public function deleteBranch(int $id)
    {
        return DB::transaction(function () use ($id) {
            $branch = $this->branchRepository->find($id);
            
            if (!$branch) {
                throw new \Exception('Branch not found');
            }

            // Check if branch has active appointments
            if ($branch->appointments()->whereIn('status', ['scheduled', 'confirmed', 'in_progress'])->exists()) {
                throw new \Exception('Cannot delete branch with active appointments');
            }

            // Remove branch head role
            if ($branch->branchHead) {
                $branch->branchHead->removeRole('branch_head');
            }

            return $this->branchRepository->delete($id);
        });
    }

    public function getActiveBranches()
    {
        return $this->cacheService->remember(
            $this->cacheService->getActiveBranchesKey(),
            fn() => $this->branchRepository->getActive()
        );
    }

    public function assignBranchHead(int $branchId, int $userId, ?string $notes = null)
    {
        return DB::transaction(function () use ($branchId, $userId, $notes) {
            $branch = $this->branchRepository->find($branchId);
            $user = $this->userRepository->find($userId);

            if (!$branch || !$user) {
                throw new \Exception('Branch or User not found');
            }

            // Assign role first
            $user->assignRole('branch_head');

            // Use BranchHead model to manage assignment
            return \App\Models\BranchHead::assignBranchHead($branchId, $userId, $notes);
        });
    }
}