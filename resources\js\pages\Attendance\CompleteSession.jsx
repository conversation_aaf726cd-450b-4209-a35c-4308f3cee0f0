import React, { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
    Clock,
    User,
    Calendar,
    Star,
    Smile,
    FileText
} from 'lucide-react';

export default function CompleteSession({ auth, session, attendance }) {
    const { data, setData, post, processing, errors } = useForm({
        session_id: session.id,
        check_out_time: new Date().toISOString().slice(0, 16),
        session_completion: 'completed',
        participation_score: 8,
        session_summary: '',
        homework_assigned: '',
        next_session_goals: '',
        mood_before: attendance.mood_before || 'neutral',
        mood_after: 'good',
        doctor_notes: '',
        follow_up_instructions: '',
        requires_follow_up: false,
        next_recommended_date: '',
        session_fee: session.patient_package.therapy_package.session_fee || 0
    });

    const moodOptions = [
        { value: 'excellent', label: 'Excellent 😄', color: 'text-green-600' },
        { value: 'good', label: 'Good 😊', color: 'text-green-500' },
        { value: 'neutral', label: 'Neutral 😐', color: 'text-yellow-500' },
        { value: 'poor', label: 'Poor 😟', color: 'text-orange-500' },
        { value: 'very_poor', label: 'Very Poor 😢', color: 'text-red-500' }
    ];

    const completionOptions = [
        { value: 'completed', label: 'Completed', description: 'Session completed as planned' },
        { value: 'partial', label: 'Partial', description: 'Session partially completed' },
        { value: 'interrupted', label: 'Interrupted', description: 'Session was interrupted' },
        { value: 'cancelled', label: 'Cancelled', description: 'Session was cancelled during' }
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        post('/attendance/complete-session', {
            onSuccess: () => {
                router.get('/attendance/dashboard');
            }
        });
    };

    const ParticipationSlider = () => (
        <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
                <span>Poor</span>
                <span>Excellent</span>
            </div>
            <input
                type="range"
                min="1"
                max="10"
                value={data.participation_score}
                onChange={(e) => setData('participation_score', parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-center">
                <div className="flex items-center space-x-1">
                    {[...Array(data.participation_score)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                    {[...Array(10 - data.participation_score)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-gray-300" />
                    ))}
                    <span className="ml-2 text-sm font-medium text-gray-700">
                        {data.participation_score}/10
                    </span>
                </div>
            </div>
        </div>
    );

    return (
        <AppLayout>
            <Head title="Complete Session" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Header */}
                    <div className="mb-8 flex justify-between items-center">
                        <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                            Complete Session
                        </h2>
                        <button
                            onClick={() => router.get('/attendance/dashboard')}
                            className="text-gray-600 hover:text-gray-900"
                        >
                            ← Back to Dashboard
                        </button>
                    </div>
                </div>
            </div>

            <div className="py-6">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    {/* Session Info Card */}
                    <div className="bg-white shadow rounded-lg mb-6">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Session Information</h3>
                        </div>
                        <div className="px-6 py-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="flex items-center space-x-3">
                                    <User className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Patient</p>
                                        <p className="text-sm text-gray-600">{session.patient.name}</p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Calendar className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Session Time</p>
                                        <p className="text-sm text-gray-600">
                                            {new Date(session.scheduled_date).toLocaleDateString()} at {session.scheduled_time}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Clock className="h-5 w-5 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium text-gray-900">Check-in Time</p>
                                        <p className="text-sm text-gray-600">
                                            {new Date(attendance.check_in_time).toLocaleTimeString()}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Completion Form */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">Session Completion Details</h3>
                        </div>
                        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-6">
                            {/* Check-out Time */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Check-out Time
                                </label>
                                <input
                                    type="datetime-local"
                                    value={data.check_out_time}
                                    onChange={(e) => setData('check_out_time', e.target.value)}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                />
                                {errors.check_out_time && (
                                    <p className="mt-1 text-sm text-red-600">{errors.check_out_time}</p>
                                )}
                            </div>

                            {/* Session Completion Status */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Session Completion Status
                                </label>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {completionOptions.map((option) => (
                                        <button
                                            key={option.value}
                                            type="button"
                                            onClick={() => setData('session_completion', option.value)}
                                            className={`p-3 border rounded-md text-left ${
                                                data.session_completion === option.value
                                                    ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                                                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                                            }`}
                                        >
                                            <div className="font-medium">{option.label}</div>
                                            <div className="text-sm text-gray-500">{option.description}</div>
                                        </button>
                                    ))}
                                </div>
                                {errors.session_completion && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_completion}</p>
                                )}
                            </div>

                            {/* Participation Score */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-3">
                                    Patient Participation Score
                                </label>
                                <ParticipationSlider />
                                {errors.participation_score && (
                                    <p className="mt-1 text-sm text-red-600">{errors.participation_score}</p>
                                )}
                            </div>

                            {/* Mood Assessment */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Mood Before Session
                                    </label>
                                    <select
                                        value={data.mood_before}
                                        onChange={(e) => setData('mood_before', e.target.value)}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                        {moodOptions.map((mood) => (
                                            <option key={mood.value} value={mood.value}>
                                                {mood.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Mood After Session
                                    </label>
                                    <select
                                        value={data.mood_after}
                                        onChange={(e) => setData('mood_after', e.target.value)}
                                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    >
                                        {moodOptions.map((mood) => (
                                            <option key={mood.value} value={mood.value}>
                                                {mood.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            {/* Session Summary */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Summary
                                </label>
                                <textarea
                                    value={data.session_summary}
                                    onChange={(e) => setData('session_summary', e.target.value)}
                                    rows={4}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="Summarize what was covered in this session, patient's progress, key insights..."
                                />
                                {errors.session_summary && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_summary}</p>
                                )}
                            </div>

                            {/* Homework Assigned */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Homework/Exercises Assigned
                                </label>
                                <textarea
                                    value={data.homework_assigned}
                                    onChange={(e) => setData('homework_assigned', e.target.value)}
                                    rows={3}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="List any homework, exercises, or tasks assigned to the patient..."
                                />
                                {errors.homework_assigned && (
                                    <p className="mt-1 text-sm text-red-600">{errors.homework_assigned}</p>
                                )}
                            </div>

                            {/* Next Session Goals */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Goals for Next Session
                                </label>
                                <textarea
                                    value={data.next_session_goals}
                                    onChange={(e) => setData('next_session_goals', e.target.value)}
                                    rows={3}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="What should be focused on in the next session..."
                                />
                                {errors.next_session_goals && (
                                    <p className="mt-1 text-sm text-red-600">{errors.next_session_goals}</p>
                                )}
                            </div>

                            {/* Doctor Notes */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Doctor's Notes (Private)
                                </label>
                                <textarea
                                    value={data.doctor_notes}
                                    onChange={(e) => setData('doctor_notes', e.target.value)}
                                    rows={3}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    placeholder="Private notes for clinical records..."
                                />
                                {errors.doctor_notes && (
                                    <p className="mt-1 text-sm text-red-600">{errors.doctor_notes}</p>
                                )}
                            </div>

                            {/* Follow-up Section */}
                            <div className="space-y-4">
                                <div className="flex items-start">
                                    <div className="flex items-center h-5">
                                        <input
                                            type="checkbox"
                                            checked={data.requires_follow_up}
                                            onChange={(e) => setData('requires_follow_up', e.target.checked)}
                                            className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label className="font-medium text-gray-700">
                                            Requires Follow-up
                                        </label>
                                        <p className="text-gray-500">
                                            Check if this patient needs a follow-up session scheduled
                                        </p>
                                    </div>
                                </div>

                                {data.requires_follow_up && (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Recommended Next Session Date
                                            </label>
                                            <input
                                                type="date"
                                                value={data.next_recommended_date}
                                                onChange={(e) => setData('next_recommended_date', e.target.value)}
                                                min={new Date().toISOString().split('T')[0]}
                                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Follow-up Instructions
                                            </label>
                                            <textarea
                                                value={data.follow_up_instructions}
                                                onChange={(e) => setData('follow_up_instructions', e.target.value)}
                                                rows={2}
                                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                                placeholder="Special instructions for follow-up..."
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Session Fee */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Fee
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 sm:text-sm">$</span>
                                    </div>
                                    <input
                                        type="number"
                                        step="0.01"
                                        value={data.session_fee}
                                        onChange={(e) => setData('session_fee', parseFloat(e.target.value))}
                                        className="block w-full pl-7 pr-3 py-2 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                    />
                                </div>
                                {errors.session_fee && (
                                    <p className="mt-1 text-sm text-red-600">{errors.session_fee}</p>
                                )}
                            </div>

                            {/* Submit Buttons */}
                            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                                <button
                                    type="button"
                                    onClick={() => router.get('/attendance/dashboard')}
                                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    {processing ? 'Completing...' : 'Complete Session'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
