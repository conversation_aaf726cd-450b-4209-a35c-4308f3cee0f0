import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { 
    Calendar,
    Clock,
    User,
    Plus,
    ChevronLeft,
    ChevronRight,
    Columns,
    List
} from 'lucide-react';

export default function SessionSchedule({ auth, sessions, view, selectedDate, canSchedule, doctors, patients }) {
    const [currentView, setCurrentView] = useState(view);
    const [currentDate, setCurrentDate] = useState(new Date(selectedDate));

    const navigateDate = (direction) => {
        const newDate = new Date(currentDate);
        if (currentView === 'calendar') {
            newDate.setMonth(newDate.getMonth() + direction);
        } else {
            newDate.setDate(newDate.getDate() + (direction * 7));
        }
        setCurrentDate(newDate);
        router.get('/sessions', { 
            view: currentView, 
            date: newDate.toISOString().split('T')[0] 
        });
    };

    const getStatusColor = (session) => {
        if (session.attendance) {
            const status = session.attendance.attendance_status;
            return {
                present: 'bg-green-100 border-green-500 text-green-800',
                absent: 'bg-red-100 border-red-500 text-red-800',
                late: 'bg-yellow-100 border-yellow-500 text-yellow-800',
                excused: 'bg-blue-100 border-blue-500 text-blue-800'
            }[status] || 'bg-gray-100 border-gray-500 text-gray-800';
        }
        
        return {
            scheduled: 'bg-blue-50 border-blue-300 text-blue-800',
            completed: 'bg-green-50 border-green-300 text-green-800',
            cancelled: 'bg-red-50 border-red-300 text-red-800',
            no_show: 'bg-orange-50 border-orange-300 text-orange-800'
        }[session.status] || 'bg-gray-50 border-gray-300 text-gray-800';
    };

    const SessionCard = ({ session }) => (
        <div className={`p-3 rounded-lg border-l-4 ${getStatusColor(session)} hover:shadow-md transition-shadow cursor-pointer`}
             onClick={() => router.get(`/sessions/${session.id}`)}>
            <div className="flex justify-between items-start">
                <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                        <Clock className="h-4 w-4" />
                        <span className="text-sm font-medium">{session.scheduled_time}</span>
                        <span className="text-xs text-gray-500">({session.duration_minutes}m)</span>
                    </div>
                    <div className="flex items-center space-x-2 mb-1">
                        <User className="h-4 w-4" />
                        <span className="text-sm font-medium">{session.patient.name}</span>
                    </div>
                    <div className="text-xs text-gray-600">
                        Dr. {session.doctor.name}
                    </div>
                    <div className="text-xs text-gray-500">
                        {session.patient_package.therapy_package.name}
                    </div>
                    {session.location && (
                        <div className="text-xs text-gray-500 mt-1">
                            📍 {session.location}
                        </div>
                    )}
                </div>
                <div className="flex flex-col items-end space-y-1">
                    {session.attendance && (
                        <span className="text-xs px-2 py-1 rounded-full bg-white bg-opacity-50">
                            {session.attendance.attendance_status}
                        </span>
                    )}
                    {!session.attendance && session.status === 'scheduled' && (
                        <span className="text-xs px-2 py-1 rounded-full bg-white bg-opacity-50">
                            Scheduled
                        </span>
                    )}
                </div>
            </div>
        </div>
    );

    const CalendarView = () => {
        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        const startDate = new Date(startOfMonth);
        startDate.setDate(startDate.getDate() - startOfMonth.getDay());
        
        const days = [];
        const currentDay = new Date(startDate);
        
        while (currentDay <= endOfMonth || currentDay.getDay() !== 0) {
            days.push(new Date(currentDay));
            currentDay.setDate(currentDay.getDate() + 1);
        }

        const getSessionsForDate = (date) => {
            const dateStr = date.toISOString().split('T')[0];
            return sessions.filter(session => session.scheduled_date === dateStr);
        };

        return (
            <div className="bg-white shadow rounded-lg">
                <div className="grid grid-cols-7 gap-px bg-gray-200">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="bg-gray-50 py-2 text-center text-xs font-semibold text-gray-700">
                            {day}
                        </div>
                    ))}
                </div>
                <div className="grid grid-cols-7 gap-px bg-gray-200">
                    {days.map((day, index) => {
                        const isCurrentMonth = day.getMonth() === currentDate.getMonth();
                        const isToday = day.toDateString() === new Date().toDateString();
                        const daySessions = getSessionsForDate(day);
                        
                        return (
                            <div key={index} className={`bg-white p-2 h-32 ${!isCurrentMonth ? 'text-gray-400' : ''}`}>
                                <div className={`text-sm font-medium mb-1 ${isToday ? 'text-indigo-600' : ''}`}>
                                    {day.getDate()}
                                </div>
                                <div className="space-y-1">
                                    {daySessions.slice(0, 3).map(session => (
                                        <div key={session.id} 
                                             className={`text-xs p-1 rounded truncate ${getStatusColor(session)}`}
                                             onClick={() => router.get(`/sessions/${session.id}`)}>
                                            {session.scheduled_time} - {session.patient.name}
                                        </div>
                                    ))}
                                    {daySessions.length > 3 && (
                                        <div className="text-xs text-gray-500">
                                            +{daySessions.length - 3} more
                                        </div>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
            </div>
        );
    };

    const ListView = () => {
        const groupedSessions = sessions.reduce((groups, session) => {
            const date = session.scheduled_date;
            if (!groups[date]) {
                groups[date] = [];
            }
            groups[date].push(session);
            return groups;
        }, {});

        return (
            <div className="space-y-6">
                {Object.entries(groupedSessions).map(([date, dateSessions]) => (
                    <div key={date} className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">
                                {new Date(date).toLocaleDateString('en-US', { 
                                    weekday: 'long', 
                                    year: 'numeric', 
                                    month: 'long', 
                                    day: 'numeric' 
                                })}
                            </h3>
                            <p className="text-sm text-gray-500">{dateSessions.length} sessions</p>
                        </div>
                        <div className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {dateSessions
                                    .sort((a, b) => a.scheduled_time.localeCompare(b.scheduled_time))
                                    .map(session => (
                                        <SessionCard key={session.id} session={session} />
                                    ))}
                            </div>
                        </div>
                    </div>
                ))}
                {Object.keys(groupedSessions).length === 0 && (
                    <div className="bg-white shadow rounded-lg p-6 text-center">
                        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No sessions scheduled</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            No sessions found for the selected time period.
                        </p>
                        {canSchedule && (
                            <div className="mt-6">
                                <button
                                    onClick={() => router.get('/sessions/create')}
                                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Schedule Session
                                </button>
                            </div>
                        )}
                    </div>
                )}
            </div>
        );
    };

    return (
        <AppLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Session Schedule
                    </h2>
                    <div className="flex items-center space-x-4">
                        {/* View Toggle */}
                        <div className="flex rounded-md shadow-sm">
                            <button
                                onClick={() => {
                                    setCurrentView('calendar');
                                    router.get('/sessions', { view: 'calendar', date: selectedDate });
                                }}
                                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                                    currentView === 'calendar'
                                        ? 'bg-indigo-600 text-white border-indigo-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                                <Columns className="w-4 h-4" />
                            </button>
                            <button
                                onClick={() => {
                                    setCurrentView('list');
                                    router.get('/sessions', { view: 'list', date: selectedDate });
                                }}
                                className={`px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                                    currentView === 'list'
                                        ? 'bg-indigo-600 text-white border-indigo-600'
                                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                                }`}
                            >
                                <List className="w-4 h-4" />
                            </button>
                        </div>

                        {/* Navigation */}
                        <div className="flex items-center space-x-2">
                            <button
                                onClick={() => navigateDate(-1)}
                                className="p-2 text-gray-400 hover:text-gray-600"
                            >
                                <ChevronLeft className="w-5 h-5" />
                            </button>
                            <div className="text-lg font-medium text-gray-900 min-w-[200px] text-center">
                                {currentView === 'calendar' 
                                    ? currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
                                    : `Week of ${currentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
                                }
                            </div>
                            <button
                                onClick={() => navigateDate(1)}
                                className="p-2 text-gray-400 hover:text-gray-600"
                            >
                                <ChevronRight className="w-5 h-5" />
                            </button>
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2">
                            <button
                                onClick={() => router.get('/attendance/dashboard')}
                                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                            >
                                Attendance
                            </button>
                            {canSchedule && (
                                <button
                                    onClick={() => router.get('/sessions/create')}
                                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                                >
                                    <Plus className="w-4 h-4 mr-2" />
                                    Schedule Session
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            }
        >
            <Head title="Session Schedule" />

            <div className="py-6">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {currentView === 'calendar' ? <CalendarView /> : <ListView />}
                </div>
            </div>
        </AppLayout>
    );
}
