import { useModal } from '@/contexts/ModalContext';
import { useToast } from '@/contexts/ToastContext';
import { FormModal, CreateModal, EditModal, ViewModal } from '@/Components/ui/FormModal';
import { showDeleteConfirmation } from '@/utils/sweetAlert';
import { router } from '@inertiajs/react';

export const useModalActions = () => {
    const { openFormModal, openViewModal, closeModal } = useModal();
    const toast = useToast();

    // Generic CRUD operations
    const openCreateModal = (config) => {
        const {
            title,
            fields,
            endpoint,
            onSuccess,
            redirectTo,
            ...modalProps
        } = config;

        const modalId = openFormModal({
            title: `Create ${title}`,
            size: 'lg',
            children: (
                <CreateModal
                    title={title}
                    fields={fields}
                    onSubmit={async (data) => {
                        try {
                            const loadingToast = toast.loading('Creating...', `Creating new ${title.toLowerCase()}`);
                            
                            await router.post(endpoint, data, {
                                onSuccess: () => {
                                    toast.removeToast(loadingToast);
                                    toast.success('Created Successfully!', `${title} has been created successfully.`);
                                    closeModal(modalId);
                                    
                                    if (onSuccess) onSuccess();
                                    if (redirectTo) router.visit(redirectTo);
                                },
                                onError: (errors) => {
                                    toast.removeToast(loadingToast);
                                    toast.error('Creation Failed', 'Please check the form and try again.');
                                }
                            });
                        } catch (error) {
                            toast.error('Error', 'An unexpected error occurred.');
                        }
                    }}
                    onCancel={() => closeModal(modalId)}
                />
            ),
            ...modalProps
        });

        return modalId;
    };

    const openEditModal = (config) => {
        const {
            title,
            fields,
            data,
            endpoint,
            onSuccess,
            redirectTo,
            ...modalProps
        } = config;

        const modalId = openFormModal({
            title: `Edit ${title}`,
            size: 'lg',
            children: (
                <EditModal
                    title={title}
                    fields={fields}
                    data={data}
                    onSubmit={async (formData) => {
                        try {
                            const loadingToast = toast.loading('Updating...', `Updating ${title.toLowerCase()}`);
                            
                            await router.put(endpoint, formData, {
                                onSuccess: () => {
                                    toast.removeToast(loadingToast);
                                    toast.success('Updated Successfully!', `${title} has been updated successfully.`);
                                    closeModal(modalId);
                                    
                                    if (onSuccess) onSuccess();
                                    if (redirectTo) router.visit(redirectTo);
                                },
                                onError: (errors) => {
                                    toast.removeToast(loadingToast);
                                    toast.error('Update Failed', 'Please check the form and try again.');
                                }
                            });
                        } catch (error) {
                            toast.error('Error', 'An unexpected error occurred.');
                        }
                    }}
                    onCancel={() => closeModal(modalId)}
                />
            ),
            ...modalProps
        });

        return modalId;
    };

    const openViewModal = (config) => {
        const {
            title,
            fields,
            data,
            ...modalProps
        } = config;

        const modalId = openViewModal({
            title: `View ${title}`,
            size: 'xl',
            children: (
                <ViewModal
                    title={title}
                    fields={fields}
                    data={data}
                    onClose={() => closeModal(modalId)}
                />
            ),
            ...modalProps
        });

        return modalId;
    };

    const confirmDelete = async (config) => {
        const {
            title,
            itemName,
            endpoint,
            onSuccess,
            redirectTo
        } = config;

        const confirmed = await showDeleteConfirmation(itemName || title);
        
        if (confirmed) {
            try {
                const loadingToast = toast.loading('Deleting...', `Removing ${title.toLowerCase()}`);
                
                await router.delete(endpoint, {
                    onSuccess: () => {
                        toast.removeToast(loadingToast);
                        toast.success('Deleted Successfully!', `${title} has been deleted successfully.`);
                        
                        if (onSuccess) onSuccess();
                        if (redirectTo) router.visit(redirectTo);
                    },
                    onError: () => {
                        toast.removeToast(loadingToast);
                        toast.error('Deletion Failed', 'Unable to delete the item. Please try again.');
                    }
                });
            } catch (error) {
                toast.error('Error', 'An unexpected error occurred.');
            }
        }
    };

    // Specific modal actions for common entities
    const packageActions = {
        create: (onSuccess) => openCreateModal({
            title: 'Package',
            fields: [
                { name: 'name', label: 'Package Name', type: 'text', required: true },
                { name: 'description', label: 'Description', type: 'textarea', rows: 3 },
                { name: 'total_sessions', label: 'Total Sessions', type: 'number', required: true },
                { name: 'price', label: 'Price (₹)', type: 'number', required: true },
                { name: 'duration_months', label: 'Duration (Months)', type: 'number', required: true },
                { name: 'is_active', label: 'Active', type: 'checkbox' }
            ],
            endpoint: '/packages',
            onSuccess
        }),

        edit: (packageData, onSuccess) => openEditModal({
            title: 'Package',
            fields: [
                { name: 'name', label: 'Package Name', type: 'text', required: true },
                { name: 'description', label: 'Description', type: 'textarea', rows: 3 },
                { name: 'total_sessions', label: 'Total Sessions', type: 'number', required: true },
                { name: 'price', label: 'Price (₹)', type: 'number', required: true },
                { name: 'duration_months', label: 'Duration (Months)', type: 'number', required: true },
                { name: 'is_active', label: 'Active', type: 'checkbox' }
            ],
            data: packageData,
            endpoint: `/packages/${packageData.id}`,
            onSuccess
        }),

        view: (packageData) => openViewModal({
            title: 'Package Details',
            fields: [
                { name: 'name', label: 'Package Name', type: 'text' },
                { name: 'description', label: 'Description', type: 'textarea' },
                { name: 'total_sessions', label: 'Total Sessions', type: 'number' },
                { name: 'price', label: 'Price', type: 'text' },
                { name: 'duration_months', label: 'Duration (Months)', type: 'number' },
                { name: 'is_active', label: 'Status', type: 'checkbox' },
                { name: 'created_at', label: 'Created At', type: 'text' },
                { name: 'updated_at', label: 'Updated At', type: 'text' }
            ],
            data: packageData
        }),

        delete: (packageData, onSuccess) => confirmDelete({
            title: 'Package',
            itemName: packageData.name,
            endpoint: `/packages/${packageData.id}`,
            onSuccess
        })
    };

    const userActions = {
        create: (onSuccess) => openCreateModal({
            title: 'User',
            fields: [
                { name: 'name', label: 'Full Name', type: 'text', required: true },
                { name: 'email', label: 'Email', type: 'email', required: true },
                { name: 'password', label: 'Password', type: 'password', required: true },
                { name: 'role', label: 'Role', type: 'select', required: true, options: [
                    { value: 'admin', label: 'Admin' },
                    { value: 'branch_head', label: 'Branch Head' },
                    { value: 'doctor', label: 'Doctor' },
                    { value: 'patient', label: 'Patient' }
                ]},
                { name: 'phone', label: 'Phone Number', type: 'text' },
                { name: 'is_active', label: 'Active', type: 'checkbox' }
            ],
            endpoint: '/users',
            onSuccess
        }),

        edit: (userData, onSuccess) => openEditModal({
            title: 'User',
            fields: [
                { name: 'name', label: 'Full Name', type: 'text', required: true },
                { name: 'email', label: 'Email', type: 'email', required: true },
                { name: 'role', label: 'Role', type: 'select', required: true, options: [
                    { value: 'admin', label: 'Admin' },
                    { value: 'branch_head', label: 'Branch Head' },
                    { value: 'doctor', label: 'Doctor' },
                    { value: 'patient', label: 'Patient' }
                ]},
                { name: 'phone', label: 'Phone Number', type: 'text' },
                { name: 'is_active', label: 'Active', type: 'checkbox' }
            ],
            data: userData,
            endpoint: `/users/${userData.id}`,
            onSuccess
        }),

        view: (userData) => openViewModal({
            title: 'User Details',
            fields: [
                { name: 'name', label: 'Full Name', type: 'text' },
                { name: 'email', label: 'Email', type: 'text' },
                { name: 'role', label: 'Role', type: 'text' },
                { name: 'phone', label: 'Phone Number', type: 'text' },
                { name: 'is_active', label: 'Status', type: 'checkbox' },
                { name: 'created_at', label: 'Created At', type: 'text' },
                { name: 'last_login_at', label: 'Last Login', type: 'text' }
            ],
            data: userData
        }),

        delete: (userData, onSuccess) => confirmDelete({
            title: 'User',
            itemName: userData.name,
            endpoint: `/users/${userData.id}`,
            onSuccess
        })
    };

    return {
        openCreateModal,
        openEditModal,
        openViewModal,
        confirmDelete,
        packageActions,
        userActions
    };
};

export default useModalActions;
