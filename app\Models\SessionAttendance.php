<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class SessionAttendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_schedule_id',
        'patient_id',
        'doctor_id',
        'patient_package_id',
        'attendance_status',
        'check_in_time',
        'check_out_time',
        'actual_duration_minutes',
        'marked_by',
        'marked_at',
        'marking_method',
        'late_minutes',
        'late_reason',
        'absence_reason',
        'is_excused',
        'excuse_reason',
        'session_completion',
        'participation_score',
        'session_summary',
        'homework_assigned',
        'next_session_goals',
        'progress_metrics',
        'mood_before',
        'mood_after',
        'doctor_notes',
        'follow_up_instructions',
        'requires_follow_up',
        'next_recommended_date',
        'billable',
        'session_fee',
        'insurance_covered',
    ];

    protected $casts = [
        'check_in_time' => 'datetime',
        'check_out_time' => 'datetime',
        'marked_at' => 'datetime',
        'progress_metrics' => 'array',
        'next_recommended_date' => 'date',
        'is_excused' => 'boolean',
        'requires_follow_up' => 'boolean',
        'billable' => 'boolean',
        'insurance_covered' => 'boolean',
        'session_fee' => 'decimal:2',
    ];

    // Relationships
    public function sessionSchedule()
    {
        return $this->belongsTo(SessionSchedule::class);
    }

    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }

    public function patientPackage()
    {
        return $this->belongsTo(PatientPackage::class);
    }

    public function markedBy()
    {
        return $this->belongsTo(User::class, 'marked_by');
    }

    // Scopes
    public function scopePresent($query)
    {
        return $query->where('attendance_status', 'present');
    }

    public function scopeAbsent($query)
    {
        return $query->where('attendance_status', 'absent');
    }

    public function scopeLate($query)
    {
        return $query->where('attendance_status', 'late');
    }

    public function scopeForPatient($query, $patientId)
    {
        return $query->where('patient_id', $patientId);
    }

    public function scopeForDoctor($query, $doctorId)
    {
        return $query->where('doctor_id', $doctorId);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('created_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    // Accessors
    public function getAttendanceStatusLabelAttribute()
    {
        return match($this->attendance_status) {
            'present' => 'Present',
            'absent' => 'Absent',
            'late' => 'Late',
            'partial' => 'Partial',
            'excused' => 'Excused',
            default => ucfirst($this->attendance_status),
        };
    }

    public function getAttendanceStatusColorAttribute()
    {
        return match($this->attendance_status) {
            'present' => 'green',
            'absent' => 'red',
            'late' => 'yellow',
            'partial' => 'orange',
            'excused' => 'blue',
            default => 'gray',
        };
    }

    public function getSessionCompletionLabelAttribute()
    {
        return match($this->session_completion) {
            'completed' => 'Completed',
            'partial' => 'Partial',
            'interrupted' => 'Interrupted',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->session_completion),
        };
    }

    public function getMoodBeforeLabelAttribute()
    {
        return match($this->mood_before) {
            'excellent' => 'Excellent',
            'good' => 'Good',
            'neutral' => 'Neutral',
            'poor' => 'Poor',
            'very_poor' => 'Very Poor',
            default => $this->mood_before ? ucfirst($this->mood_before) : null,
        };
    }

    public function getMoodAfterLabelAttribute()
    {
        return match($this->mood_after) {
            'excellent' => 'Excellent',
            'good' => 'Good',
            'neutral' => 'Neutral',
            'poor' => 'Poor',
            'very_poor' => 'Very Poor',
            default => $this->mood_after ? ucfirst($this->mood_after) : null,
        };
    }

    public function getFormattedCheckInTimeAttribute()
    {
        return $this->check_in_time ? $this->check_in_time->format('g:i A') : null;
    }

    public function getFormattedCheckOutTimeAttribute()
    {
        return $this->check_out_time ? $this->check_out_time->format('g:i A') : null;
    }

    public function getFormattedSessionFeeAttribute()
    {
        return $this->session_fee ? '$' . number_format($this->session_fee, 2) : null;
    }

    // Methods
    public function isPresent(): bool
    {
        return $this->attendance_status === 'present';
    }

    public function isAbsent(): bool
    {
        return $this->attendance_status === 'absent';
    }

    public function isLate(): bool
    {
        return $this->attendance_status === 'late';
    }

    public function isExcused(): bool
    {
        return $this->is_excused || $this->attendance_status === 'excused';
    }

    public function calculateActualDuration(): int
    {
        if ($this->check_in_time && $this->check_out_time) {
            return $this->check_in_time->diffInMinutes($this->check_out_time);
        }
        return 0;
    }

    public function markCheckIn($time = null): void
    {
        $checkInTime = $time ? Carbon::parse($time) : now();
        $scheduledTime = $this->sessionSchedule->scheduled_date_time;
        
        $lateMinutes = $checkInTime->gt($scheduledTime) ? 
                      $checkInTime->diffInMinutes($scheduledTime) : 0;

        $this->update([
            'check_in_time' => $checkInTime,
            'late_minutes' => $lateMinutes,
            'attendance_status' => $lateMinutes > 15 ? 'late' : 'present',
        ]);
    }

    public function markCheckOut($time = null): void
    {
        $checkOutTime = $time ? Carbon::parse($time) : now();
        
        $this->update([
            'check_out_time' => $checkOutTime,
            'actual_duration_minutes' => $this->calculateActualDuration(),
        ]);
    }

    public function markAsAbsent($reason = null, $isExcused = false): void
    {
        $this->update([
            'attendance_status' => $isExcused ? 'excused' : 'absent',
            'absence_reason' => $reason,
            'is_excused' => $isExcused,
        ]);
    }

    public function updateSessionProgress($data): void
    {
        $this->update([
            'session_completion' => $data['session_completion'] ?? 'completed',
            'participation_score' => $data['participation_score'] ?? null,
            'session_summary' => $data['session_summary'] ?? null,
            'homework_assigned' => $data['homework_assigned'] ?? null,
            'next_session_goals' => $data['next_session_goals'] ?? null,
            'progress_metrics' => $data['progress_metrics'] ?? null,
            'mood_before' => $data['mood_before'] ?? null,
            'mood_after' => $data['mood_after'] ?? null,
            'doctor_notes' => $data['doctor_notes'] ?? null,
            'follow_up_instructions' => $data['follow_up_instructions'] ?? null,
            'requires_follow_up' => $data['requires_follow_up'] ?? false,
            'next_recommended_date' => $data['next_recommended_date'] ?? null,
        ]);
    }

    public function getAttendanceRate(): float
    {
        $totalSessions = $this->patient->sessionAttendances()->count();
        $presentSessions = $this->patient->sessionAttendances()->present()->count();
        
        return $totalSessions > 0 ? ($presentSessions / $totalSessions) * 100 : 0;
    }
}
