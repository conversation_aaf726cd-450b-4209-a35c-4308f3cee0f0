<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'branch_id',
        'specialization',
        'license_number',
        'qualifications',
        'experience_years',
        'phone',
        'bio',
        'consultation_fee',
        'available_days',
        'start_time',
        'end_time',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'available_days' => 'array',
            'consultation_fee' => 'decimal:2',
            'start_time' => 'datetime:H:i',
            'end_time' => 'datetime:H:i',
            'is_active' => 'boolean',
            'experience_years' => 'integer',
        ];
    }

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class, 'doctor_id', 'user_id');
    }

    public function medicalRecords()
    {
        return $this->hasMany(MedicalRecord::class, 'doctor_id', 'user_id');
    }

    // Helper methods
    public function isAvailableOnDay($day)
    {
        return in_array(strtolower($day), array_map('strtolower', $this->available_days ?? []));
    }

    public function getFullNameAttribute()
    {
        return $this->user->name;
    }
}