import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Filter, Download, RefreshCw } from 'lucide-react';
import axios from 'axios';
import { useToast } from '@/contexts/ToastContext';
import { showLoading, closeLoading, showError, showSuccess } from '@/utils/sweetAlert';

export default function PackageTracker({ initialData, availableFilters }) {
    const [data, setData] = useState(initialData?.package_tracker || { data: [], grand_total: {} });
    const [filters, setFilters] = useState({
        year: new Date().getFullYear(),
        month: '',
        course_type: '',
    });
    const [loading, setLoading] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const toast = useToast();

    const fetchData = async () => {
        setLoading(true);
        const loadingToast = toast.loading('Fetching package data...', 'Please wait while we update the tracker');

        try {
            const response = await axios.get('/dashboard/package-analytics', {
                params: filters
            });
            setData(response.data.package_tracker);
            toast.removeToast(loadingToast);
            toast.success('Data Updated', 'Package tracker has been refreshed successfully');
        } catch (error) {
            console.error('Error fetching package tracker data:', error);
            toast.removeToast(loadingToast);
            toast.error('Failed to Load Data', 'Unable to fetch package tracker data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
    };

    const applyFilters = async () => {
        await fetchData();
        setShowFilters(false);
    };

    const resetFilters = () => {
        const defaultFilters = {
            year: new Date().getFullYear(),
            month: '',
            course_type: '',
        };
        setFilters(defaultFilters);
        toast.info('Filters Reset', 'All filters have been reset to default values');
    };

    const handleExport = async () => {
        try {
            showLoading('Preparing Export', 'Generating your package tracker report...');

            // Simulate export process
            await new Promise(resolve => setTimeout(resolve, 2000));

            closeLoading();
            showSuccess('Export Complete!', 'Your package tracker report has been downloaded successfully.');
        } catch (error) {
            closeLoading();
            showError('Export Failed', 'Unable to generate the report. Please try again.');
        }
    };

    return (
        <Card className="w-full">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <div>
                        <CardTitle className="text-lg font-semibold text-green-800 bg-green-100 px-3 py-1 rounded">
                            DETAILS OF PACKAGE TRACKER - {filters.year}-{String(filters.year + 1).slice(-2)}
                        </CardTitle>
                        <CardDescription className="mt-2">
                            Track patient packages, sessions completed, and pending sessions
                        </CardDescription>
                    </div>
                    <div className="flex gap-2">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowFilters(!showFilters)}
                            className="hover:bg-blue-50 hover:border-blue-300 transition-colors"
                        >
                            <Filter className="h-4 w-4 mr-2" />
                            Filters
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={fetchData}
                            disabled={loading}
                            className="hover:bg-green-50 hover:border-green-300 transition-colors"
                        >
                            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleExport}
                            className="hover:bg-purple-50 hover:border-purple-300 transition-colors"
                        >
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {showFilters && (
                    <div className="mt-4 p-4 border rounded-lg bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <Label htmlFor="year">Year</Label>
                                <Select value={filters.year.toString()} onValueChange={(value) => handleFilterChange('year', parseInt(value))}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select year" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableFilters?.years?.map(year => (
                                            <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="month">Month</Label>
                                <Select value={filters.month} onValueChange={(value) => handleFilterChange('month', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All months" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All months</SelectItem>
                                        {Object.entries(availableFilters?.months || {}).map(([value, label]) => (
                                            <SelectItem key={value} value={value}>{label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <Label htmlFor="course_type">Course Type</Label>
                                <Select value={filters.course_type} onValueChange={(value) => handleFilterChange('course_type', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All types" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">All types</SelectItem>
                                        {availableFilters?.course_types?.map(type => (
                                            <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex items-end gap-2">
                                <Button onClick={applyFilters} className="flex-1">
                                    Apply
                                </Button>
                                <Button variant="outline" onClick={resetFilters}>
                                    Reset
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </CardHeader>

            <CardContent>
                {loading ? (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto"></div>
                            <p className="mt-4 text-gray-600 animate-pulse">Loading package tracker data...</p>
                            <div className="mt-2 flex justify-center space-x-1">
                                <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-green-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                            <thead>
                                <tr className="bg-green-700 text-white">
                                    <th className="border border-gray-300 px-4 py-2 text-left">NAME</th>
                                    <th className="border border-gray-300 px-4 py-2 text-left">REGISTRATION NO</th>
                                    <th className="border border-gray-300 px-4 py-2 text-left">COURSE TYPE</th>
                                    <th className="border border-gray-300 px-4 py-2 text-center">TOTAL SESSIONS/MONTHS</th>
                                    <th className="border border-gray-300 px-4 py-2 text-center">SESSIONS/MONTHS COMPLETED</th>
                                    <th className="border border-gray-300 px-4 py-2 text-center">PENDING SESSIONS/MONTHS</th>
                                </tr>
                            </thead>
                            <tbody>
                                {data.data?.length > 0 ? (
                                    data.data.map((item, index) => (
                                        <tr
                                            key={index}
                                            className={`
                                                ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                                                hover:bg-green-50 transition-colors duration-200
                                                animate-fade-in
                                            `}
                                            style={{ animationDelay: `${index * 50}ms` }}
                                        >
                                            <td className="border border-gray-300 px-4 py-2 font-medium text-gray-900">
                                                {item.name}
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-gray-600">
                                                {item.registration_no}
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2">
                                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {item.course_type}
                                                </span>
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-center font-medium">
                                                {item.total_sessions}
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-center">
                                                <div className="flex items-center justify-center">
                                                    <span className="text-green-600 font-medium mr-2">{item.sessions_completed}</span>
                                                    <div className="w-16 bg-gray-200 rounded-full h-2">
                                                        <div
                                                            className="bg-green-600 h-2 rounded-full transition-all duration-500"
                                                            style={{
                                                                width: `${Math.min((item.sessions_completed / item.total_sessions) * 100, 100)}%`
                                                            }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="border border-gray-300 px-4 py-2 text-center">
                                                <span className={`font-medium ${item.pending_sessions > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                                                    {item.pending_sessions}
                                                </span>
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    <tr>
                                        <td colSpan="6" className="border border-gray-300 px-4 py-12 text-center">
                                            <div className="flex flex-col items-center">
                                                <svg className="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                <p className="text-lg font-medium text-gray-900">No package data found</p>
                                                <p className="text-sm text-gray-500">Try adjusting your filters or check back later.</p>
                                            </div>
                                        </td>
                                    </tr>
                                )}
                                
                                {/* Grand Total Row */}
                                <tr className="bg-green-700 text-white font-bold">
                                    <td className="border border-gray-300 px-4 py-2" colSpan="3">Grand Total</td>
                                    <td className="border border-gray-300 px-4 py-2 text-center">{data.grand_total?.total_sessions || 0}</td>
                                    <td className="border border-gray-300 px-4 py-2 text-center">{data.grand_total?.sessions_completed || 0}</td>
                                    <td className="border border-gray-300 px-4 py-2 text-center">{data.grand_total?.pending_sessions || 0}</td>
                                </tr>
                            </tbody>
                        </table>

                        {(!data.data || data.data.length === 0) && (
                            <div className="text-center py-8 text-gray-500">
                                No package data found for the selected filters.
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
