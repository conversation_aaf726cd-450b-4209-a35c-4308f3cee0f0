<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserRequest;
use App\Services\UserService;
use App\Services\BranchService;
use App\Services\PackageAssignmentService;
use App\Models\TherapyPackage;
use Inertia\Inertia;

class UserController extends Controller
{
    public function __construct(
        private UserService $userService,
        private BranchService $branchService,
        private PackageAssignmentService $packageAssignmentService
    ) {}

    public function index()
    {
        $user = auth()->user();
        
        // Admin sees all users, Branch Head sees only users from their branch
        if ($user->hasRole('admin')) {
            $users = $this->userService->getAllUsers();
            $canManageAll = true;
        } else {
            // Branch Head sees only users from their branch
            $branchId = $user->managedBranch->id;
            $doctors = $this->userService->getDoctorsByBranch($branchId);
            $patients = $this->userService->getPatientsByBranch($branchId);
            $users = $doctors->merge($patients);
            $canManageAll = false;
        }
        
        return Inertia::render('Users/Index', [
            'users' => $users,
            'canManageAll' => $canManageAll,
            'stats' => [
                'total_users' => $users->count(),
                'active_users' => $users->where('is_active', true)->count(),
                'doctors' => $users->filter(fn($u) => $u->hasRole('doctor'))->count(),
                'patients' => $users->filter(fn($u) => $u->hasRole('patient'))->count(),
                'branch_heads' => $users->filter(fn($u) => $u->hasRole('branch_head'))->count(),
            ]
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        
        if ($user->hasRole('admin')) {
            $branches = $this->branchService->getActiveBranches();
            $roles = ['doctor', 'patient', 'branch_head'];
        } else {
            // Branch Head can only create users for their branch
            $branches = collect([$user->managedBranch]);
            $roles = ['doctor', 'patient'];
        }
        
        // Get available therapy packages for patient creation
        $therapyPackages = TherapyPackage::active()->get();

        return Inertia::render('Users/Create', [
            'branches' => $branches,
            'roles' => $roles,
            'therapyPackages' => $therapyPackages,
            'canManageAll' => $user->hasRole('admin')
        ]);
    }

    public function store(UserRequest $request)
    {
        $user = auth()->user();
        $data = $request->validated();
        
        // Branch Head can only create users for their branch
        if ($user->hasRole('branch_head')) {
            $data['branch_id'] = $user->managedBranch->id;
            
            if (!in_array($data['role'], ['doctor', 'patient'])) {
                abort(403, 'Unauthorized to create this role');
            }
        }

        try {
            $newUser = $this->userService->createUser($data, $data['role']);

            // If creating a patient and a therapy package is selected, assign it
            if ($data['role'] === 'patient' && !empty($data['therapy_package_id'])) {
                $packageData = [
                    'patient_id' => $newUser->id,
                    'therapy_package_id' => $data['therapy_package_id'],
                    'assigned_doctor_id' => $data['assigned_doctor_id'] ?? null,
                    'start_date' => $data['package_start_date'] ?? now()->toDateString(),
                    'discount_given' => $data['discount_given'] ?? 0,
                    'number_of_installments' => $data['number_of_installments'] ?? 1,
                    'payment_type' => $data['payment_type'] ?? 'cash',
                    'amount_paid' => $data['amount_paid'] ?? 0,
                    'payment_status' => $data['payment_status'] ?? 'pending',
                    'payment_notes' => $data['payment_notes'] ?? null,
                    'notes' => $data['package_notes'] ?? null,
                ];

                $this->packageAssignmentService->assignPackageToPatient($packageData);
            }

            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'User created successfully' .
                        ($data['role'] === 'patient' && !empty($data['therapy_package_id']) ? ' and therapy package assigned' : ''),
                    'user' => $newUser
                ]);
            }

            return redirect()->route('users.index')
                ->with('success', 'User created successfully' .
                    ($data['role'] === 'patient' && !empty($data['therapy_package_id']) ? ' and therapy package assigned' : ''));
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => $e->getMessage()
                ], 422);
            }
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $currentUser = auth()->user();
        $user = $this->userService->getUserById($id);
        
        if (!$user) {
            return redirect()->route('users.index')
                ->withErrors(['error' => 'User not found']);
        }

        // Branch heads can only see users from their branch
        if ($currentUser->hasRole('branch_head')) {
            $branchId = $currentUser->managedBranch->id;
            $userBranchId = null;
            
            if ($user->doctorProfile) {
                $userBranchId = $user->doctorProfile->branch_id;
            } elseif ($user->patientProfile) {
                $userBranchId = $user->patientProfile->branch_id;
            }
            
            if ($userBranchId !== $branchId) {
                abort(403, 'Unauthorized');
            }
        }

        return Inertia::render('Users/Show', [
            'user' => $user->load(['roles', 'doctorProfile.branch', 'patientProfile.branch']),
            'canEdit' => $this->canEditUser($currentUser, $user)
        ]);
    }

    public function edit($id)
    {
        $currentUser = auth()->user();
        $user = $this->userService->getUserById($id);
        
        if (!$user || !$this->canEditUser($currentUser, $user)) {
            abort(403, 'Unauthorized');
        }

        if ($currentUser->hasRole('admin')) {
            $branches = $this->branchService->getActiveBranches();
        } else {
            $branches = collect([$currentUser->managedBranch]);
        }

        return Inertia::render('Users/Edit', [
            'user' => $user->load(['roles', 'doctorProfile', 'patientProfile']),
            'branches' => $branches,
            'canManageAll' => $currentUser->hasRole('admin')
        ]);
    }

    public function update(UserRequest $request, $id)
    {
        $currentUser = auth()->user();
        $user = $this->userService->getUserById($id);
        
        if (!$user || !$this->canEditUser($currentUser, $user)) {
            abort(403, 'Unauthorized');
        }

        try {
            $updatedUser = $this->userService->updateUser($id, $request->validated());

            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'User updated successfully',
                    'user' => $updatedUser
                ]);
            }

            return redirect()->route('users.index')
                ->with('success', 'User updated successfully');
        } catch (\Exception $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'message' => $e->getMessage()
                ], 422);
            }
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function destroy($id)
    {
        $currentUser = auth()->user();
        $user = $this->userService->getUserById($id);
        
        if (!$user || !$this->canEditUser($currentUser, $user)) {
            abort(403, 'Unauthorized');
        }

        try {
            $this->userService->deleteUser($id);

            if (request()->wantsJson()) {
                return response()->json([
                    'message' => 'User deleted successfully'
                ]);
            }

            return redirect()->route('users.index')
                ->with('success', 'User deleted successfully');
        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'message' => $e->getMessage()
                ], 422);
            }
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function doctors()
    {
        $user = auth()->user();
        
        if ($user->hasRole('admin')) {
            $doctors = $this->userService->getUsersByRole('doctor');
        } else {
            $doctors = $this->userService->getDoctorsByBranch($user->managedBranch->id);
        }
        
        return Inertia::render('Users/Doctors', [
            'doctors' => $doctors,
            'canManageAll' => $user->hasRole('admin')
        ]);
    }

    public function patients()
    {
        $user = auth()->user();
        
        if ($user->hasRole('admin')) {
            $patients = $this->userService->getUsersByRole('patient');
        } else {
            $patients = $this->userService->getPatientsByBranch($user->managedBranch->id);
        }
        
        return Inertia::render('Users/Patients', [
            'patients' => $patients,
            'canManageAll' => $user->hasRole('admin')
        ]);
    }

    public function branchHeads()
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Unauthorized');
        }

        $branchHeads = $this->userService->getUsersByRole('branch_head');
        
        return Inertia::render('Users/BranchHeads', [
            'branchHeads' => $branchHeads
        ]);
    }

    public function getStats()
    {
        $user = auth()->user();
        
        if ($user->hasRole('admin')) {
            $users = $this->userService->getAllUsers();
        } else {
            $branchId = $user->managedBranch->id;
            $doctors = $this->userService->getDoctorsByBranch($branchId);
            $patients = $this->userService->getPatientsByBranch($branchId);
            $users = $doctors->merge($patients);
        }

        return response()->json([
            'total_users' => $users->count(),
            'active_users' => $users->where('is_active', true)->count(),
            'doctors' => $users->filter(fn($u) => $u->hasRole('doctor'))->count(),
            'patients' => $users->filter(fn($u) => $u->hasRole('patient'))->count(),
        ]);
    }

    private function canEditUser($currentUser, $targetUser)
    {
        if ($currentUser->hasRole('admin')) {
            return true;
        }

        if ($currentUser->hasRole('branch_head')) {
            $branchId = $currentUser->managedBranch->id;
            
            if ($targetUser->doctorProfile && $targetUser->doctorProfile->branch_id === $branchId) {
                return true;
            }
            
            if ($targetUser->patientProfile && $targetUser->patientProfile->branch_id === $branchId) {
                return true;
            }
        }

        return false;
    }
}