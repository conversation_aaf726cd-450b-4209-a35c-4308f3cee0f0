<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\AppointmentRequest;
use App\Services\AppointmentService;
use App\Services\UserService;
use Inertia\Inertia;

class AppointmentController extends Controller
{
    public function __construct(
        private AppointmentService $appointmentService,
        private UserService $userService
    ) {
        // Middleware is handled in routes
    }

    public function index()
    {
        $appointments = $this->appointmentService->getAppointmentsByPatient(auth()->id());
        
        return Inertia::render('Patient/Appointments/Index', [
            'appointments' => $appointments,
            'stats' => [
                'total' => $appointments->count(),
                'upcoming' => $appointments->filter(fn($a) => $a->appointment_date->isFuture())->count(),
                'completed' => $appointments->where('status', 'completed')->count(),
                'cancelled' => $appointments->where('status', 'cancelled')->count(),
            ]
        ]);
    }

    public function create()
    {
        $user = auth()->user();
        $branchId = $user->patientProfile->branch_id;
        $doctors = $this->userService->getDoctorsByBranch($branchId);

        return Inertia::render('Patient/Appointments/Create', [
            'doctors' => $doctors,
            'branchId' => $branchId
        ]);
    }

    public function store(AppointmentRequest $request)
    {
        try {
            $user = auth()->user();
            $data = $request->validated();
            $data['patient_id'] = $user->id;
            $data['branch_id'] = $user->patientProfile->branch_id;

            $appointment = $this->appointmentService->createAppointment($data);
            
            return redirect()->route('patient.appointments.index')
                ->with('success', 'Appointment booked successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    public function show($id)
    {
        $appointment = $this->appointmentService->getAppointmentById($id);
        
        if (!$appointment || $appointment->patient_id !== auth()->id()) {
            return redirect()->route('patient.appointments.index')
                ->withErrors(['error' => 'Appointment not found']);
        }

        return Inertia::render('Patient/Appointments/Show', [
            'appointment' => $appointment->load(['doctor.doctorProfile', 'medicalRecords'])
        ]);
    }

    public function cancel($id)
    {
        try {
            $appointment = $this->appointmentService->getAppointmentById($id);
            
            if (!$appointment || $appointment->patient_id !== auth()->id()) {
                return back()->withErrors(['error' => 'Appointment not found']);
            }

            $this->appointmentService->cancelAppointment($id, 'Cancelled by patient');
            
            return back()->with('success', 'Appointment cancelled successfully');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
}